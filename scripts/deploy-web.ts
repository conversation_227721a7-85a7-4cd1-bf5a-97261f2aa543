#!/usr/bin/env tsx

import { execSync } from "child_process";
import { existsSync } from "fs";
import { join } from "path";

async function deployWeb() {
  console.log("🚀 Fazendo deploy da aplicação web...");
  console.log("=====================================");

  try {
    // 1. Verificar se estamos no diretório correto
    console.log("📁 Verificando estrutura do projeto...");

    if (!existsSync("apps/web/package.json")) {
      console.error("❌ Não foi possível encontrar apps/web/package.json");
      process.exit(1);
    }

    // 2. Verificar se vercel.json existe em apps/web
    const vercelJsonPath = join("apps/web", "vercel.json");
    if (!existsSync(vercelJsonPath)) {
      console.error("❌ vercel.json não encontrado em apps/web");
      process.exit(1);
    }

    // 3. Fazer deploy da pasta apps/web
    console.log("🌐 Iniciando deploy da pasta apps/web...");
    execSync("vercel --prod", {
      cwd: "apps/web",
      stdio: "inherit"
    });

    console.log("\n✅ Deploy concluído com sucesso!");
    console.log("\n🔐 Contas de teste disponíveis:");
    console.log("Super Admin: <EMAIL> / admin123");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Ismael Costa: <EMAIL> / user123");

  } catch (error) {
    console.error("❌ Erro durante deploy:", error);
    process.exit(1);
  }
}

deployWeb();
