import { auth } from '@repo/auth'
import { db } from '@repo/database'

async function testApiDirectly() {
  console.log('🧪 Testando API diretamente...')

  // 1. Simular o que a API faz
  const userEmail = '<EMAIL>'
  const organizationSlug = 'techcorp-premium'

  // 2. Buscar usuário
  const user = await db.user.findUnique({
    where: { email: userEmail }
  })
  console.log('1. Usuário encontrado:', user?.name, user?.id)

  // 3. Buscar organização
  const organization = await db.organization.findUnique({
    where: { slug: organizationSlug },
    select: { id: true, name: true, slug: true }
  })
  console.log('2. Organização encontrada:', organization?.name, organization?.id)

  // 4. Verificar membro (exatamente como a API faz)
  const userMembership = await db.member.findFirst({
    where: {
      userId: user?.id,
      organizationId: organization?.id
    }
  })
  console.log('3. Membro encontrado:', userMembership ? 'SIM' : 'NÃO', userMembership?.role)

  if (!userMembership) {
    console.log('❌ Usuário não é membro da organização')
    return
  }

  // 5. Buscar vitrine (exatamente como a API faz)
  const vitrine = await db.vitrine.findFirst({
    where: {
      organizationId: organization?.id,
      status: 'PUBLISHED',
      visibility: 'PUBLIC'
    },
    include: {
      sections: {
        orderBy: { position: 'asc' },
        include: {
          courses: {
            include: {
              course: {
                select: {
                  id: true,
                  name: true,
                  logo: true,
                  community: true,
                  link: true
                }
              }
            },
            orderBy: { position: 'asc' }
          }
        }
      },
      organization: {
        select: {
          id: true,
          name: true,
          slug: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  console.log('4. Vitrine encontrada:', vitrine ? 'SIM' : 'NÃO')
  if (vitrine) {
    console.log('   Título:', vitrine.title)
    console.log('   Seções:', vitrine.sections.length)
    console.log('   Status:', vitrine.status)
    console.log('   Visibility:', vitrine.visibility)
  }

  console.log('✅ Teste concluído!')
}

testApiDirectly()
  .catch(e => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
