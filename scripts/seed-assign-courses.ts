// Set DATABASE_URL directly
process.env.DATABASE_URL = "postgresql://cakto:cakto@localhost:5432/cakto";

import { db } from '../packages/database'

const USER_ID = 'cmdbnbbsp0000yowl4vitvgdy'

async function assignAllCoursesToUser() {
  try {
    console.log('Iniciando atribuição de cursos...')
    
    // Buscar todos os cursos
    const courses = await db.courses.findMany({
      select: {
        id: true,
        name: true
      }
    })
    
    console.log(`Encontrados ${courses.length} cursos`)
    
    // Buscar cursos já atribuídos ao usuário
    const existingUserCourses = await db.userCourses.findMany({
      where: {
        userId: USER_ID
      },
      select: {
        courseId: true
      }
    })
    
    const existingCourseIds = new Set(existingUserCourses.map(uc => uc.courseId))
    
    // Filtrar cursos que ainda não foram atribuídos
    const coursesToAssign = courses.filter(course => !existingCourseIds.has(course.id))
    
    console.log(`${coursesToAssign.length} cursos serão atribuídos ao usuário`)
    
    if (coursesToAssign.length === 0) {
      console.log('Todos os cursos já estão atribuídos ao usuário')
      return
    }
    
    // Criar registros em lote
    const userCoursesData = coursesToAssign.map(course => ({
      userId: USER_ID,
      courseId: course.id,
      createdAt: new Date(),
      updatedAt: new Date()
    }))
    
    const result = await db.userCourses.createMany({
      data: userCoursesData,
      skipDuplicates: true
    })
    
    console.log(`✅ ${result.count} cursos atribuídos com sucesso ao usuário ${USER_ID}`)
    
    // Verificar total de cursos atribuídos
    const totalUserCourses = await db.userCourses.count({
      where: {
        userId: USER_ID
      }
    })
    
    console.log(`Total de cursos atribuídos ao usuário: ${totalUserCourses}`)
    
  } catch (error) {
    console.error('Erro ao atribuir cursos:', error)
    throw error
  } finally {
    await db.$disconnect()
  }
}

// Executar o script
if (require.main === module) {
  assignAllCoursesToUser()
    .then(() => {
      console.log('Script executado com sucesso')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Falha na execução do script:', error)
      process.exit(1)
    })
}

export { assignAllCoursesToUser }