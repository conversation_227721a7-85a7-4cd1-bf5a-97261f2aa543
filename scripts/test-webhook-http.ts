import { logger } from "@repo/logs";
import crypto from "crypto";

// Configurações
const WEBHOOK_SECRET = process.env.MEMBERS_WEBHOOK_SECRET || "test-secret";
const WEBHOOK_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

interface CaktoWebhookPayload {
  secret: string;
  event: string;
  data: {
    id: string;
    refId: string;
    customer: {
      name: string;
      email: string;
      phone?: string;
      docNumber?: string;
    };
    affiliate?: string;
    offer?: {
      id: string;
      name: string;
      price: number;
    };
    offer_type?: string;
    product: {
      name: string;
      id: string;
      short_id?: string;
      supportEmail?: string;
      type?: string;
      invoiceDescription?: string;
    };
    parent_order?: string;
    checkoutUrl?: string;
    status: string;
    baseAmount?: number;
    discount?: number;
    amount?: number;
    commissions?: Array<{
      user: string;
      totalAmount: number;
      type: string;
      percentage: number;
    }>;
    reason?: string;
    refund_reason?: string;
    installments?: number;
    paymentMethod?: string;
    paymentMethodName?: string;
    paidAt?: string | null;
    createdAt: string;
    card?: {
      lastDigits: string;
      holderName: string;
      brand: string;
    };
    boleto?: {
      barcode: string;
      boletoUrl: string;
      expirationDate: string;
    };
    pix?: {
      expirationDate: string;
      qrCode: string;
    };
    picpay?: {
      qrCode: string;
      paymentURL: string;
      expirationDate: string;
    };
  };
}

function generateWebhookSignature(payload: CaktoWebhookPayload): string {
  const body = JSON.stringify(payload);
  return crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(body)
    .digest('hex');
}

async function testWebhookHTTP() {
  try {
    logger.info("🚀 Testando webhook via HTTP...");

    // Simular payload de uma venda aprovada
    const webhookPayload: CaktoWebhookPayload = {
      secret: "1340098d-340d-488a-af83-f80e0eaaa773",
      event: "purchase_approved",
      data: {
        id: `webhook-http-test-${Date.now()}`,
        refId: "9vbgfmg",
        customer: {
          name: "Ismael Costa",
          email: "<EMAIL>",
          phone: "+5511999999999",
          docNumber: "123.456.789-00"
        },
        affiliate: "<EMAIL>",
        offer: {
          id: "B8BcHrY",
          name: "Special Offer",
          price: 10
        },
        offer_type: "main",
        product: {
          name: "Automação de Marketing",
          id: "ff3fdf61-e88f-43b5-982a-32d50f112414",
          short_id: "TEST456",
          supportEmail: "<EMAIL>",
          type: "unique",
          invoiceDescription: ""
        },
        parent_order: "95M26wi",
        checkoutUrl: "https://pay.cakto.com.br/EXAMPLE",
        status: "approved",
        baseAmount: 150.0,
        discount: 0.10,
        amount: 149.90,
        commissions: [{
          user: "<EMAIL>",
          totalAmount: 1.89,
          type: "producer",
          percentage: 80
        }],
        reason: "Motivo de recusa do cartão",
        refund_reason: "Motivo de reembolso",
        installments: 1,
        paymentMethod: "credit_card",
        paymentMethodName: "Cartão de Crédito",
        paidAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        card: {
          lastDigits: "4323",
          holderName: "Card Example",
          brand: "visa"
        }
      }
    };

    // Gerar assinatura HMAC
    const signature = generateWebhookSignature(webhookPayload);

    logger.info(`📦 Payload do webhook:`);
    logger.info(`   Cliente: ${webhookPayload.data.customer.name} (${webhookPayload.data.customer.email})`);
    logger.info(`   Produto: ${webhookPayload.data.product.name} (${webhookPayload.data.product.id})`);
    logger.info(`   Status: ${webhookPayload.data.status}`);
    logger.info(`   Valor: R$ ${webhookPayload.data.amount}`);

    // Preparar requisição HTTP
    const url = `${WEBHOOK_URL}/api/webhooks/cakto/purchase`;
    const body = JSON.stringify(webhookPayload);

    logger.info(`\n🌐 Enviando requisição HTTP:`);
    logger.info(`   URL: ${url}`);
    logger.info(`   Method: POST`);
    logger.info(`   Headers: X-Cakto-Signature: ${signature}`);

    // Fazer a requisição HTTP
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Cakto-Signature': signature,
      },
      body: body,
    });

    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = { raw: responseText };
    }

    logger.info(`\n📡 Resposta do servidor:`);
    logger.info(`   Status: ${response.status} ${response.statusText}`);
    logger.info(`   Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
    logger.info(`   Body: ${JSON.stringify(responseData, null, 2)}`);

    if (response.ok) {
      logger.info(`\n✅ Webhook processado com sucesso!`);

      if (responseData.success) {
        logger.info(`   ✅ Usuário criado/atualizado: ${responseData.data?.userId}`);
        logger.info(`   ✅ Acesso ao curso: ${responseData.data?.courseId}`);
        logger.info(`   ✅ Nome do curso: ${responseData.data?.courseName}`);
      }
    } else {
      logger.error(`\n❌ Erro no webhook:`);
      logger.error(`   Status: ${response.status}`);
      logger.error(`   Mensagem: ${responseData.message || responseText}`);
    }

    // Mostrar como testar com curl
    logger.info(`\n🔧 Para testar com curl:`);
    logger.info(`curl -X POST ${url} \\`);
    logger.info(`  -H "Content-Type: application/json" \\`);
    logger.info(`  -H "X-Cakto-Signature: ${signature}" \\`);
    logger.info(`  -d '${body}'`);

  } catch (error) {
    logger.error("❌ Erro ao testar webhook HTTP:", error);
  }
}

// Executar o teste
testWebhookHTTP();
