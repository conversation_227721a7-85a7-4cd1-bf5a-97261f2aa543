#!/usr/bin/env tsx

process.env.DATABASE_URL = "postgresql://cakto:cakto@localhost:5432/cakto";

import { db } from '../packages/database'

const BUNNY_VIDEO_IDS = [
  'aec1f124-28b1-49aa-8e69-07e659e0106e',
  '92ce3920-ac1a-4c27-9c85-85902b460eb4'
]

const BUNNY_LIBRARY_ID = '759'

async function updateBunnyVideoIds() {
  try {
    console.log('🔄 Iniciando atualização dos IDs de vídeo do Bunny.net...')

    const lessons = await db.lessons.findMany({
      where: {
        videoUrl: {
          not: null
        }
      },
      select: {
        id: true,
        name: true,
        videoUrl: true
      }
    })

    console.log(`📊 Encontradas ${lessons.length} aulas com vídeos`)

    let updatedCount = 0
    let skippedCount = 0

    for (let i = 0; i < lessons.length; i++) {
      const lesson = lessons[i]
      const videoId = BUNNY_VIDEO_IDS[i % BUNNY_VIDEO_IDS.length]

      const newVideoUrl = `https://iframe.mediadelivery.net/embed/${BUNNY_LIBRARY_ID}/${videoId}`

      try {
        await db.lessons.update({
          where: { id: lesson.id },
          data: { videoUrl: newVideoUrl }
        })

        console.log(`✅ Aula "${lesson.name}" atualizada: ${newVideoUrl}`)
        updatedCount++
      } catch (error) {
        console.error(`❌ Erro ao atualizar aula "${lesson.name}":`, error)
        skippedCount++
      }
    }

    console.log('\n📈 Resumo da atualização:')
    console.log(`✅ Aulas atualizadas: ${updatedCount}`)
    console.log(`❌ Aulas com erro: ${skippedCount}`)
    console.log(`📊 Total processado: ${lessons.length}`)

  } catch (error) {
    console.error('❌ Erro durante a atualização:', error)
  } finally {
    await db.$disconnect()
  }
}

updateBunnyVideoIds()
  .then(() => {
    console.log('🎉 Script concluído com sucesso!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
