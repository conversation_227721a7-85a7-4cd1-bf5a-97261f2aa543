import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function createSampleOrganizationData() {
	try {
		console.log("🔍 Buscando organizações existentes...");

		// Buscar organizações existentes
		const organizations = await db.organization.findMany({
			take: 5,
		});

		if (organizations.length === 0) {
			console.log("❌ Nenhuma organização encontrada. Crie organizações primeiro.");
			return;
		}

		console.log(`✅ Encontradas ${organizations.length} organizações`);

		// Buscar usuários
		const users = await db.user.findMany({
			take: 20,
		});

		if (users.length === 0) {
			console.log("❌ Nenhum usuário encontrado.");
			return;
		}

		console.log(`✅ Encontrados ${users.length} usuários`);

		// Criar membros para cada organização
		console.log("👥 Criando membros para as organizações...");

		for (const organization of organizations) {
			// Criar 10-50 membros por organização
			const memberCount = Math.floor(Math.random() * 41) + 10;
			const selectedUsers = users.slice(0, memberCount);

			for (let i = 0; i < selectedUsers.length; i++) {
				const user = selectedUsers[i];
				const role = i === 0 ? "owner" : i < 3 ? "admin" : "member";

				// Criar membro com data de criação variada (últimos 90 dias)
				const daysAgo = Math.floor(Math.random() * 90);
				const createdAt = new Date();
				createdAt.setDate(createdAt.getDate() - daysAgo);

				await db.member.upsert({
					where: {
						organizationId_userId: {
							organizationId: organization.id,
							userId: user.id,
						},
					},
					update: {},
					create: {
						organizationId: organization.id,
						userId: user.id,
						role: role as any,
						createdAt,
					},
				});
			}

			console.log(`  👥 Criados ${selectedUsers.length} membros para "${organization.name}"`);
		}

		// Criar cursos para cada organização
		console.log("📚 Criando cursos para as organizações...");

		const courseTemplates = [
			{ name: "Introdução ao Marketing Digital", modules: 5 },
			{ name: "Desenvolvimento Web com React", modules: 8 },
			{ name: "Design Gráfico para Iniciantes", modules: 6 },
			{ name: "Gestão de Projetos", modules: 4 },
			{ name: "Fotografia Digital", modules: 7 },
			{ name: "Copywriting Avançado", modules: 5 },
			{ name: "Análise de Dados", modules: 6 },
			{ name: "Vendas e Negociação", modules: 4 },
		];

		for (const organization of organizations) {
			// Criar 2-6 cursos por organização
			const courseCount = Math.floor(Math.random() * 5) + 2;
			const selectedTemplates = courseTemplates.slice(0, courseCount);

			for (const template of selectedTemplates) {
				const course = await db.courses.create({
					data: {
						name: template.name,
						logo: `https://images.unsplash.com/photo-${Math.random().toString(36).substring(2, 15)}?w=400&h=300&fit=crop`,
						community: `Curso completo sobre ${template.name.toLowerCase()}`,
						organizationId: organization.id,
						createdBy: users[0].id,
					},
				});

				// Criar módulos para o curso
				for (let i = 1; i <= template.modules; i++) {
					await db.modules.create({
						data: {
							name: `Módulo ${i}: ${template.name}`,
							cover: `https://images.unsplash.com/photo-${Math.random().toString(36).substring(2, 15)}?w=400&h=300&fit=crop`,
							position: i,
						},
					});
				}

				// Criar inscrições de usuários no curso
				const enrollmentCount = Math.floor(Math.random() * 20) + 5;
				const enrolledUsers = users.slice(0, enrollmentCount);

				for (const user of enrolledUsers) {
					await db.userCourses.create({
						data: {
							userId: user.id,
							courseId: course.id,
						},
					});
				}
			}

			console.log(`  📚 Criados ${selectedTemplates.length} cursos para "${organization.name}"`);
		}

		// Criar vitrines para cada organização
		console.log("🎨 Criando vitrines para as organizações...");

		const vitrineTemplates = [
			{
				title: "Vitrine de Cursos de Programação",
				description: "Uma vitrine completa com cursos de JavaScript, React, Node.js e mais tecnologias web.",
				status: "PUBLISHED" as const,
				visibility: "PUBLIC" as const,
				bannerImage: "https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop",
			},
			{
				title: "Vitrine de Marketing Digital",
				description: "Cursos especializados em marketing digital, SEO, redes sociais e análise de dados.",
				status: "PUBLISHED" as const,
				visibility: "PUBLIC" as const,
				bannerImage: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
			},
			{
				title: "Vitrine de Design Gráfico",
				description: "Aprenda design gráfico, Photoshop, Illustrator e técnicas de criação visual.",
				status: "DRAFT" as const,
				visibility: "PRIVATE" as const,
				bannerImage: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=400&fit=crop",
			},
		];

		for (const organization of organizations) {
			// Criar 1-3 vitrines por organização
			const vitrineCount = Math.floor(Math.random() * 3) + 1;
			const selectedTemplates = vitrineTemplates.slice(0, vitrineCount);

			for (const template of selectedTemplates) {
				const vitrine = await db.vitrine.create({
					data: {
						...template,
						organizationId: organization.id,
						createdBy: users[0].id,
					},
				});

				// Criar seções para a vitrine
				const sections = [
					{
						title: "Fundamentos",
						subtitle: "Conceitos básicos e introdução",
						description: "Seção introdutória com conceitos fundamentais",
						position: 1,
						isLocked: false,
						requiresPurchase: false,
						accessType: "FREE" as const,
						visibility: "PUBLIC" as const,
					},
					{
						title: "Avançado",
						subtitle: "Técnicas avançadas",
						description: "Conteúdo avançado para quem já tem experiência",
						position: 2,
						isLocked: true,
						requiresPurchase: true,
						price: 99.99,
						originalPrice: 149.99,
						accessType: "PAID" as const,
						visibility: "PUBLIC" as const,
					},
				];

				for (const sectionData of sections) {
					await db.vitrineSection.create({
						data: {
							...sectionData,
							vitrineId: vitrine.id,
						},
					});
				}

				// Criar visualizações para a vitrine
				const viewCount = Math.floor(Math.random() * 50) + 10;

				for (let i = 0; i < viewCount; i++) {
					const daysAgo = Math.floor(Math.random() * 30);
					const viewedAt = new Date();
					viewedAt.setDate(viewedAt.getDate() - daysAgo);

					await db.vitrineView.create({
						data: {
							vitrineId: vitrine.id,
							ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
							userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
							createdAt: viewedAt,
						},
					});
				}
			}

			console.log(`  🎨 Criadas ${selectedTemplates.length} vitrines para "${organization.name}"`);
		}

		console.log("✅ Script concluído com sucesso!");
		console.log(`📊 Resumo:`);
		console.log(`  - ${organizations.length} organizações processadas`);
		console.log(`  - Membros criados para cada organização`);
		console.log(`  - Cursos e módulos criados`);
		console.log(`  - Vitrines e visualizações criadas`);

	} catch (error) {
		console.error("❌ Erro ao criar dados de exemplo:", error);
	} finally {
		await db.$disconnect();
	}
}

createSampleOrganizationData();
