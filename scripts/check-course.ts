import { db } from "@repo/database";

async function checkCourse() {
  const courseId = "cmdevaoul00l3yo2frydulh24";
  const organizationSlug = "cakto-academy";

  console.log("🔍 Checking course and organization...");

  // Check if organization exists
  const organization = await db.organization.findFirst({
    where: { slug: organizationSlug },
  });

  console.log("Organization:", organization ? { id: organization.id, name: organization.name } : "NOT FOUND");

  if (!organization) {
    console.log("❌ Organization not found");
    return;
  }

  // Check if course exists
  const course = await db.courses.findFirst({
    where: { id: courseId },
    select: { id: true, name: true, organizationId: true }
  });

  console.log("Course:", course ? { id: course.id, name: course.name, organizationId: course.organizationId } : "NOT FOUND");

  if (!course) {
    console.log("❌ Course not found");
    return;
  }

  // Check if course belongs to the organization
  if (course.organizationId !== organization.id) {
    console.log("❌ Course belongs to different organization");
    console.log("Course organization ID:", course.organizationId);
    console.log("Requested organization ID:", organization.id);
    return;
  }

  // Check user membership
  const userId = "cmdevak600000yo2fhusdedli";
  const userMembership = await db.member.findFirst({
    where: {
      userId: userId,
      organizationId: organization.id,
    },
  });

  console.log("User membership:", userMembership ? { role: userMembership.role } : "NOT FOUND");

  // Check user role
  const user = await db.user.findFirst({
    where: { id: userId },
    select: { id: true, name: true, email: true, role: true }
  });

  console.log("User:", user ? { id: user.id, name: user.name, email: user.email, role: user.role } : "NOT FOUND");

  console.log("✅ All checks completed");
}

checkCourse()
  .catch(console.error)
  .finally(() => process.exit(0));
