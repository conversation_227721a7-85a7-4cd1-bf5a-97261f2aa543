#!/usr/bin/env tsx

import { execSync } from "child_process";

async function setupEnvVars() {
  console.log("🔧 Configurando variáveis de ambiente para produção...");
  console.log("=====================================================");

  // URL base para produção
  const baseUrl = "https://members-base.vercel.app";

  const envVars = [
    // BETTER AUTH (mantém o mesmo secret)
    {
      name: "BETTER_AUTH_SECRET",
      value: "112c209614f7bb08cf834f53d5e2b1721ba940a106b01a3769a4e9bd29cd723c697ce245a874c1ab3140142c3a4fb780c19bf1c5a302b1ee3e85a4950cc3e6c8",
      description: "Chave secreta para autenticação Better Auth"
    },

    // NEXT.JS (muda apenas as URLs)
    {
      name: "NEXT_PUBLIC_SITE_URL",
      value: baseUrl,
      description: "URL da aplicação em produção"
    },
    {
      name: "NEXT_PUBLIC_APP_URL",
      value: baseUrl,
      description: "URL pública da aplicação"
    },

    // MAIL SMTP (mantém tudo igual)
    {
      name: "MAIL__ADDRESS_FROM",
      value: "<EMAIL>",
      description: "Email remetente"
    },
    {
      name: "MAIL__SMTP_ENABLE",
      value: "true",
      description: "Habilitar SMTP"
    },
    {
      name: "MAIL_HOST",
      value: "smtp.mailgun.org",
      description: "Servidor SMTP"
    },
    {
      name: "MAIL_PORT",
      value: "587",
      description: "Porta SMTP"
    },
    {
      name: "MAILER_SMTP_SECURE",
      value: "false",
      description: "SMTP seguro"
    },
    {
      name: "MAIL_USER",
      value: "<EMAIL>",
      description: "Usuário SMTP"
    },
    {
      name: "MAIL_PASS",
      value: "**************************************************",
      description: "Senha SMTP"
    },
    {
      name: "MAIL__TLS_REJECT_UNAUTHORIZED",
      value: "false",
      description: "TLS reject unauthorized"
    },
    {
      name: "MAIL_USE_CUSTOM_CONFIGS",
      value: "true",
      description: "Usar configurações customizadas"
    },

    // STORAGE (mantém tudo igual)
    {
      name: "S3_ENDPOINT",
      value: "https://cakto-members-files.nyc3.digitaloceanspaces.com/",
      description: "Endpoint S3"
    },
    {
      name: "S3_REGION",
      value: "nyc3",
      description: "Região S3"
    },
    {
      name: "S3_ACCESS_KEY_ID",
      value: "DO00CZT23AZED2ZHJTL7",
      description: "Access Key ID S3"
    },
    {
      name: "S3_SECRET_ACCESS_KEY",
      value: "cqLzBLc1bmP5HjTiStDz4RGTolwn/DKWn5xW//QKh+c",
      description: "Secret Access Key S3"
    },
    {
      name: "NEXT_PUBLIC_AVATARS_BUCKET_NAME",
      value: "cakto-members-files",
      description: "Bucket de avatares"
    },
    {
      name: "S3_BUCKET_NAME",
      value: "cakto-members-files",
      description: "Nome do bucket S3"
    },

    // SSO Configuration (muda apenas SSO_REDIRECT_URI)
    {
      name: "CAKTO_SSO_CLIENT_ID",
      value: "Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN",
      description: "Client ID SSO Cakto"
    },
    {
      name: "CAKTO_SSO_CLIENT_SECRET",
      value: "KqLDBTCThIh82h2kOPRK7VBbpVyAOoQSTXSJDmdOeSvO5HeLrMX6PeuesRK6qcZGZJ8aoYcLOxy9htHzmBo57O1yPdcCXCCNlolJMt7P1NyiSs8ePOgT0NJXsjIT6O9f",
      description: "Client Secret SSO Cakto"
    },
    {
      name: "SSO_REDIRECT_URI",
      value: `${baseUrl}/auth/callback`,
      description: "URI de redirecionamento SSO"
    },
    {
      name: "CAKTO_API_URL",
      value: "https://sso.cakto.com.br",
      description: "URL da API Cakto"
    },
    {
      name: "SSO_DOMAIN",
      value: "sso.cakto.com.br",
      description: "Domínio SSO"
    },
    {
      name: "CAKTO_DOMAIN",
      value: ".cakto.com.br",
      description: "Domínio Cakto"
    },

    // Bunny.net Stream Configuration (mantém tudo igual)
    {
      name: "NEXT_PUBLIC_BUNNY_LIBRARY_ID",
      value: "467287",
      description: "ID da biblioteca Bunny"
    },
    {
      name: "NEXT_PUBLIC_BUNNY_SIGNATURE",
      value: "812c6dae-88a0-4b5a-bf32b440cae9-5072-4772",
      description: "Assinatura Bunny"
    },
    {
      name: "NEXT_PUBLIC_BUNNY_EXPIRE",
      value: "1735689600",
      description: "Expiração Bunny"
    },

    // AMBIENTE (muda apenas NODE_ENV e NEXT_PUBLIC_CAKTO_BACKEND_URL)
    {
      name: "NODE_ENV",
      value: "production",
      description: "Ambiente de produção"
    },
    {
      name: "NEXT_PUBLIC_CAKTO_BACKEND_URL",
      value: "https://api.cakto.com.br",
      description: "URL do backend Cakto"
    }
  ];

  console.log("📋 Adicionando variáveis ao projeto Vercel...");

  for (const { name, value } of envVars) {
    try {
      execSync(`vercel env add ${name} production`, {
        stdio: "pipe",
        input: value + "\n"
      });
      console.log(`   ✅ ${name}`);
    } catch (error) {
      console.log(`   ⚠️ ${name} - Adicione manualmente: ${value}`);
    }
  }

  console.log("\n✅ Variáveis de ambiente configuradas!");
  console.log("\n📋 Próximos passos:");
  console.log("1. Faça deploy: pnpm deploy:vercel");
  console.log("2. Após o deploy, atualize as URLs se necessário");

  console.log("\n🔐 Contas de teste disponíveis:");
  console.log("Super Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Ismael Costa: <EMAIL> / user123");
}

setupEnvVars();
