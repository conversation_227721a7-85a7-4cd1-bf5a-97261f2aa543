#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";
import { hash } from "bcryptjs";

const prisma = new PrismaClient();

// Available local images for cycling
const availableImages = [
  "/images/cards/card1.jpg",
  "/images/cards/card2.jpg",
  "/images/cards/card3.jpg",
  "/images/cards/card4.jpg",
  "/images/cards/card5.jpg",
  "/images/cards/card6.png",
  "/images/cards/card7.png",
  "/images/cards/card8.png",
  "/images/cards/card9.png",
  "/images/cards/card10.png"
];

// Image cycling logic
let imageIndex = 0;
const getNextImage = (): string => {
  const image = availableImages[imageIndex];
  imageIndex = (imageIndex + 1) % availableImages.length;
  return image;
};

async function main() {
  console.log("🧹 Limpando banco de dados...");

  // Clean up existing data in correct order (respecting foreign key constraints)
  await prisma.userPurchase.deleteMany();
  await prisma.vitrineSectionCourse.deleteMany();
  await prisma.vitrineSection.deleteMany();
  await prisma.vitrineView.deleteMany();
  await prisma.vitrine.deleteMany();

  await prisma.lessonCommentReplies.deleteMany();
  await prisma.lessonComments.deleteMany();
  await prisma.userWatchedLessons.deleteMany();
  await prisma.lessonFiles.deleteMany();
  await prisma.lessons.deleteMany();
  await prisma.courseModules.deleteMany();
  await prisma.modules.deleteMany();
  await prisma.courseBannerButton.deleteMany();
  await prisma.courseBanner.deleteMany();
  await prisma.userCourses.deleteMany();
  await prisma.courses.deleteMany();

  await prisma.userSystemColors.deleteMany();
  await prisma.session.deleteMany();
  await prisma.account.deleteMany();
  await prisma.user.deleteMany();

  await prisma.memberAreaSettings.deleteMany();
  await prisma.member.deleteMany();
  await prisma.invitation.deleteMany();
  await prisma.organization.deleteMany();

  console.log("✨ Criando usuários...");

  // Create users with bcryptjs
  const users = [
    {
      email: "<EMAIL>",
      name: "Administrador Principal",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "Caio Martins",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "Ana Silva",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "Ismael Costa",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "João Pedro Oliveira",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Maria Fernanda Costa",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Lucas Rodrigues",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Beatriz Almeida",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Rafael Mendes",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Camila Souza",
      role: "user",
      password: "user123"
    }
  ];

  const createdUsers = [];

  for (const userData of users) {
    try {
      // Create hash da senha usando bcryptjs
      const hashedPassword = await hash(userData.password, 12);

      // Criar usuário
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          name: userData.name,
          role: userData.role,
          emailVerified: true,
          onboardingComplete: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      // Criar conta com senha
      await prisma.account.create({
        data: {
          userId: user.id,
          providerId: "credential",
          accountId: userData.email,
          password: hashedPassword,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      createdUsers.push(user);
      console.log(`✅ Usuário criado: ${userData.email} (${userData.role})`);

    } catch (error) {
      console.log(`❌ Erro ao criar usuário ${userData.email}:`, error);
    }
  }

  const [superAdmin, admin1, admin2, ismaelUser, ...otherUsers] = createdUsers;

  console.log("🏢 Criando organizações...");

  // Create organizations
  const organizations = await Promise.all([
    prisma.organization.create({
      data: {
        name: "Cakto Academy",
        slug: "cakto-academy",
        logo: "https://ui-avatars.com/api/?name=Cakto+Academy&background=4f46e5&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "Marketing Team",
        slug: "marketing-team",
        logo: "https://ui-avatars.com/api/?name=Marketing+Team&background=dc2626&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "TechCorp Premium",
        slug: "techcorp-premium",
        logo: "https://ui-avatars.com/api/?name=TechCorp+Premium&background=dc2626&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "Afiliados Network",
        slug: "afiliados-network",
        logo: "https://ui-avatars.com/api/?name=Afiliados+Network&background=f59e0b&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "EduTech Institute",
        slug: "edutech-institute",
        logo: "https://ui-avatars.com/api/?name=EduTech+Institute&background=8b5cf6&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "InnovateLab",
        slug: "innovatelab",
        logo: "https://ui-avatars.com/api/?name=InnovateLab&background=10b981&color=fff",
        createdAt: new Date()
      }
    })
  ]);

  console.log("👥 Criando membros das organizações...");

  // Create members for each organization
  for (let i = 0; i < organizations.length; i++) {
    const organization = organizations[i];
    const userIndex = i % createdUsers.length;
    const user = createdUsers[userIndex];

    // Create owner
    await prisma.member.create({
      data: {
        organizationId: organization.id,
        userId: user.id,
        role: "owner",
        createdAt: new Date()
      }
    });

    // Create additional members for some organizations
    if (i < 3) {
      const additionalUser = createdUsers[(i + 1) % createdUsers.length];
      await prisma.member.create({
        data: {
          organizationId: organization.id,
          userId: additionalUser.id,
          role: "admin",
          createdAt: new Date()
        }
      });
    }
  }

  console.log("📚 Criando cursos...");

  // Create courses
  const courses = await Promise.all([
    prisma.courses.create({
      data: {
        name: "Marketing Digital Completo",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Tráfego Pago Masterclass",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Copywriting Persuasivo",
        logo: getNextImage(),
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Gestão de Redes Sociais",
        logo: getNextImage(),
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Email Marketing Avançado",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "SEO e Marketing de Conteúdo",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    })
  ]);

  console.log("👤 Adicionando cursos para Ismael Costa...");

  // Add courses for Ismael Costa
  await Promise.all(
    courses.map(async (course) => {
      return prisma.userCourses.create({
        data: {
          userId: ismaelUser.id,
          courseId: course.id
        }
      });
    })
  );

  console.log("🎨 Criando configurações de área de membros...");

  // Create member area settings
  await prisma.memberAreaSettings.create({
    data: {
      organizationId: organizations[0].id,
      memberAreaName: "Cakto Academy - Área de Membros",
      primaryColor: "#4f46e5",
      logoUrl: "https://ui-avatars.com/api/?name=Cakto+Academy&background=4f46e5&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      subdomain: "academy",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Meus Cursos",
          url: "/cursos",
          icon: "book",
          position: 2
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 Cakto Academy. Todos os direitos reservados.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Termos de Uso",
            url: "/termos"
          }
        ],
        socialLinks: [
          {
            platform: "instagram",
            url: "https://instagram.com/caktoacademy"
          }
        ]
      }
    }
  });

  console.log("✅ Seed concluído com sucesso!");
  console.log("📊 Resumo dos dados criados:");
  console.log(`👥 ${createdUsers.length} usuários`);
  console.log(`🏢 ${organizations.length} organizações`);
  console.log(`📚 ${courses.length} cursos`);

  console.log("\n🔐 Contas de teste:");
  console.log("Super Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Ismael Costa: <EMAIL> / user123");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
