import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function debugCourse() {
  try {
    console.log('🔍 Debugging course access...')

    const courseId = 'cmdevaoul00l3yo2frydulh24'
    const organizationSlug = 'cakto-academy'
    const userId = 'cmdevak600000yo2fhusdedli'

    // Check if course exists
    const course = await prisma.courses.findFirst({
      where: { id: courseId },
      select: { id: true, name: true, organizationId: true }
    })

    console.log('Course:', course)

    if (!course) {
      console.log('❌ Course not found')

      // List all courses
      const allCourses = await prisma.courses.findMany({
        select: { id: true, name: true, organizationId: true }
      })

      console.log('All courses:', allCourses)
      return
    }

    // Check organization
    const organization = await prisma.organization.findFirst({
      where: { slug: organizationSlug },
      select: { id: true, name: true, slug: true }
    })

    console.log('Organization:', organization)

    // Check user
    const user = await prisma.user.findFirst({
      where: { id: userId },
      select: { id: true, name: true, email: true, role: true }
    })

    console.log('User:', user)

    // Check membership
    const membership = await prisma.member.findFirst({
      where: {
        userId: userId,
        organizationId: organization?.id || ''
      },
      select: { id: true, role: true, organizationId: true }
    })

    console.log('Membership:', membership)

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugCourse()
