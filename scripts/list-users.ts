import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('👥 Available users for login:')
  console.log('================================')

  const users = await prisma.user.findMany({
    include: {
      accounts: {
        where: {
          providerId: 'credential'
        }
      }
    },
    orderBy: {
      createdAt: 'asc'
    }
  })

  users.forEach((user, index) => {
    const hasPassword = user.accounts.length > 0 && user.accounts[0].password
    const status = user.emailVerified ? '✅ Verified' : '❌ Not Verified'

    console.log(`${index + 1}. ${user.name}`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Role: ${user.role || 'user'}`)
    console.log(`   Status: ${status}`)
    console.log(`   Password: ${hasPassword ? '✅ Set' : '❌ Not Set'}`)

    if (hasPassword) {
      console.log(`   Login: Use email/password login`)
    }

    console.log('')
  })

  console.log('🔐 Login Instructions:')
  console.log('1. Go to: http://localhost:3000/auth/login')
  console.log('2. Use email and password from any user above')
  console.log('3. Make sure to use users with "✅ Set" password')

  console.log('\n📝 Recommended test accounts:')
  console.log('- <EMAIL> / admin123 (Super Admin)')
  console.log('- <EMAIL> / password123 (Regular User)')
  console.log('- <EMAIL> / test123 (Regular User)')
}

main()
  .catch(e => {
    console.error('❌ Error listing users:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
