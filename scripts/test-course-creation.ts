import { db } from '../packages/database'

const prisma = db

async function testCourseCreation() {
  try {
    console.log('🧪 Testing course creation with lessons...')

    // Find an organization
    const organization = await prisma.organization.findFirst({
      where: { slug: 'digital-members' }
    })

    if (!organization) {
      console.log('❌ Organization not found')
      return
    }

    console.log('✅ Organization found:', organization.name)

    // Create a test course
    const course = await prisma.courses.create({
      data: {
        name: 'Test Course with Lessons',
        organizationId: organization.id,
        // createdBy: null, // Leave as null for testing
      }
    })

    console.log('✅ Course created:', course.name)

    // Create a module
    const module = await prisma.modules.create({
      data: {
        name: 'Test Module',
        position: 1,
      }
    })

    console.log('✅ Module created:', module.name)

    // Link module to course
    await prisma.courseModules.create({
      data: {
        courseId: course.id,
        moduleId: module.id,
      }
    })

    console.log('✅ Module linked to course')

    // Create a lesson
    const lesson = await prisma.lessons.create({
      data: {
        name: 'Test Lesson',
        description: 'This is a test lesson',
        position: 1,
        moduleId: module.id,
        videoUrl: 'https://example.com/video.mp4',
        duration: '15min',
      }
    })

    console.log('✅ Lesson created:', lesson.name)

    // Verify the course structure
    const courseWithModules = await prisma.courses.findUnique({
      where: { id: course.id },
      include: {
        courseModules: {
          include: {
            module: {
              include: {
                lessons: true
              }
            }
          }
        }
      }
    })

    console.log('📊 Course structure:')
    console.log('- Course:', courseWithModules?.name)
    console.log('- Modules:', courseWithModules?.courseModules.length)
    courseWithModules?.courseModules.forEach(cm => {
      console.log(`  - Module: ${cm.module.name} (${cm.module.lessons.length} lessons)`)
      cm.module.lessons.forEach(lesson => {
        console.log(`    - Lesson: ${lesson.name}`)
      })
    })

    // Clean up
    await prisma.lessons.delete({ where: { id: lesson.id } })
    await prisma.courseModules.deleteMany({ where: { courseId: course.id, moduleId: module.id } })
    await prisma.modules.delete({ where: { id: module.id } })
    await prisma.courses.delete({ where: { id: course.id } })

    console.log('🧹 Test data cleaned up')

  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testCourseCreation()
