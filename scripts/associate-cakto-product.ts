import { db } from "@repo/database";
import { logger } from "@repo/logs";

interface AssociateProductParams {
  caktoProductId: string;
  courseId?: string;
  courseName?: string;
  organizationId?: string;
}

async function listOrganizations() {
  logger.info("🏢 Organizações disponíveis:");

  const organizations = await db.organization.findMany({
    select: {
      id: true,
      name: true,
      slug: true,
      _count: {
        select: {
          courses: true
        }
      }
    },
    orderBy: { name: 'asc' }
  });

  if (organizations.length === 0) {
    logger.warn("⚠️ Nenhuma organização encontrada");
    return null;
  }

  organizations.forEach((org, index) => {
    logger.info(`   ${index + 1}. ${org.name} (${org.slug}) - ${org._count.courses} cursos`);
  });

  return organizations;
}

async function listCourses(organizationId?: string) {
  const where = organizationId ? { organizationId } : {};

  logger.info("🎓 Cursos disponíveis:");

  const courses = await db.courses.findMany({
    where,
    select: {
      id: true,
      name: true,
      description: true,
      organization: {
        select: {
          name: true,
          slug: true
        }
      },
      _count: {
        select: {
          caktoProducts: true
        }
      }
    },
    orderBy: { name: 'asc' }
  });

  if (courses.length === 0) {
    logger.warn("⚠️ Nenhum curso encontrado");
    return null;
  }

  courses.forEach((course, index) => {
    logger.info(`   ${index + 1}. ${course.name} (${course.organization.name}) - ${course._count.caktoProducts} produtos associados`);
    if (course.description) {
      logger.info(`      Descrição: ${course.description}`);
    }
  });

  return courses;
}

async function createCourse(organizationId: string, courseName: string) {
  logger.info(`🎓 Criando curso: ${courseName}`);

  const course = await db.courses.create({
    data: {
      name: courseName,
      description: `Curso criado para o produto da Cakto`,
      organizationId: organizationId,
    }
  });

  logger.info(`✅ Curso criado: ${course.name} (ID: ${course.id})`);
  return course;
}

async function associateProduct(params: AssociateProductParams) {
  const { caktoProductId, courseId, courseName, organizationId } = params;

  logger.info(`🔗 Associando produto da Cakto: ${caktoProductId}`);

  // Verificar se já existe uma associação
  const existingAssociation = await db.courseProduct.findFirst({
    where: {
      caktoProductId: caktoProductId
    },
    include: {
      course: {
        include: {
          organization: true
        }
      }
    }
  });

  if (existingAssociation) {
    logger.warn(`⚠️ Produto já associado:`);
    logger.info(`   Curso: ${existingAssociation.course.name}`);
    logger.info(`   Organização: ${existingAssociation.course.organization.name}`);
    logger.info(`   ID da Associação: ${existingAssociation.id}`);

    const shouldUpdate = await logger.prompt('Deseja atualizar a associação? (y/N):', {
      type: 'text',
      default: 'N'
    });

    if (shouldUpdate.toLowerCase() !== 'y') {
      logger.info("❌ Operação cancelada");
      return existingAssociation;
    }
  }

  let targetCourse;

  if (courseId) {
    // Usar curso existente
    targetCourse = await db.courses.findUnique({
      where: { id: courseId },
      include: { organization: true }
    });

    if (!targetCourse) {
      throw new Error(`Curso não encontrado com ID: ${courseId}`);
    }
  } else if (courseName && organizationId) {
    // Criar novo curso
    targetCourse = await createCourse(organizationId, courseName);
  } else {
    throw new Error("É necessário fornecer courseId ou (courseName + organizationId)");
  }

  // Criar ou atualizar a associação
  const association = await db.courseProduct.upsert({
    where: {
      courseId_caktoProductId: {
        courseId: targetCourse.id,
        caktoProductId: caktoProductId
      }
    },
    update: {
      caktoProductName: `Produto ${caktoProductId}`
    },
    create: {
      courseId: targetCourse.id,
      caktoProductId: caktoProductId,
      caktoProductName: `Produto ${caktoProductId}`
    },
    include: {
      course: {
        include: {
          organization: true
        }
      }
    }
  });

  logger.info(`✅ Associação criada/atualizada:`);
  logger.info(`   Produto Cakto: ${caktoProductId}`);
  logger.info(`   Curso: ${association.course.name}`);
  logger.info(`   Organização: ${association.course.organization.name}`);
  logger.info(`   ID da Associação: ${association.id}`);

  return association;
}

async function main() {
  try {
    logger.info("🚀 Script de Associação de Produtos da Cakto");
    logger.info("=" .repeat(50));

    // ID do produto da Cakto (você pode alterar aqui)
    const caktoProductId = "ff3fdf61-e88f-43b5-982a-32d50f112414";

    logger.info(`📦 Produto da Cakto: ${caktoProductId}`);

    // Listar organizações
    const organizations = await listOrganizations();
    if (!organizations) {
      logger.error("❌ Nenhuma organização disponível. Crie uma organização primeiro.");
      return;
    }

    // Perguntar qual organização usar
    const orgChoice = await logger.prompt('Escolha a organização (número) ou pressione Enter para ver cursos de todas:', {
      type: 'text',
      default: ''
    });

    let selectedOrgId: string | undefined;
    if (orgChoice && !isNaN(Number(orgChoice))) {
      const orgIndex = Number(orgChoice) - 1;
      if (orgIndex >= 0 && orgIndex < organizations.length) {
        selectedOrgId = organizations[orgIndex].id;
        logger.info(`✅ Organização selecionada: ${organizations[orgIndex].name}`);
      }
    }

    // Listar cursos
    const courses = await listCourses(selectedOrgId);
    if (!courses) {
      logger.error("❌ Nenhum curso disponível. Crie um curso primeiro.");
      return;
    }

    // Perguntar se quer usar curso existente ou criar novo
    const action = await logger.prompt('Escolha uma opção:\n1. Usar curso existente\n2. Criar novo curso\nDigite 1 ou 2:', {
      type: 'text',
      default: '1'
    });

    if (action === '1') {
      // Usar curso existente
      const courseChoice = await logger.prompt('Escolha o curso (número):', {
        type: 'text',
        required: true
      });

      const courseIndex = Number(courseChoice) - 1;
      if (courseIndex >= 0 && courseIndex < courses.length) {
        const selectedCourse = courses[courseIndex];
        await associateProduct({
          caktoProductId,
          courseId: selectedCourse.id
        });
      } else {
        logger.error("❌ Opção inválida");
      }
    } else if (action === '2') {
      // Criar novo curso
      if (!selectedOrgId) {
        logger.error("❌ É necessário selecionar uma organização para criar um curso");
        return;
      }

      const courseName = await logger.prompt('Nome do novo curso:', {
        type: 'text',
        required: true,
        default: `Curso para Produto ${caktoProductId}`
      });

      await associateProduct({
        caktoProductId,
        courseName,
        organizationId: selectedOrgId
      });
    } else {
      logger.error("❌ Opção inválida");
    }

    logger.info("\n🎉 Processo concluído!");
    logger.info("💡 Agora você pode testar o webhook com:");
    logger.info(`   pnpm test:webhook:complete`);

  } catch (error) {
    logger.error("❌ Erro durante o processo:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
main();
