#!/usr/bin/env tsx

import { execSync } from "child_process";

async function setupDBManual() {
  console.log("🚀 Configuração Manual do Banco de Dados");
  console.log("==========================================");

  console.log("\n📋 Passos para configurar o banco:");
  console.log("\n1️⃣ Criar banco PostgreSQL na Vercel:");
  console.log("   - Acesse: https://vercel.com/dashboard");
  console.log("   - Vá em 'Storage' > 'Create Database' > 'PostgreSQL'");
  console.log("   - Escolha um nome para o banco");
  console.log("   - Selecione a região (recomendado: São Paulo)");
  console.log("   - Clique em 'Create'");

  console.log("\n2️⃣ Copiar DATABASE_URL:");
  console.log("   - Após criar o banco, clique em 'Connect'");
  console.log("   - Copie a string de conexão (DATABASE_URL)");

  console.log("\n3️⃣ Adicionar variável de ambiente:");
  console.log("   - No dashboard da Vercel, vá em 'Settings' > 'Environment Variables'");
  console.log("   - Adicione: DATABASE_URL (Production)");
  console.log("   - Cole a string de conexão");

  console.log("\n4️⃣ Executar migrações:");
  console.log("   - Execute: cd packages/database && pnpm push");

  console.log("\n5️⃣ Executar seed:");
  console.log("   - Execute: pnpm deploy:seed:simple");

  console.log("\n6️⃣ Fazer deploy:");
  console.log("   - Execute: pnpm deploy:vercel");

  console.log("\n🔧 Comandos úteis:");
  console.log("   - Verificar conexão: vercel storage connect postgres");
  console.log("   - Ver logs: vercel logs");
  console.log("   - Status do projeto: vercel ls");

  console.log("\n📞 Se precisar de ajuda:");
  console.log("   - Consulte: DEPLOY.md");
  console.log("   - Verifique os logs: vercel logs");
  console.log("   - Teste localmente primeiro");
}

setupDBManual();
