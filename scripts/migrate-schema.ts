#!/usr/bin/env tsx

/**
 * Script de Migração do Schema - Cakto Members → Cakto Members
 *
 * Este script migra o schema do banco de dados do projeto atual
 * para a nova estrutura do Cakto Members
 */

import { PrismaClient } from '@prisma/client'
import { config } from '../config'

const prisma = new PrismaClient();

async function migrateSchema() {
	console.log("🚀 Iniciando migração do schema...");

	try {
		// Verificar se as colunas existem antes de tentar acessá-las
		console.log("🔍 Verificando estrutura atual do banco...");

		const tableInfo = await prisma.$queryRaw<Array<{ column_name: string; table_name: string }>>`
			SELECT column_name, table_name
			FROM information_schema.columns
			WHERE table_name IN ('user', 'account', 'member')
			AND column_name IN ('mainAppUserId', 'role')
		`;

		const existingColumns = tableInfo.map(col => `${col.table_name}.${col.column_name}`);
		console.log("📋 Colunas existentes:", existingColumns);

		// 1. Verificar e corrigir dados duplicados na tabela account
		console.log("📋 Verificando duplicatas na tabela account...");
		const duplicateAccounts = await prisma.$queryRaw<Array<{ userId: string; providerId: string; count: number }>>`
			SELECT "userId", "providerId", COUNT(*) as count
			FROM account
			GROUP BY "userId", "providerId"
			HAVING COUNT(*) > 1
		`;

		if (duplicateAccounts.length > 0) {
			console.log(`⚠️  Encontradas ${duplicateAccounts.length} duplicatas na tabela account`);

			for (const duplicate of duplicateAccounts) {
				// Manter apenas o registro mais recente
				const accounts = await prisma.account.findMany({
					where: {
						userId: duplicate.userId,
						providerId: duplicate.providerId,
					},
					orderBy: {
						createdAt: 'desc',
					},
				});

				// Remover registros duplicados (manter apenas o primeiro/mais recente)
				if (accounts.length > 1) {
					const accountsToDelete = accounts.slice(1);
					await prisma.account.deleteMany({
						where: {
							id: {
								in: accountsToDelete.map(acc => acc.id),
							},
						},
					});
					console.log(`🗑️  Removidos ${accountsToDelete.length} registros duplicados para userId: ${duplicate.userId}, providerId: ${duplicate.providerId}`);
				}
			}
		}

		// 2. Verificar e corrigir dados duplicados na tabela user (mainAppUserId) - apenas se a coluna existir
		if (existingColumns.includes('user.mainAppUserId')) {
			console.log("📋 Verificando duplicatas na tabela user (mainAppUserId)...");
			const duplicateUsers = await prisma.$queryRaw<Array<{ mainAppUserId: string; count: number }>>`
				SELECT "mainAppUserId", COUNT(*) as count
				FROM "user"
				WHERE "mainAppUserId" IS NOT NULL
				GROUP BY "mainAppUserId"
				HAVING COUNT(*) > 1
			`;

			if (duplicateUsers.length > 0) {
				console.log(`⚠️  Encontradas ${duplicateUsers.length} duplicatas na tabela user (mainAppUserId)`);

				for (const duplicate of duplicateUsers) {
					// Manter apenas o registro mais recente
					const users = await prisma.user.findMany({
						where: {
							mainAppUserId: duplicate.mainAppUserId,
						},
						orderBy: {
							createdAt: 'desc',
						},
					});

					// Remover registros duplicados (manter apenas o primeiro/mais recente)
					if (users.length > 1) {
						const usersToDelete = users.slice(1);
						await prisma.user.deleteMany({
							where: {
								id: {
									in: usersToDelete.map(user => user.id),
								},
							},
						});
						console.log(`🗑️  Removidos ${usersToDelete.length} registros duplicados para mainAppUserId: ${duplicate.mainAppUserId}`);
					}
				}
			}
		} else {
			console.log("ℹ️  Coluna mainAppUserId não existe ainda - será criada pelo schema");
		}

		// 3. Migrar dados do campo role na tabela member - apenas se a coluna existir
		if (existingColumns.includes('member.role')) {
			console.log("📋 Migrando dados do campo role na tabela member...");

			// Primeiro, verificar se há membros sem role definida
			const membersWithoutRole = await prisma.$queryRaw<Array<{ id: string; role: string | null }>>`
				SELECT id, role FROM member WHERE role IS NULL
			`;

			if (membersWithoutRole.length > 0) {
				console.log(`📋 Definindo role padrão para ${membersWithoutRole.length} membros`);

				await prisma.$executeRaw`
					UPDATE member SET role = 'member' WHERE role IS NULL
				`;
			}

			// Verificar membros com roles inválidas
			const membersWithInvalidRoles = await prisma.$queryRaw<Array<{ id: string; role: string }>>`
				SELECT id, role FROM member
				WHERE role NOT IN ('owner', 'admin', 'member')
			`;

			if (membersWithInvalidRoles.length > 0) {
				console.log(`⚠️  Encontrados ${membersWithInvalidRoles.length} membros com roles inválidos`);

				// Mapear roles antigas para o novo enum
				const roleMapping: Record<string, 'owner' | 'admin' | 'member'> = {
					'OWNER': 'owner',
					'ADMIN': 'admin',
					'MEMBER': 'member',
					'USER': 'member',
					'MODERATOR': 'admin',
					'SUPER_ADMIN': 'owner',
				};

				for (const member of membersWithInvalidRoles) {
					const newRole = roleMapping[member.role] || 'member';

					await prisma.$executeRaw`
						UPDATE member SET role = ${newRole} WHERE id = ${member.id}
					`;

					console.log(`🔄 Migrado role de "${member.role}" para "${newRole}" no member ${member.id}`);
				}
			}
		} else {
			console.log("ℹ️  Coluna role não existe ainda - será criada pelo schema");
		}

		console.log("✅ Migração do schema concluída com sucesso!");
		console.log("🚀 Agora você pode executar: pnpm --filter database push");

	} catch (error) {
		console.error("❌ Erro durante a migração:", error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Executar migração se o script for chamado diretamente
if (require.main === module) {
	migrateSchema()
		.then(() => {
			console.log("🎉 Script de migração executado com sucesso!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("💥 Erro no script de migração:", error);
			process.exit(1);
		});
}

export { migrateSchema };
