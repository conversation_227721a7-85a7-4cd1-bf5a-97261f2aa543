// Set environment variables
process.env.DATABASE_URL = "postgresql://cakto:cakto@localhost:5432/cakto";

import { db } from '../packages/database'

async function checkSpecificCourse() {
  try {
    console.log('🔍 Checking specific course: cmdmlk436000eyo0ejj2vmngd')

    // Check if course exists
    const course = await db.courses.findUnique({
      where: { id: 'cmdmlk436000eyo0ejj2vmngd' },
      include: {
        organization: true,
        courseModules: {
          include: {
            module: {
              include: {
                lessons: {
                  orderBy: { position: 'asc' }
                }
              }
            }
          },
          orderBy: { module: { position: 'asc' } }
        }
      }
    })

    if (!course) {
      console.log('❌ Course not found')
      return
    }

    console.log('✅ Course found:', course.name)
    console.log('📊 Organization:', course.organization.name)
    console.log('📊 Modules count:', course.courseModules.length)

    course.courseModules.forEach((cm, index) => {
      console.log(`\n📚 Module ${index + 1}: ${cm.module.name}`)
      console.log(`   Lessons count: ${cm.module.lessons.length}`)

      if (cm.module.lessons.length > 0) {
        cm.module.lessons.forEach((lesson, lessonIndex) => {
          console.log(`   - Lesson ${lessonIndex + 1}: ${lesson.name}`)
          console.log(`     ID: ${lesson.id}`)
          console.log(`     Video URL: ${lesson.videoUrl || 'No video'}`)
        })
      } else {
        console.log('   ❌ No lessons found in this module')
      }
    })

    // Check if there are any lessons at all in the database for this course
    const allLessonsForCourse = await db.lessons.findMany({
      where: {
        module: {
          courseModules: {
            some: { courseId: 'cmdmlk436000eyo0ejj2vmngd' }
          }
        }
      },
      include: {
        module: true
      }
    })

    console.log(`\n🔍 Total lessons found for course: ${allLessonsForCourse.length}`)

    if (allLessonsForCourse.length > 0) {
      allLessonsForCourse.forEach((lesson, index) => {
        console.log(`   ${index + 1}. ${lesson.name} (Module: ${lesson.module.name})`)
      })
    }

  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await db.$disconnect()
  }
}

checkSpecificCourse()
