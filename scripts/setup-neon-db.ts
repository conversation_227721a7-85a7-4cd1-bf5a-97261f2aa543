#!/usr/bin/env tsx

import { execSync } from "child_process";

const DATABASE_URL = "postgres://neondb_owner:<EMAIL>/neondb?sslmode=require";

async function setupNeonDB() {
  console.log("🚀 Configurando banco Neon para produção...");
  console.log("=============================================");

  try {
    // 1. Verificar se o Vercel CLI está instalado
    try {
      execSync("vercel --version", { stdio: "pipe" });
      console.log("✅ Vercel CLI encontrado");
    } catch {
      console.log("📦 Instalando Vercel CLI...");
      execSync("npm install -g vercel", { stdio: "inherit" });
    }

    // 2. Fazer login no Vercel (se necessário)
    console.log("🔐 Verificando login no Vercel...");
    try {
      execSync("vercel whoami", { stdio: "pipe" });
      console.log("✅ Já logado no Vercel");
    } catch {
      console.log("🔑 Fazendo login no Vercel...");
      execSync("vercel login", { stdio: "inherit" });
    }

    // 3. Linkar projeto ao Vercel
    console.log("🔗 Linkando projeto ao Vercel...");
    try {
      execSync("vercel link --yes", { stdio: "inherit" });
      console.log("✅ Projeto linkado ao Vercel!");
    } catch (error) {
      console.log("⚠️ Erro ao linkar projeto. Execute manualmente:");
      console.log("vercel link");
      return;
    }

    // 4. Adicionar DATABASE_URL ao projeto
    console.log("🔧 Adicionando DATABASE_URL ao projeto...");
    try {
      execSync(`vercel env add DATABASE_URL production`, {
        stdio: "inherit",
        input: DATABASE_URL + "\n"
      });
      console.log("✅ DATABASE_URL adicionada com sucesso!");
    } catch (error) {
      console.log("⚠️ Erro ao adicionar DATABASE_URL automaticamente");
      console.log("🔧 Adicione manualmente no dashboard da Vercel:");
      console.log(`DATABASE_URL: ${DATABASE_URL}`);
    }

    // 5. Testar conexão com o banco (usando variável de ambiente)
    console.log("🔍 Testando conexão com o banco...");
    try {
      // Definir a variável de ambiente temporariamente
      process.env.DATABASE_URL = DATABASE_URL;

      const { PrismaClient } = require("@prisma/client");
      const prisma = new PrismaClient();

      await prisma.$connect();
      console.log("✅ Conexão com banco estabelecida!");
      await prisma.$disconnect();
    } catch (error) {
      console.log("❌ Erro ao conectar com o banco:", (error as Error).message);
      console.log("🔧 Verifique se a DATABASE_URL está correta");
      return;
    }

    // 6. Executar migrações
    console.log("🔄 Executando migrações...");
    try {
      // Definir a variável de ambiente para o comando
      const env = { ...process.env, DATABASE_URL };
      execSync("cd packages/database && pnpm push", {
        stdio: "inherit",
        env
      });
      console.log("✅ Migrações executadas com sucesso!");
    } catch (error) {
      console.log("⚠️ Erro ao executar migrações automaticamente");
      console.log("🔧 Execute manualmente:");
      console.log("DATABASE_URL='postgres://neondb_owner:<EMAIL>/neondb?sslmode=require' cd packages/database && pnpm push");
      return;
    }

    // 7. Executar seed
    console.log("🌱 Executando seed...");
    try {
      const env = { ...process.env, DATABASE_URL };
      execSync("pnpm tsx scripts/seed-simple.ts", {
        stdio: "inherit",
        env
      });
      console.log("✅ Seed executado com sucesso!");
    } catch (error) {
      console.log("⚠️ Erro ao executar seed automaticamente");
      console.log("🔧 Execute manualmente:");
      console.log("DATABASE_URL='postgres://neondb_owner:<EMAIL>/neondb?sslmode=require' pnpm deploy:seed:simple");
      return;
    }

    console.log("\n✅ Configuração do banco Neon concluída!");
    console.log("\n📋 Próximos passos:");
    console.log("1. Configure outras variáveis de ambiente no dashboard da Vercel:");
    console.log("   - BETTER_AUTH_SECRET");
    console.log("   - NEXT_PUBLIC_SITE_URL");
    console.log("   - NEXT_PUBLIC_APP_URL");
    console.log("2. Faça deploy: pnpm deploy:vercel");

    console.log("\n🔐 Contas de teste criadas:");
    console.log("Super Admin: <EMAIL> / admin123");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Ismael Costa: <EMAIL> / user123");

  } catch (error) {
    console.error("❌ Erro durante configuração:", error);
    process.exit(1);
  }
}

setupNeonDB();
