import { db } from '../packages/database'

async function testLessonCreation() {
  try {
    console.log('🧪 Testing lesson creation via API...')

    // Find the specific course and module
    const course = await db.courses.findUnique({
      where: { id: 'cmdmlk436000eyo0ejj2vmngd' },
      include: {
        courseModules: {
          include: {
            module: true
          }
        }
      }
    })

    if (!course) {
      console.log('❌ Course not found')
      return
    }

    console.log('✅ Course found:', course.name)

    const module = course.courseModules[0]?.module
    if (!module) {
      console.log('❌ No modules found in course')
      return
    }

    console.log('✅ Module found:', module.name)

    // Create a test lesson
    const lesson = await db.lessons.create({
      data: {
        name: 'Test Lesson via API',
        description: 'This is a test lesson created via API',
        position: 1,
        moduleId: module.id,
        videoUrl: 'https://example.com/test-video.mp4',
        duration: '15min',
      }
    })

    console.log('✅ Lesson created:', lesson.name)
    console.log('   ID:', lesson.id)
    console.log('   Module ID:', lesson.moduleId)

    // Verify the lesson was created
    const courseWithLessons = await db.courses.findUnique({
      where: { id: 'cmdmlk436000eyo0ejj2vmngd' },
      include: {
        courseModules: {
          include: {
            module: {
              include: {
                lessons: {
                  orderBy: { position: 'asc' }
                }
              }
            }
          }
        }
      }
    })

    console.log('\n📊 Course structure after lesson creation:')
    courseWithLessons?.courseModules.forEach((cm, index) => {
      console.log(`   Module ${index + 1}: ${cm.module.name}`)
      console.log(`   Lessons count: ${cm.module.lessons.length}`)

      cm.module.lessons.forEach((lesson, lessonIndex) => {
        console.log(`     - Lesson ${lessonIndex + 1}: ${lesson.name}`)
      })
    })

    // Clean up - delete the test lesson
    await db.lessons.delete({ where: { id: lesson.id } })
    console.log('\n🧹 Test lesson cleaned up')

  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await db.$disconnect()
  }
}

testLessonCreation()
