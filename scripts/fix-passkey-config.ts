import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🔍 Checking passkey configuration...')

  // Verificar se há usuários com passkeys
  const usersWithPasskeys = await prisma.passkey.findMany({
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true
        }
      }
    }
  })

  console.log(`Found ${usersWithPasskeys.length} passkeys in database`)

  if (usersWithPasskeys.length > 0) {
    console.log('Users with passkeys:')
    usersWithPasskeys.forEach(passkey => {
      console.log(`- ${passkey.user.email} (${passkey.user.name})`)
    })
  }

  // Verificar se há usuários sem passkeys
  const usersWithoutPasskeys = await prisma.user.findMany({
    where: {
      passkeys: {
        none: {}
      }
    },
    select: {
      id: true,
      email: true,
      name: true
    }
  })

  console.log(`\nUsers without passkeys: ${usersWithoutPasskeys.length}`)

  if (usersWithoutPasskeys.length > 0) {
    console.log('Users without passkeys:')
    usersWithoutPasskeys.forEach(user => {
      console.log(`- ${user.email} (${user.name})`)
    })
  }

  // Verificar se há contas de credenciais
  const credentialAccounts = await prisma.account.findMany({
    where: {
      providerId: 'credential'
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true
        }
      }
    }
  })

  console.log(`\nCredential accounts: ${credentialAccounts.length}`)

  if (credentialAccounts.length > 0) {
    console.log('Credential accounts:')
    credentialAccounts.forEach(account => {
      console.log(`- ${account.user.email} (${account.user.name}) - Has password: ${!!account.password}`)
    })
  }

  console.log('\n✅ Passkey configuration check completed')
  console.log('\n💡 If you\'re having login issues:')
  console.log('1. Make sure you\'re using email/password login, not passkey')
  console.log('2. Try accessing: http://localhost:3000/auth/login')
  console.log('3. Use one of the credential accounts above')
}

main()
  .catch(e => {
    console.error('❌ Error checking passkey configuration:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
