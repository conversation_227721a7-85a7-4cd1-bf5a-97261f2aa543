#!/usr/bin/env tsx

import { logger } from '@repo/logs';
import { db } from '@repo/database';
import { nanoid } from 'nanoid';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  duration?: number;
}

class IntegrationTester {
  private results: TestResult[] = [];
  private testEmail: string;
  private testProductId: string;
  private testCourseId: string;

  constructor() {
    this.testEmail = `test-${nanoid(8)}@example.com`;
    this.testProductId = 'ff3fdf61-e88f-43b5-982a-32d50f112414';
    this.testCourseId = 'clq1234abcdef';
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    logger.info(`🧪 Executando teste: ${name}`);

    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'PASS',
        message: 'Teste passou com sucesso',
        duration
      });
      logger.success(`✅ ${name} - PASS (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'FAIL',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        duration
      });
      logger.error(`❌ ${name} - FAIL (${duration}ms): ${error}`);
    }
  }

  private async testDatabaseConnection(): Promise<void> {
    await db.$queryRaw`SELECT 1`;
  }

  private async testCourseExists(): Promise<void> {
    const course = await db.courses.findUnique({
      where: { id: this.testCourseId }
    });

    if (!course) {
      throw new Error(`Curso com ID ${this.testCourseId} não encontrado`);
    }
  }

  private async testProductAssociation(): Promise<void> {
    const association = await db.courseProduct.findFirst({
      where: {
        caktoProductId: this.testProductId
      }
    });

    if (!association) {
      throw new Error(`Produto ${this.testProductId} não está associado a nenhum curso`);
    }
  }

  private async testUserCreation(): Promise<void> {
    // Verificar se o usuário não existe
    const existingUser = await db.user.findUnique({
      where: { email: this.testEmail }
    });

    if (existingUser) {
      throw new Error(`Usuário ${this.testEmail} já existe`);
    }
  }

  private async testWebhookPayload(): Promise<void> {
    const payload = {
      id: nanoid(),
      customer: {
        name: 'Usuário de Teste',
        email: this.testEmail,
        phone: '11999999999',
        docNumber: '12345678909',
      },
      product: {
        name: 'Produto Teste',
        id: this.testProductId,
        short_id: 'TEST123',
      },
      status: 'approved',
      amount: 99.90,
      paymentMethod: 'credit_card',
      paidAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
    };

    // Validar payload com Zod (simulação)
    if (!payload.customer.email || !payload.product.id) {
      throw new Error('Payload inválido');
    }
  }

  private async testEmailTemplates(): Promise<void> {
    const templates = ['userCreated', 'newUser'];

    for (const template of templates) {
      if (!['userCreated', 'newUser'].includes(template)) {
        throw new Error(`Template ${template} não encontrado`);
      }
    }
  }

  private async testEnvironmentVariables(): Promise<void> {
    const requiredVars = [
      'CAKTO_WEBHOOK_API_KEY',
      'CAKTO_API_URL',
      'NEXT_PUBLIC_APP_URL'
    ];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        throw new Error(`Variável de ambiente ${varName} não configurada`);
      }
    }
  }

  private async testAPIEndpoints(): Promise<void> {
    const endpoints = [
      '/api/webhooks/cakto/purchase',
      '/api/admin/cakto-products',
      '/api/admin/cakto-products/associate'
    ];

    for (const endpoint of endpoints) {
      // Simular verificação de endpoint
      if (!endpoint.startsWith('/api/')) {
        throw new Error(`Endpoint inválido: ${endpoint}`);
      }
    }
  }

  private async cleanup(): Promise<void> {
    try {
      // Limpar usuário de teste se existir
      await db.user.deleteMany({
        where: { email: this.testEmail }
      });

      // Limpar acessos de teste
      await db.userCourses.deleteMany({
        where: {
          user: { email: this.testEmail }
        }
      });

      logger.info('🧹 Limpeza concluída');
    } catch (error) {
      logger.warn('⚠️ Erro na limpeza:', error);
    }
  }

  private printResults(): void {
    logger.info('\n📊 RESULTADOS DOS TESTES');
    logger.info('='.repeat(50));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⏭️';
      const duration = result.duration ? ` (${result.duration}ms)` : '';
      logger.info(`${icon} ${result.name} - ${result.status}${duration}`);

      if (result.status === 'FAIL') {
        logger.error(`   Erro: ${result.message}`);
      }
    });

    logger.info('='.repeat(50));
    logger.info(`Total: ${this.results.length} | Passou: ${passed} | Falhou: ${failed} | Pulou: ${skipped}`);

    if (failed > 0) {
      logger.error('❌ Alguns testes falharam. Verifique os erros acima.');
      process.exit(1);
    } else {
      logger.success('🎉 Todos os testes passaram!');
    }
  }

  async runAllTests(): Promise<void> {
    logger.info('🚀 Iniciando testes de integração Cakto-Members');
    logger.info(`📧 Email de teste: ${this.testEmail}`);
    logger.info(`🆔 Produto de teste: ${this.testProductId}`);
    logger.info(`📚 Curso de teste: ${this.testCourseId}`);
    logger.info('');

    // Testes de infraestrutura
    await this.runTest('Conexão com banco de dados', () => this.testDatabaseConnection());
    await this.runTest('Variáveis de ambiente', () => this.testEnvironmentVariables());
    await this.runTest('Templates de email', () => this.testEmailTemplates());
    await this.runTest('Endpoints da API', () => this.testAPIEndpoints());

    // Testes de dados
    await this.runTest('Existência do curso', () => this.testCourseExists());
    await this.runTest('Associação produto-curso', () => this.testProductAssociation());
    await this.runTest('Criação de usuário', () => this.testUserCreation());

    // Testes de funcionalidade
    await this.runTest('Validação de payload do webhook', () => this.testWebhookPayload());

    // Limpeza
    await this.cleanup();

    // Resultados
    this.printResults();
  }
}

async function main() {
  try {
    const tester = new IntegrationTester();
    await tester.runAllTests();
  } catch (error) {
    logger.error('❌ Erro fatal nos testes:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main().catch((error) => {
    logger.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}
