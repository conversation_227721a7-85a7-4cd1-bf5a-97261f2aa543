import { auth } from '@repo/auth'

async function testSession() {
  console.log('🔍 Testando sessão do Better Auth...')

  try {
    // Simular headers com cookie
    const headers = new Headers()
    headers.set('cookie', 'better-auth.session_token=hjwuRkcXzAXxOZE6VibOMroqN0lesZhO.JI2lXAsUTzTSD95kPqiMqvnIIpx0CYgeQWsxnwokAOI%3D')

    console.log('1. Headers criados:', headers.get('cookie'))

    // Tentar extrair a sessão
    const session = await auth.api.getSession({
      headers
    })

    console.log('2. Sessão extraída:', session ? 'SIM' : 'NÃO')
    console.log('3. Usuário:', session?.user?.email)
    console.log('4. Token:', session?.session?.token)

  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

testSession()
  .catch(e => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
