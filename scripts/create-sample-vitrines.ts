import { db } from "@repo/database";

async function createSampleVitrines() {
	try {
		console.log("🔍 Buscando organizações...");

		// Buscar organizações existentes
		const organizations = await db.organization.findMany({
			take: 3,
		});

		if (organizations.length === 0) {
			console.log("❌ Nenhuma organização encontrada. Crie organizações primeiro.");
			return;
		}

		console.log(`✅ Encontradas ${organizations.length} organizações`);

		// Buscar usuários admin
		const users = await db.user.findMany({
			where: {
				role: "admin",
			},
			take: 1,
		});

		if (users.length === 0) {
			console.log("❌ Nenhum usuário admin encontrado.");
			return;
		}

		const adminUser = users[0];
		console.log(`✅ Usuário admin encontrado: ${adminUser.name}`);

		// Criar vitrines de exemplo
		const sampleVitrines = [
			{
				title: "Vitrine de Cursos de Programação",
				description: "Uma vitrine completa com cursos de JavaScript, React, Node.js e mais tecnologias web.",
				status: "PUBLISHED" as const,
				visibility: "PUBLIC" as const,
				bannerImage: "https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop",
				isDefault: true,
				organizationId: organizations[0].id,
				createdBy: adminUser.id,
			},
			{
				title: "Vitrine de Marketing Digital",
				description: "Cursos especializados em marketing digital, SEO, redes sociais e análise de dados.",
				status: "PUBLISHED" as const,
				visibility: "PUBLIC" as const,
				bannerImage: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
				isDefault: false,
				organizationId: organizations[0].id,
				createdBy: adminUser.id,
			},
			{
				title: "Vitrine de Design Gráfico",
				description: "Aprenda design gráfico, Photoshop, Illustrator e técnicas de criação visual.",
				status: "DRAFT" as const,
				visibility: "PRIVATE" as const,
				bannerImage: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=400&fit=crop",
				isDefault: false,
				organizationId: organizations[0].id,
				createdBy: adminUser.id,
			},
			{
				title: "Vitrine de Negócios",
				description: "Cursos sobre empreendedorismo, gestão de negócios e estratégias empresariais.",
				status: "PUBLISHED" as const,
				visibility: "PUBLIC" as const,
				bannerImage: "https://images.unsplash.com/photo-1552664730-d307ca884978?w=800&h=400&fit=crop",
				isDefault: false,
				organizationId: organizations[1]?.id || organizations[0].id,
				createdBy: adminUser.id,
			},
			{
				title: "Vitrine de Fotografia",
				description: "Técnicas de fotografia, edição de imagens e composição visual.",
				status: "ARCHIVED" as const,
				visibility: "PRIVATE" as const,
				bannerImage: "https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=800&h=400&fit=crop",
				isDefault: false,
				organizationId: organizations[1]?.id || organizations[0].id,
				createdBy: adminUser.id,
			},
		];

		console.log("📝 Criando vitrines de exemplo...");

		for (const vitrineData of sampleVitrines) {
			const vitrine = await db.vitrine.create({
				data: vitrineData,
			});

			console.log(`✅ Vitrine criada: ${vitrine.title} (${vitrine.status})`);

			// Criar algumas seções para cada vitrine
			const sections = [
				{
					title: "Fundamentos",
					subtitle: "Conceitos básicos e introdução",
					description: "Seção introdutória com conceitos fundamentais",
					position: 1,
					isLocked: false,
					requiresPurchase: false,
					accessType: "FREE" as const,
					visibility: "PUBLIC" as const,
				},
				{
					title: "Avançado",
					subtitle: "Técnicas avançadas",
					description: "Conteúdo avançado para quem já tem experiência",
					position: 2,
					isLocked: true,
					requiresPurchase: true,
					price: 99.99,
					originalPrice: 149.99,
					accessType: "PAID" as const,
					visibility: "PUBLIC" as const,
				},
			];

			for (const sectionData of sections) {
				await db.vitrineSection.create({
					data: {
						...sectionData,
						vitrineId: vitrine.id,
					},
				});
			}

			console.log(`  📚 Criadas ${sections.length} seções para a vitrine`);
		}

		// Criar algumas visualizações de exemplo
		console.log("👁️ Criando visualizações de exemplo...");

		const vitrines = await db.vitrine.findMany({
			where: {
				status: "PUBLISHED",
			},
		});

		for (const vitrine of vitrines) {
			// Criar 5-15 visualizações por vitrine
			const viewCount = Math.floor(Math.random() * 11) + 5;

			for (let i = 0; i < viewCount; i++) {
				await db.vitrineView.create({
					data: {
						vitrineId: vitrine.id,
						ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
						userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
					},
				});
			}

			console.log(`  👁️ Criadas ${viewCount} visualizações para "${vitrine.title}"`);
		}

		console.log("✅ Script concluído com sucesso!");
		console.log(`📊 Resumo:`);
		console.log(`  - ${sampleVitrines.length} vitrines criadas`);
		console.log(`  - ${sampleVitrines.length * 2} seções criadas`);
		console.log(`  - Visualizações de exemplo adicionadas`);

	} catch (error) {
		console.error("❌ Erro ao criar vitrines de exemplo:", error);
	} finally {
		await db.$disconnect();
	}
}

createSampleVitrines();
