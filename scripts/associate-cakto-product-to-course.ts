import { db } from '../packages/database';
import { logger } from '@repo/logs';

async function main() {
  try {
    logger.info('🔄 Iniciando associação de produto Cakto a curso no Members-base');

    // Solicitar ID do curso
    const courseId = await logger.prompt('ID do curso no Members-base:', {
      required: true,
      placeholder: 'clq1234abcdef',
      type: 'text',
    });

    // Verificar se o curso existe
    const course = await db.courses.findUnique({
      where: { id: courseId },
      select: { id: true, name: true }
    });

    if (!course) {
      logger.error(`❌ Curso com ID ${courseId} não encontrado!`);
      return;
    }

    logger.success(`✅ Curso encontrado: ${course.name}`);

    // Solicitar ID do produto na Cakto
    const caktoProductId = await logger.prompt('ID do produto na Cakto:', {
      required: true,
      placeholder: 'ff3fdf61-e88f-43b5-982a-32d50f112414',
      type: 'text',
    });

    // Solicitar nome do produto (opcional)
    const caktoProductName = await logger.prompt('Nome do produto na Cakto (opcional):', {
      required: false,
      placeholder: 'Curso de Marketing Digital',
      type: 'text',
    });

    // Verificar se já existe associação
    const existingAssociation = await db.courseProduct.findFirst({
      where: {
        courseId,
        caktoProductId
      }
    });

    if (existingAssociation) {
      logger.warn(`⚠️ Já existe uma associação entre este curso e produto!`);

      const shouldUpdate = await logger.prompt('Deseja atualizar o nome do produto?', {
        required: true,
        type: 'confirm',
        default: false,
      });

      if (shouldUpdate && caktoProductName) {
        await db.courseProduct.update({
          where: { id: existingAssociation.id },
          data: { caktoProductName }
        });
        logger.success(`✅ Nome do produto atualizado com sucesso!`);
      } else {
        logger.info(`ℹ️ Nenhuma alteração realizada.`);
      }

      return;
    }

    // Criar a associação
    await db.courseProduct.create({
      data: {
        courseId,
        caktoProductId,
        caktoProductName: caktoProductName || null
      }
    });

    logger.success(`✅ Associação criada com sucesso!`);
    logger.info(`🔄 Curso: ${course.name}`);
    logger.info(`🔄 Produto Cakto ID: ${caktoProductId}`);
    if (caktoProductName) {
      logger.info(`🔄 Produto Cakto Nome: ${caktoProductName}`);
    }

  } catch (error) {
    logger.error('❌ Erro ao associar produto a curso:', error);
  } finally {
    await db.$disconnect();
  }
}

main().catch((error) => {
  logger.error('❌ Erro fatal:', error);
  process.exit(1);
});
