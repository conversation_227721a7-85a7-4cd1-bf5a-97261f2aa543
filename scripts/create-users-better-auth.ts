import { auth } from "@repo/auth";
import { createUser, createUserAccount, getUserByEmail } from "@repo/database";
import { logger } from "@repo/logs";

async function main() {
  console.log("🔐 Criando usuários com Better Auth...");

  const users = [
    {
      email: "<EMAIL>",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON><PERSON><PERSON>",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "admin",
      password: "admin123"
    },
    {
      email: "ismael<PERSON><PERSON><PERSON>@gmail.com",
      name: "<PERSON><PERSON><PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "luca<PERSON>.rodrig<PERSON>@outlook.com",
      name: "<PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Beatriz <PERSON>meida",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON> Mendes",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Camila Souza",
      role: "user",
      password: "user123"
    }
  ];

  const authContext = await auth.$context;

  for (const userData of users) {
    try {
      // Verificar se o usuário já existe
      const existingUser = await getUserByEmail(userData.email);
      if (existingUser) {
        console.log(`⚠️ Usuário já existe: ${userData.email}`);
        continue;
      }

      // Criar hash da senha usando Better Auth
      const hashedPassword = await authContext.password.hash(userData.password);

      // Criar usuário
      const user = await createUser({
        email: userData.email,
        name: userData.name,
        role: userData.role as "user" | "admin",
        emailVerified: true,
        onboardingComplete: true,
      });

      if (!user) {
        console.log(`❌ Falha ao criar usuário: ${userData.email}`);
        continue;
      }

      // Criar conta com senha
      await createUserAccount({
        userId: user.id,
        providerId: "credential",
        accountId: userData.email,
        hashedPassword,
      });

      console.log(`✅ Usuário criado: ${userData.email} (${userData.role})`);

    } catch (error) {
      console.log(`❌ Erro ao criar usuário ${userData.email}:`, error);
    }
  }

  console.log("\n🎉 Processo concluído!");
  console.log("\n🔐 Contas criadas:");
  console.log("Super Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Ismael Costa: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
}

main()
  .catch(e => {
    console.error('❌ Erro:', e);
    process.exit(1);
  });
