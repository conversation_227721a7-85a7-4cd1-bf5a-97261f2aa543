import { createUser, createUserAccount, getUserByEmail, db } from "@repo/database";
import { sendEmail } from "@repo/mail";
import { logger } from "@repo/logs";
import { nanoid } from "nanoid";

async function createUserAccount() {
  try {
    const email = "<EMAIL>";
    const name = "Hello User";

    logger.info(`👤 Verificando/criando usuário para ${email}...`);

    // Verificar se o usuário já existe
    let user = await getUserByEmail(email);

    if (!user) {
      logger.info(`🆕 Criando novo usuário para ${email}`);

      // Gerar senha aleatória
      const password = nanoid(12);

      // Criar o usuário
      user = await createUser({
        email: email,
        name: name,
        role: "user",
        emailVerified: true,
        onboardingComplete: false,
      });

      if (!user) {
        throw new Error(`Failed to create user for ${email}`);
      }

      // Criar conta com senha
      const authContext = await import("@repo/auth").then(m => m.auth.$context);
      const hashedPassword = await authContext.password.hash(password);

      await createUserAccount({
        userId: user.id,
        providerId: "credential",
        accountId: user.email,
        hashedPassword,
      });

      logger.info(`✅ Usuário criado: ${user.name} (${user.email})`);

      // Enviar email de boas-vindas
      logger.info(`📧 Enviando email de boas-vindas para ${user.email}...`);

      const emailResult = await sendEmail({
        to: user.email,
        templateId: "userCreated",
        context: {
          name: user.name,
          email: user.email,
          password,
          url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
        },
      });

      if (emailResult) {
        logger.info(`✅ Email de boas-vindas enviado com sucesso!`);
        logger.info(`🔑 Credenciais de acesso:`);
        logger.info(`   Email: ${user.email}`);
        logger.info(`   Senha: ${password}`);
        logger.info(`   URL: ${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}`);
      } else {
        logger.error(`❌ Falha ao enviar email de boas-vindas`);
      }

      return { user, password };
    } else {
      logger.info(`✅ Usuário já existe: ${user.name} (${user.email})`);

      // Verificar se tem acesso ao curso
      const courseProduct = await db.courseProduct.findFirst({
        where: {
          caktoProductId: "ff3fdf61-e88f-43b5-982a-32d50f112414"
        },
        include: {
          course: true
        }
      });

      if (courseProduct) {
        const existingAccess = await db.userCourses.findFirst({
          where: {
            userId: user.id,
            courseId: courseProduct.courseId,
          },
        });

        if (!existingAccess) {
          // Conceder acesso ao curso
          await db.userCourses.create({
            data: {
              userId: user.id,
              courseId: courseProduct.courseId,
              finalTime: null, // Sem data de expiração
            },
          });

          logger.info(`✅ Acesso concedido ao curso: ${courseProduct.course.name}`);

          // Enviar email de acesso
          const emailResult = await sendEmail({
            to: user.email,
            templateId: "courseAccess",
            context: {
              name: user.name,
              courseName: courseProduct.course.name,
              url: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/courses/${courseProduct.courseId}`,
            },
          });

          if (emailResult) {
            logger.info(`✅ Email de acesso ao curso enviado!`);
          }
        } else {
          logger.info(`✅ Usuário já tem acesso ao curso: ${courseProduct.course.name}`);
        }
      }

      return { user };
    }

  } catch (error) {
    logger.error("❌ Erro ao criar usuário:", error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
createUserAccount();
