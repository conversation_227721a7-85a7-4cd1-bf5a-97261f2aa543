import { PrismaClient } from '@prisma/client'
import { auth } from '@repo/auth'

const prisma = new PrismaClient()

async function testLogin() {
  console.log('🔍 Testando login dos usuários criados pelo seed...')

  const testUsers = [
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'user123' },
    { email: '<EMAIL>', password: 'user123' }
  ]

  const authContext = await auth.$context

  for (const testUser of testUsers) {
    console.log(`\n📧 Testando: ${testUser.email}`)

    try {
      // Buscar usuário
      const user = await prisma.user.findUnique({
        where: { email: testUser.email },
        include: {
          accounts: {
            where: { providerId: 'credential' }
          }
        }
      })

      if (!user) {
        console.log('❌ Usuário não encontrado')
        continue
      }

      console.log(`✅ Usuário encontrado: ${user.name}`)
      console.log(`📧 Email verificado: ${user.emailVerified}`)
      console.log(`🔐 Contas encontradas: ${user.accounts.length}`)

      if (user.accounts.length === 0) {
        console.log('❌ Nenhuma conta de credenciais encontrada')
        continue
      }

      const account = user.accounts[0]
      console.log(`🔑 Account ID: ${account.accountId}`)
      console.log(`🔑 Provider ID: ${account.providerId}`)
      console.log(`🔑 Tem senha: ${!!account.password}`)

      if (!account.password) {
        console.log('❌ Senha não encontrada na conta')
        continue
      }

      // Testar hash da senha usando Better Auth
      const isValid = await authContext.password.verify(testUser.password, account.password)
      console.log(`🔐 Senha válida: ${isValid ? '✅' : '❌'}`)

      if (isValid) {
        console.log('🎉 Login funcionando!')
      } else {
        console.log('❌ Senha incorreta')
      }

    } catch (error) {
      console.log(`❌ Erro: ${error}`)
    }
  }

  console.log('\n🏁 Teste concluído!')
}

testLogin()
  .catch(e => {
    console.error('❌ Erro no teste:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
