import { logger } from '@repo/logs';
import { createUser, getUserByEmail, db } from '@repo/database';
import { nanoid } from 'nanoid';
import { sendMail } from '@repo/mail';

/**
 * Script para testar o webhook da Cakto para o Members-base
 * Este script simula o recebimento de um webhook e processa os dados
 * sem realmente fazer uma chamada HTTP.
 */
async function main() {
  try {
    logger.info('🧪 Iniciando teste de webhook Cakto');

    // Solicitar email do usuário de teste
    const email = await logger.prompt('Email do usuário de teste:', {
      required: true,
      placeholder: '<EMAIL>',
      type: 'text',
    });

    // Solicitar ID do produto Cakto
    const productId = await logger.prompt('ID do produto Cakto:', {
      required: true,
      placeholder: 'ff3fdf61-e88f-43b5-982a-32d50f112414',
      type: 'text',
    });

    // Simular payload do webhook
    const payload = {
      id: nanoid(),
      customer: {
        name: 'Usu<PERSON><PERSON> de Teste',
        email,
        phone: '11999999999',
        docNumber: '12345678909',
      },
      product: {
        name: 'Produto Teste',
        id: productId,
        short_id: 'TEST123',
      },
      status: 'approved',
      amount: 99.90,
      paymentMethod: 'credit_card',
      paidAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
    };

    logger.info('📦 Payload do webhook simulado:', payload);

    // Verificar se o usuário já existe
    let user = await getUserByEmail(email);

    // Se não existir, criar o usuário
    if (!user) {
      logger.info(`👤 Criando novo usuário para ${email}`);

      // Gerar senha aleatória
      const password = nanoid(12);

      // Criar o usuário
      user = await createUser({
        email,
        name: payload.customer.name,
        role: "user",
        emailVerified: true,
        onboardingComplete: false,
      });

      if (!user) {
        throw new Error(`Falha ao criar usuário para ${email}`);
      }

      logger.success(`✅ Usuário criado com sucesso: ${user.id}`);

      // Simular envio de email
      logger.info(`📧 Simulando envio de email de boas-vindas para ${email}`);

      // Não envia o email de verdade em ambiente de teste
      // await sendMail({
      //   to: user.email,
      //   subject: "Acesso à área de membros liberado",
      //   template: "userCreated",
      //   props: {
      //     name: user.name,
      //     email: user.email,
      //     password,
      //     url: process.env.NEXT_PUBLIC_APP_URL || "https://members.cakto.com.br",
      //   },
      // });
    } else {
      logger.info(`👤 Usuário já existe: ${user.id}`);
    }

    // Buscar o curso relacionado ao produto
    const courseProduct = await db.courseProduct.findFirst({
      where: {
        caktoProductId: productId,
      },
      include: {
        course: true,
      },
    });

    if (!courseProduct) {
      logger.error(`❌ Nenhum curso encontrado para o produto ID: ${productId}`);
      logger.info('💡 Use o comando "pnpm associate:product" para associar um produto a um curso');
      return;
    }

    logger.info(`🎓 Curso encontrado: ${courseProduct.course.name}`);

    // Verificar se o usuário já tem acesso ao curso
    const existingAccess = await db.userCourses.findFirst({
      where: {
        userId: user.id,
        courseId: courseProduct.courseId,
      },
    });

    if (existingAccess) {
      logger.info(`⚠️ Usuário ${user.email} já tem acesso ao curso ${courseProduct.course.name}`);
      return;
    }

    // Conceder acesso ao curso
    await db.userCourses.create({
      data: {
        userId: user.id,
        courseId: courseProduct.courseId,
        finalTime: null, // Sem data de expiração
      },
    });

    logger.success(`✅ Acesso concedido: Usuário ${user.email} ao curso ${courseProduct.course.name}`);

    // Simular envio de email de acesso liberado
    logger.info(`📧 Simulando envio de email de acesso liberado para ${email}`);

    // Não envia o email de verdade em ambiente de teste
    // await sendMail({
    //   to: user.email,
    //   subject: `Acesso liberado: ${courseProduct.course.name}`,
    //   template: "newUser",
    //   props: {
    //     name: user.name,
    //     courseName: courseProduct.course.name,
    //     url: process.env.NEXT_PUBLIC_APP_URL || "https://members.cakto.com.br",
    //   },
    // });

    logger.success('🎉 Teste de webhook concluído com sucesso!');

  } catch (error) {
    logger.error('❌ Erro ao processar webhook:', error);
  } finally {
    await db.$disconnect();
  }
}

main().catch((error) => {
  logger.error('❌ Erro fatal:', error);
  process.exit(1);
});
