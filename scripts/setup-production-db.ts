#!/usr/bin/env tsx

import { execSync } from "child_process";
import { readFileSync, writeFileSync } from "fs";
import { join } from "path";

async function setupProductionDB() {
  console.log("🚀 Configurando banco de produção na Vercel...");

  try {
    // 1. Verificar se o Vercel CLI está instalado
    try {
      execSync("vercel --version", { stdio: "pipe" });
      console.log("✅ Vercel CLI encontrado");
    } catch {
      console.log("📦 Instalando Vercel CLI...");
      execSync("npm install -g vercel", { stdio: "inherit" });
    }

    // 2. Fazer login no Vercel (se necessário)
    console.log("🔐 Verificando login no Vercel...");
    try {
      execSync("vercel whoami", { stdio: "pipe" });
      console.log("✅ Já logado no Vercel");
    } catch {
      console.log("🔑 Fazendo login no Vercel...");
      execSync("vercel login", { stdio: "inherit" });
    }

        // 3. Criar banco PostgreSQL na Vercel
    console.log("🗄️ Criando banco PostgreSQL na Vercel...");

    // Primeiro, verificar se já existe um projeto Vercel
    try {
      execSync("vercel project ls", { stdio: "pipe" });
    } catch {
      console.log("📁 Inicializando projeto Vercel...");
      execSync("vercel --yes", { stdio: "inherit" });
    }

    // Criar banco PostgreSQL
    console.log("🗄️ Criando banco PostgreSQL...");
    const dbOutput = execSync("vercel storage create postgres --yes", {
      stdio: "pipe",
      encoding: "utf8"
    });

    console.log("📋 Output do comando de criação do banco:");
    console.log(dbOutput);

    // 4. Extrair DATABASE_URL do output
    const dbUrlMatch = dbOutput.match(/DATABASE_URL="([^"]+)"/);
    if (!dbUrlMatch) {
      console.log("⚠️ Não foi possível extrair DATABASE_URL automaticamente");
      console.log("🔧 Configure manualmente no dashboard da Vercel:");
      console.log("1. Acesse: https://vercel.com/dashboard");
      console.log("2. Vá em Storage > PostgreSQL");
      console.log("3. Copie a DATABASE_URL");
      console.log("4. Adicione como variável de ambiente");

      // Continuar sem DATABASE_URL
      console.log("🔄 Continuando sem DATABASE_URL...");
    } else {
      const databaseUrl = dbUrlMatch[1];
      console.log("✅ DATABASE_URL extraída:", databaseUrl.substring(0, 50) + "...");

      // 5. Adicionar variável de ambiente ao projeto
      console.log("🔧 Adicionando DATABASE_URL ao projeto...");
      try {
        execSync(`vercel env add DATABASE_URL production`, {
          stdio: "inherit",
          input: databaseUrl + "\n"
        });
      } catch (error) {
        console.log("⚠️ Erro ao adicionar variável de ambiente automaticamente");
        console.log("🔧 Adicione manualmente no dashboard da Vercel:");
        console.log(`DATABASE_URL: ${databaseUrl}`);
      }
    }

    // 6. Executar migrações
    console.log("🔄 Executando migrações...");
    try {
      execSync("cd packages/database && pnpm push", { stdio: "inherit" });
    } catch (error) {
      console.log("⚠️ Erro ao executar migrações automaticamente");
      console.log("🔧 Execute manualmente:");
      console.log("cd packages/database && pnpm push");
    }

    // 7. Executar seed
    console.log("🌱 Executando seed...");
    try {
      execSync("pnpm tsx scripts/seed-simple.ts", { stdio: "inherit" });
    } catch (error) {
      console.log("⚠️ Erro ao executar seed automaticamente");
      console.log("🔧 Execute manualmente:");
      console.log("pnpm deploy:seed:simple");
    }

    console.log("✅ Configuração do banco de produção concluída!");
    console.log("\n📋 Próximos passos:");
    console.log("1. Faça deploy: vercel --prod");
    console.log("2. Configure domínios personalizados se necessário");
    console.log("3. Configure outras variáveis de ambiente");

  } catch (error) {
    console.error("❌ Erro durante configuração:", error);
    process.exit(1);
  }
}

setupProductionDB();
