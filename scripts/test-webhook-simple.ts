#!/usr/bin/env tsx

import { nanoid } from 'nanoid';
import crypto from 'crypto';

/**
 * Script simples para testar o webhook da Cakto
 * Usa o payload exato do cakto-backend com assinatura HMAC
 */

async function testWebhook() {
  try {
    console.log('🧪 Testando webhook da Cakto...');

    // Dados de teste
    const testEmail = '<EMAIL>';
    const productId = 'ff3fdf61-e88f-43b5-982a-32d50f112414';
    const productName = 'Curso de Marketing Digital';
    const customerName = '<PERSON>';

    // Criar payload EXATO como enviado pelo cakto-backend
    const payload = {
      id: nanoid(),
      customer: {
        name: customerName,
        email: testEmail,
        phone: '11999999999',
        docNumber: '12345678909',
      },
      product: {
        name: productName,
        id: productId,
        short_id: 'TEST123',
      },
      status: 'approved',
      amount: 99.90,
      paymentMethod: 'credit_card',
      paidAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
    };

    console.log('📦 Payload do webhook:', JSON.stringify(payload, null, 2));

    // Obter secret do ambiente
    const secret = process.env.MEMBERS_WEBHOOK_SECRET;
    if (!secret) {
      throw new Error('MEMBERS_WEBHOOK_SECRET não configurada no ambiente');
    }

    // Gerar assinatura HMAC (igual ao cakto-backend)
    const payloadJson = JSON.stringify(payload, null, 0); // Sem espaços para ser igual ao cakto-backend
    const signature = crypto
      .createHmac('sha256', secret)
      .update(payloadJson)
      .digest('hex');

    console.log('🔐 Assinatura HMAC gerada:', signature);

    // URL do webhook
    const webhookUrl = process.env.NEXT_PUBLIC_APP_URL
      ? `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/cakto/purchase`
      : 'http://localhost:3000/api/webhooks/cakto/purchase';

    console.log(`🌐 Enviando webhook para: ${webhookUrl}`);

    // Fazer requisição HTTP real com headers corretos
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Cakto-Signature': signature,
        'User-Agent': 'CaktoBot/1.0',
      },
      body: payloadJson,
    });

    const responseData = await response.json();

    console.log(`📊 Status da resposta: ${response.status}`);
    console.log(`📄 Resposta:`, JSON.stringify(responseData, null, 2));

    if (response.ok) {
      console.log('✅ Webhook processado com sucesso!');

      if (responseData.success) {
        console.log(`👤 Usuário: ${responseData.data?.userId || 'N/A'}`);
        console.log(`📚 Curso: ${responseData.data?.courseName || 'N/A'}`);
        console.log(`💬 Mensagem: ${responseData.message || 'N/A'}`);
      }
    } else {
      console.log('❌ Erro no webhook:', responseData.message || 'Erro desconhecido');
    }

  } catch (error) {
    console.error('❌ Erro ao testar webhook:', error);
  }
}

// Executar teste
testWebhook().catch((error) => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
