import { logger } from "@repo/logs";

// Configurações
const CAKTO_API_URL = process.env.NEXT_PUBLIC_CAKTO_API_URL || "https://api.cakto.com.br";
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

async function testSSOLogin() {
  try {
    logger.info("🚀 Testando fluxo de SSO da Cakto...");

    // 1. Simular redirecionamento para login da Cakto
    const loginUrl = `${CAKTO_API_URL}/auth/sso/login`;
    const redirectUrl = `${APP_URL}/auth/sso/callback`;

    logger.info(`🔗 URLs de SSO:`);
    logger.info(`   API Cakto: ${CAKTO_API_URL}`);
    logger.info(`   Login URL: ${loginUrl}`);
    logger.info(`   Redirect URL: ${redirectUrl}`);

    // 2. Simular parâmetros de login
    const loginParams = new URLSearchParams({
      redirect_uri: redirectUrl,
      response_type: 'code',
      scope: 'openid email profile',
      state: 'test-state-' + Date.now(),
    });

    const fullLoginUrl = `${loginUrl}?${loginParams.toString()}`;

    logger.info(`\n🌐 URL completa de login:`);
    logger.info(`   ${fullLoginUrl}`);

    // 3. Simular callback com código de autorização
    const mockAuthCode = 'mock-auth-code-' + Date.now();
    const mockState = 'test-state-' + Date.now();

    const callbackUrl = `${redirectUrl}?code=${mockAuthCode}&state=${mockState}`;

    logger.info(`\n📞 URL de callback simulada:`);
    logger.info(`   ${callbackUrl}`);

    // 4. Simular troca de código por token
    logger.info(`\n🔄 Simulando troca de código por token...`);
    logger.info(`   Auth Code: ${mockAuthCode}`);
    logger.info(`   State: ${mockState}`);

    // 5. Mostrar como testar manualmente
    logger.info(`\n🔧 Para testar manualmente:`);
    logger.info(`   1. Acesse: ${fullLoginUrl}`);
    logger.info(`   2. Faça login na Cakto`);
    logger.info(`   3. Você será redirecionado para: ${redirectUrl}`);
    logger.info(`   4. O sistema processará o callback automaticamente`);

    // 6. Verificar configurações de SSO
    logger.info(`\n⚙️ Configurações de SSO:`);
    logger.info(`   CAKTO_SSO_CLIENT_ID: ${process.env.CAKTO_SSO_CLIENT_ID || 'Não configurado'}`);
    logger.info(`   CAKTO_SSO_CLIENT_SECRET: ${process.env.CAKTO_SSO_CLIENT_SECRET ? 'Configurado' : 'Não configurado'}`);
    logger.info(`   NEXT_PUBLIC_CAKTO_API_URL: ${CAKTO_API_URL}`);
    logger.info(`   NEXT_PUBLIC_APP_URL: ${APP_URL}`);

    // 7. Verificar se o provider SSO está ativo
    logger.info(`\n🔍 Verificando provider SSO...`);

    try {
      const authConfig = await import("@repo/auth").then(m => m.auth.$config);
      const providers = authConfig.providers || [];
      const ssoProvider = providers.find(p => p.id === 'cakto');

      if (ssoProvider) {
        logger.info(`   ✅ Provider SSO da Cakto está configurado`);
        logger.info(`   ID: ${ssoProvider.id}`);
        logger.info(`   Type: ${ssoProvider.type}`);
      } else {
        logger.warn(`   ⚠️ Provider SSO da Cakto não encontrado`);
        logger.info(`   Providers disponíveis: ${providers.map(p => p.id).join(', ')}`);
      }
    } catch (error) {
      logger.error(`   ❌ Erro ao verificar configuração de auth:`, error);
    }

    logger.info(`\n✅ Teste de SSO concluído!`);
    logger.info(`   Para testar completamente, inicie o servidor e acesse a URL de login.`);

  } catch (error) {
    logger.error("❌ Erro ao testar SSO:", error);
  }
}

// Executar o teste
testSSOLogin();
