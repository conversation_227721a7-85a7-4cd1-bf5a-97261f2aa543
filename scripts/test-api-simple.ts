import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "postgresql://cakto:cakto@localhost:5432/cakto"
    }
  }
})

async function testApiSimple() {
  console.log('🧪 Testando API simples...')

  try {
    // 1. Buscar usuário
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    console.log('1. Usuário:', user?.name, user?.id)

    // 2. Buscar organização
    const organization = await prisma.organization.findUnique({
      where: { slug: 'techcorp-premium' },
      select: { id: true, name: true, slug: true }
    })
    console.log('2. Organização:', organization?.name, organization?.id)

    // 3. Verificar membro
    const userMembership = await prisma.member.findFirst({
      where: {
        userId: user?.id,
        organizationId: organization?.id
      }
    })
    console.log('3. Membro:', userMembership ? 'SIM' : 'NÃO', userMembership?.role)

    // 4. Buscar vitrine
    const vitrine = await prisma.vitrine.findFirst({
      where: {
        organizationId: organization?.id,
        status: 'PUBLISHED',
        visibility: 'PUBLIC'
      }
    })
    console.log('4. Vitrine:', vitrine ? 'SIM' : 'NÃO', vitrine?.title)

    console.log('✅ Teste concluído!')
  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

testApiSimple()
  .catch(e => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
