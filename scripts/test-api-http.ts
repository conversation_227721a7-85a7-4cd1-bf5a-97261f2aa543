import fetch from 'node-fetch'

async function testApiHttp() {
  console.log('🌐 Testando API via HTTP...')

  try {
    // Primeiro, vamos fazer login para obter o cookie de sessão
    console.log('1. Fazendo login...')
    const loginResponse = await fetch('http://localhost:3000/api/auth/sign-in/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'user123'
      })
    })

    console.log('Status do login:', loginResponse.status)

    if (!loginResponse.ok) {
      const errorText = await loginResponse.text()
      console.log('Erro no login:', errorText)
      return
    }

    // Pegar os cookies da resposta
    const cookies = loginResponse.headers.get('set-cookie')
    console.log('Cookies recebidos:', cookies ? 'SIM' : 'NÃO')

    // Agora testar a rota da vitrine
    console.log('2. Testando rota da vitrine...')
    const vitrineResponse = await fetch('http://localhost:3000/api/vitrines/organization/vitrine?organizationSlug=techcorp-premium', {
      method: 'GET',
      headers: {
        'Cookie': cookies || '',
        'Content-Type': 'application/json',
      }
    })

    console.log('Status da vitrine:', vitrineResponse.status)

    if (vitrineResponse.ok) {
      const data = await vitrineResponse.json()
      console.log('✅ Vitrine encontrada:', data.data?.title)
    } else {
      const errorText = await vitrineResponse.text()
      console.log('❌ Erro na vitrine:', errorText)
    }

  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

testApiHttp()
  .catch(e => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
