import { createUser, createUserAccount, getUserByEmail } from "../packages/database";
import { sendEmail } from "../packages/mail";
import { logger } from "../packages/logs";
import crypto from "crypto";
import { nanoid } from "nanoid";
import { PrismaClient } from "@prisma/client";

const db = new PrismaClient();

// Configurações
const WEBHOOK_SECRET = process.env.MEMBERS_WEBHOOK_SECRET || "test-secret";
const WEBHOOK_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

interface CaktoWebhookPayload {
  secret: string;
  event: string;
  data: {
    id: string;
    refId: string;
    customer: {
      name: string;
      email: string;
      phone?: string;
      docNumber?: string;
    };
    affiliate?: string;
    offer?: {
      id: string;
      name: string;
      price: number;
    };
    offer_type?: string;
    product: {
      name: string;
      id: string;
      short_id?: string;
      supportEmail?: string;
      type?: string;
      invoiceDescription?: string;
    };
    parent_order?: string;
    checkoutUrl?: string;
    status: string;
    baseAmount?: number;
    discount?: number;
    amount?: number;
    commissions?: Array<{
      user: string;
      totalAmount: number;
      type: string;
      percentage: number;
    }>;
    reason?: string;
    refund_reason?: string;
    installments?: number;
    paymentMethod?: string;
    paymentMethodName?: string;
    paidAt?: string | null;
    createdAt: string;
    card?: {
      lastDigits: string;
      holderName: string;
      brand: string;
    };
    boleto?: {
      barcode: string;
      boletoUrl: string;
      expirationDate: string;
    };
    pix?: {
      expirationDate: string;
      qrCode: string;
    };
    picpay?: {
      qrCode: string;
      paymentURL: string;
      expirationDate: string;
    };
  };
}

async function createTestCourse() {
  logger.info("🎓 Verificando curso associado ao produto...");

  // Buscar a associação do produto para encontrar o curso correto
  const courseProduct = await db.courseProduct.findFirst({
    where: {
      caktoProductId: "ff3fdf61-e88f-43b5-982a-32d50f112414"
    },
    include: {
      course: {
        include: {
          organization: true
        }
      }
    }
  });

  if (!courseProduct) {
    throw new Error("Produto não associado. Execute 'pnpm associate:product' primeiro.");
  }

  logger.info(`✅ Curso encontrado: ${courseProduct.course.name} (${courseProduct.course.organization.name})`);
  return courseProduct.course;
}

async function getProductAssociation(courseId: string) {
  logger.info("🔗 Verificando associação produto-curso...");

  // ID do produto real da Cakto que você está testando
  const testProductId = "ff3fdf61-e88f-43b5-982a-32d50f112414";

  const courseProduct = await db.courseProduct.findFirst({
    where: {
      caktoProductId: testProductId,
      courseId: courseId
    },
    include: { course: true }
  });

  if (!courseProduct) {
    throw new Error(`Produto ${testProductId} não associado ao curso. Execute pnpm associate:product primeiro.`);
  }

  logger.info(`✅ Associação encontrada: Produto ${testProductId} -> Curso ${courseProduct.course.name}`);
  return courseProduct;
}

async function simulateWebhookPayload(): Promise<CaktoWebhookPayload> {
  // Usar email real para teste - simula uma compra real
  const testEmail = "<EMAIL>";

  return {
    secret: "1340098d-340d-488a-af83-f80e0eaaa773",
    event: "purchase_approved",
    data: {
      id: `webhook-test-${Date.now()}`,
      refId: "9vbgfmg",
      customer: {
        name: "Ismael Costa",
        email: testEmail,
        phone: "+5511999999999",
        docNumber: "123.456.789-00"
      },
      affiliate: "<EMAIL>",
      offer: {
        id: "B8BcHrY",
        name: "Special Offer",
        price: 10
      },
      offer_type: "main",
      product: {
        name: "Automação de Marketing",
        id: "ff3fdf61-e88f-43b5-982a-32d50f112414",
        short_id: "TEST123",
        supportEmail: "<EMAIL>",
        type: "unique",
        invoiceDescription: ""
      },
      parent_order: "95M26wi",
      checkoutUrl: "https://pay.cakto.com.br/EXAMPLE",
      status: "approved",
      baseAmount: 100.0,
      discount: 10.0,
      amount: 90.0,
      commissions: [{
        user: "<EMAIL>",
        totalAmount: 1.89,
        type: "producer",
        percentage: 80
      }],
      reason: "Motivo de recusa do cartão",
      refund_reason: "Motivo de reembolso",
      installments: 1,
      paymentMethod: "credit_card",
      paymentMethodName: "Cartão de Crédito",
      paidAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      card: {
        lastDigits: "4323",
        holderName: "Card Example",
        brand: "visa"
      }
    }
  };
}

async function createUserAndAccount(customer: CaktoWebhookPayload["data"]["customer"]) {
  logger.info(`👤 Verificando/criando usuário para ${customer.email}...`);

  // Verificar se o usuário já existe
  let user = await getUserByEmail(customer.email);

  if (!user) {
    logger.info(`🆕 Criando novo usuário para ${customer.email}`);

    // Gerar senha aleatória
    const password = nanoid(12);

    // Criar o usuário
    user = await createUser({
      email: customer.email,
      name: customer.name,
      role: "user",
      emailVerified: true,
      onboardingComplete: false,
    });

    if (!user) {
      throw new Error(`Failed to create user for ${customer.email}`);
    }

    // Criar conta com senha
          const authContext = await import("../packages/auth").then(m => m.auth.$context);
    const hashedPassword = await authContext.password.hash(password);

    await createUserAccount({
      userId: user.id,
      providerId: "credential",
      accountId: user.email,
      hashedPassword,
    });

    logger.info(`✅ Usuário criado: ${user.name} (${user.email})`);

    // Enviar email de boas-vindas
    logger.info(`📧 Enviando email de boas-vindas para ${user.email}...`);

    // Email simples sem template React para evitar problemas
    const emailResult = await sendEmail({
      to: user.email,
      subject: `Bem-vindo ao Cakto Members! | Suas credenciais de acesso`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Bem-vindo ao Cakto Members!</h2>
          <p>Olá ${user.name},</p>
          <p>Sua conta foi criada com sucesso! Aqui estão suas credenciais de acesso:</p>
          <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Email:</strong> ${user.email}</p>
            <p><strong>Senha:</strong> ${password}</p>
          </div>
          <p>Clique no botão abaixo para fazer login:</p>
          <p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/auth/login"
               style="background: #556cd6; color: #fff; padding: 12px 32px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Fazer Login
            </a>
          </p>
          <p>Se tiver dúvidas, conte com nosso suporte.</p>
        </div>
      `,
      text: `
        Bem-vindo ao Cakto Members!

        Olá ${user.name},

        Sua conta foi criada com sucesso! Aqui estão suas credenciais de acesso:

        Email: ${user.email}
        Senha: ${password}

        Acesse: ${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/auth/login

        Se tiver dúvidas, conte com nosso suporte.
      `
    });

    if (emailResult) {
      logger.info(`✅ Email de boas-vindas enviado com sucesso!`);
      logger.info(`🔑 Credenciais de acesso:`);
      logger.info(`   Email: ${user.email}`);
      logger.info(`   Senha: ${password}`);
    } else {
      logger.error(`❌ Falha ao enviar email de boas-vindas`);
    }

    return { user, isNewUser: true, password };
  } else {
    logger.info(`✅ Usuário já existe: ${user.name} (${user.email})`);
    return { user, isNewUser: false };
  }
}

async function grantCourseAccess(user: any, courseProduct: any) {
  logger.info(`🎯 Verificando acesso ao curso para ${user.email}...`);

  // Verificar se o usuário já tem acesso ao curso
  const existingAccess = await db.userCourses.findFirst({
    where: {
      userId: user.id,
      courseId: courseProduct.courseId,
    },
  });

  if (existingAccess) {
    logger.info(`⚠️ Usuário ${user.email} já tem acesso ao curso ${courseProduct.course.name}`);
    return false;
  }

  // Conceder acesso ao curso
  await db.userCourses.create({
    data: {
      userId: user.id,
      courseId: courseProduct.courseId,
      finalTime: null, // Sem data de expiração
    },
  });

  logger.info(`✅ Acesso concedido: Usuário ${user.email} ao curso ${courseProduct.course.name}`);
  return true;
}

async function sendCourseAccessEmail(user: any, courseProduct: any) {
  logger.info(`📧 Enviando email de acesso ao curso para ${user.email}...`);

  // Gerar magic link real usando Better Auth
  const auth = await import("../packages/auth").then(m => m.auth);
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  // Criar URL de callback para o curso
  const callbackURL = `${baseUrl}/courses/${courseProduct.courseId}`;

  // Gerar token para magic link
  const token = crypto.randomBytes(32).toString('hex');

  // Criar URL do magic link com token
  const magicLinkUrl = `${baseUrl}/api/auth/magic-link/verify?token=${token}&callbackURL=${encodeURIComponent(callbackURL)}`;

  // Email simples sem template React para evitar problemas
  const emailResult = await sendEmail({
    to: user.email,
    subject: `Acesso Liberado ao Curso ${courseProduct.course.name} | Cakto Members`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f8f9fb; padding: 20px;">
        <div style="background: #fff; border-radius: 8px; padding: 32px; box-shadow: 0 5px 10px rgba(20,50,70,.08);">
          <!-- Logo -->
          <div style="text-align: center; margin-bottom: 32px;">
            <img src="${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/logo.png"
                 alt="Cakto Members"
                 width="120"
                 style="margin: 0 auto 16px; display: block;">
          </div>

          <!-- Conteúdo -->
          <h2 style="color: #333; text-align: center; margin-bottom: 24px;">Acesso Liberado ao Curso!</h2>
          <p style="color: #333; font-size: 16px; line-height: 1.5;">Olá ${user.name},</p>
          <p style="color: #333; font-size: 16px; line-height: 1.5;">Seu acesso ao curso <strong>${courseProduct.course.name}</strong> foi liberado com sucesso!</p>
          <p style="color: #333; font-size: 16px; line-height: 1.5;">Clique no botão abaixo para fazer login automaticamente e começar a estudar agora mesmo:</p>

          <!-- Botão -->
          <div style="text-align: center; margin: 32px 0;">
            <a href="${magicLinkUrl}"
               style="background: #39a561; color: #fff; padding: 12px 32px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600; font-size: 16px;">
              Acessar Curso
            </a>
          </div>

          <!-- Informação do Magic Link -->
          <p style="color: #666; font-size: 14px; margin-top: 24px; text-align: center;">
            <strong>🔐 Login Automático:</strong> Este link fará login automaticamente na sua conta e te levará direto ao curso.
          </p>

          <!-- Footer -->
          <div style="margin-top: 40px; text-align: center; color: #888; font-size: 12px; border-top: 1px solid #eee; padding-top: 20px;">
            <p style="margin: 0;">Bons estudos!</p>
            <p style="margin: 8px 0 0;">Se tiver dúvidas, conte com nosso suporte.</p>
            <p style="margin: 16px 0 0; font-weight: 600;">Atenciosamente,<br>Equipe Cakto Members</p>
          </div>
        </div>
      </div>
    `,
    text: `
      Acesso Liberado ao Curso!

      Olá ${user.name},

      Seu acesso ao curso ${courseProduct.course.name} foi liberado com sucesso!

      🔐 Login Automático: ${magicLinkUrl}

      Este link fará login automaticamente na sua conta e te levará direto ao curso.

      Bons estudos!
      Se tiver dúvidas, conte com nosso suporte.
    `
  });

  if (emailResult) {
    logger.info(`✅ Email de acesso ao curso enviado com sucesso!`);
    logger.info(`🔗 Magic link gerado: ${magicLinkUrl}`);
  } else {
    logger.error(`❌ Falha ao enviar email de acesso ao curso`);
  }
}

async function generateWebhookSignature(payload: CaktoWebhookPayload): Promise<string> {
  const body = JSON.stringify(payload);
  return crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(body)
    .digest('hex');
}

async function testWebhookFlow() {
  try {
    logger.info("🚀 Iniciando teste completo do webhook da Cakto...");

    // 1. Criar curso de teste
    const course = await createTestCourse();

    // 2. Verificar associação produto-curso
    const courseProduct = await getProductAssociation(course.id);

    // 3. Simular payload do webhook
    const webhookPayload = await simulateWebhookPayload();
    logger.info(`📦 Payload do webhook simulado:`);
    logger.info(`   Cliente: ${webhookPayload.data.customer.name} (${webhookPayload.data.customer.email})`);
    logger.info(`   Produto: ${webhookPayload.data.product.name} (${webhookPayload.data.product.id})`);
    logger.info(`   Status: ${webhookPayload.data.status}`);
    logger.info(`   Valor: R$ ${webhookPayload.data.amount}`);

        // 4. Criar usuário e conta
    const { user, isNewUser, password } = await createUserAndAccount(webhookPayload.data.customer);

    // 5. Conceder acesso ao curso
    const accessGranted = await grantCourseAccess(user, courseProduct);

    // 6. Enviar email de acesso (se não for usuário novo)
    if (accessGranted && !isNewUser) {
      await sendCourseAccessEmail(user, courseProduct);
    }

    // 7. Gerar assinatura HMAC para teste
    const signature = await generateWebhookSignature(webhookPayload);

    logger.info("\n🎉 Teste concluído com sucesso!");
    logger.info("📋 Resumo do teste:");
    logger.info(`   ✅ Curso: ${course.name}`);
    logger.info(`   ✅ Produto: ${webhookPayload.data.product.name}`);
    logger.info(`   ✅ Usuário: ${user.name} (${user.email})`);
    logger.info(`   ✅ Acesso ao curso: ${accessGranted ? "Concedido" : "Já existia"}`);
    logger.info(`   ✅ Emails enviados: ${isNewUser ? "Boas-vindas" : "Acesso ao curso"}`);

    if (isNewUser && password) {
      logger.info("\n🔑 Credenciais de acesso:");
      logger.info(`   Email: ${user.email}`);
      logger.info(`   Senha: ${password}`);
      logger.info(`   URL: ${WEBHOOK_URL}`);
    }

    logger.info("\n🔐 Para testar o webhook real, use:");
    logger.info(`   URL: ${WEBHOOK_URL}/api/webhooks/cakto/purchase`);
    logger.info(`   Method: POST`);
    logger.info(`   Headers: X-Cakto-Signature: ${signature}`);
    logger.info(`   Body: ${JSON.stringify(webhookPayload, null, 2)}`);

  } catch (error) {
    logger.error("❌ Erro durante o teste:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o teste
testWebhookFlow();
