import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  const email = process.env.USER_EMAIL || '<EMAIL>'
  const password = process.env.USER_PASSWORD || 'password123'
  const name = process.env.USER_NAME || 'Test User'
  const role = process.env.USER_ROLE || 'user'

  // Verifica se já existe
  const existing = await prisma.user.findUnique({ where: { email } })
  if (existing) {
    console.log(`User already exists: ${email}`)
    return
  }

  // Cria o hash da senha usando bcryptjs (compatível com Better Auth)
  const hashedPassword = await hash(password, 12)

  // Cria o usuário
  const user = await prisma.user.create({
    data: {
      email,
      name,
      role,
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  // Cria a conta com senha (Better Auth usa 'credential' como providerId)
  await prisma.account.create({
    data: {
      userId: user.id,
      providerId: 'credential',
      accountId: email,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  console.log(`✅ User created successfully with Better Auth compatibility:`)
  console.log(`Email: ${email}`)
  console.log(`Password: ${password}`)
  console.log(`Role: ${role}`)
  console.log(`Name: ${name}`)
  console.log(`\n🔐 You can now login with these credentials`)
  console.log(`🌐 Try accessing: http://localhost:3000/auth/login`)
}

main()
  .catch(e => {
    console.error('❌ Error creating user:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
