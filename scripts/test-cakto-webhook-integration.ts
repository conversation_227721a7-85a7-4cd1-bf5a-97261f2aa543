import { nanoid } from 'nanoid';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import crypto from 'crypto';

/**
 * Script para testar a integração completa do webhook Cakto
 * Simula uma requisição HTTP real para o endpoint de webhook
 */
async function main() {
  try {
    logger.info('🧪 Iniciando teste de integração webhook Cakto');

    // Solicitar dados do teste
    const email = await logger.prompt('Email do usuário de teste:', {
      required: true,
      placeholder: '<EMAIL>',
      type: 'text',
    });

    const productId = await logger.prompt('ID do produto Cakto:', {
      required: true,
      placeholder: 'ff3fdf61-e88f-43b5-982a-32d50f112414',
      type: 'text',
    });

    // Verificar se existe um curso associado ao produto
    const courseProduct = await db.courseProduct.findFirst({
      where: { caktoProductId: productId },
      include: { course: true }
    });

    if (!courseProduct) {
      logger.error(`❌ Nenhum curso encontrado para o produto ID: ${productId}`);
      logger.info('💡 Criando associação de teste...');
      
      // Buscar primeiro curso disponível
      const course = await db.course.findFirst();
      if (!course) {
        logger.error('❌ Nenhum curso encontrado no banco de dados');
        return;
      }

      // Criar associação
      await db.courseProduct.create({
        data: {
          courseId: course.id,
          caktoProductId: productId,
        }
      });
      
      logger.success(`✅ Associação criada: Produto ${productId} → Curso ${course.name}`);
    }

    // Criar payload do webhook
    const payload = {
      id: nanoid(),
      customer: {
        name: 'Usuário de Teste',
        email,
        phone: '11999999999',
        docNumber: '12345678909',
      },
      product: {
        name: 'Produto Teste',
        id: productId,
        short_id: 'TEST123',
      },
      status: 'approved',
      amount: 99.90,
      paymentMethod: 'credit_card',
      paidAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
    };

    // Gerar assinatura do webhook
    const webhookSecret = process.env.CAKTO_WEBHOOK_SECRET;
    if (!webhookSecret) {
      logger.error('❌ CAKTO_WEBHOOK_SECRET não configurado');
      return;
    }

    const payloadString = JSON.stringify(payload);
    const signature = crypto
      .createHmac('sha256', webhookSecret)
      .update(payloadString)
      .digest('hex');

    logger.info('📦 Payload do webhook:', payload);
    logger.info('🔐 Assinatura gerada:', signature);

    // Simular requisição HTTP
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const webhookUrl = `${baseUrl}/webhooks/cakto/purchase`;

    logger.info(`📡 Enviando webhook para: ${webhookUrl}`);

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Cakto-Signature': signature,
      },
      body: payloadString,
    });

    const responseData = await response.json();

    if (response.ok) {
      logger.success('✅ Webhook processado com sucesso!');
      logger.info('📄 Resposta:', responseData);
      
      // Verificar se o usuário foi criado/atualizado
      const user = await db.user.findUnique({
        where: { email },
        include: {
          userCourses: {
            include: {
              course: true
            }
          }
        }
      });

      if (user) {
        logger.success(`👤 Usuário encontrado: ${user.name} (${user.email})`);
        logger.info(`📚 Cursos com acesso: ${user.userCourses.length}`);
        
        user.userCourses.forEach(uc => {
          logger.info(`  - ${uc.course.name}`);
        });
      }
      
    } else {
      logger.error('❌ Erro no webhook:', responseData);
    }

  } catch (error) {
    logger.error('❌ Erro no teste:', error);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main();
}