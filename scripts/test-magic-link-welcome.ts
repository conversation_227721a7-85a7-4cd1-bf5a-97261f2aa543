import { createUser, getUserByEmail, db } from "@repo/database";
import { auth } from "@repo/auth";
import { logger } from "@repo/logs";

async function testMagicLinkWelcome() {
  try {
    // Usar um email real para teste - você pode alterar aqui
    const email = "<EMAIL>";
    const name = "<PERSON><PERSON><PERSON>";

    logger.info(`🧪 Testando fluxo de magic link para ${email}...`);

    // Verificar se o usuário já existe
    let user = await getUserByEmail(email);

    if (!user) {
      logger.info(`🆕 Criando novo usuário para ${email}`);

      // Criar o usuário
      user = await createUser({
        email: email,
        name: name,
        role: "user",
        emailVerified: true,
        onboardingComplete: false,
      });

      if (!user) {
        throw new Error(`Failed to create user for ${email}`);
      }

      logger.info(`✅ Usuário criado: ${user.name} (${user.email})`);
    } else {
      logger.info(`👤 Usuário já existe: ${user.name} (${user.email})`);
    }

    // Buscar ou criar organização padrão
    let defaultOrganization = await db.organization.findFirst({
      where: { name: "Cakto Members" }
    });

    if (!defaultOrganization) {
      defaultOrganization = await db.organization.create({
        data: {
          name: "Cakto Members",
          slug: "cakto-members",
          createdAt: new Date(),
        }
      });
      logger.info(`🏢 Organização criada: ${defaultOrganization.name}`);
    } else {
      logger.info(`🏢 Organização já existe: ${defaultOrganization.name}`);
    }

    // Verificar se o usuário já é membro da organização
    const existingMember = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId: defaultOrganization.id,
      },
    });

    // Se não for membro, adicionar à organização
    if (!existingMember) {
      await db.member.create({
        data: {
          userId: user.id,
          organizationId: defaultOrganization.id,
          role: "member",
          createdAt: new Date(),
        },
      });
      logger.info(`👥 Usuário adicionado à organização ${defaultOrganization.name}`);
    } else {
      logger.info(`👥 Usuário já é membro da organização ${defaultOrganization.name}`);
    }

    // Criar um curso de teste se não existir
    let testCourse = await db.courses.findFirst({
      where: { name: "Curso de Teste Magic Link" }
    });

    if (!testCourse) {
      testCourse = await db.courses.create({
        data: {
          name: "Curso de Teste Magic Link",
          description: "Curso para testar o fluxo de magic link",
          organizationId: defaultOrganization.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      });
      logger.info(`📚 Curso criado: ${testCourse.name}`);
    } else {
      logger.info(`📚 Curso já existe: ${testCourse.name}`);
    }

    // Verificar se o usuário já tem acesso ao curso
    const existingAccess = await db.userCourses.findFirst({
      where: {
        userId: user.id,
        courseId: testCourse.id,
      },
    });

    // Se não tiver acesso, conceder
    if (!existingAccess) {
      await db.userCourses.create({
        data: {
          userId: user.id,
          courseId: testCourse.id,
          finalTime: null,
        },
      });
      logger.info(`🎯 Acesso concedido ao curso ${testCourse.name}`);
    } else {
      logger.info(`🎯 Usuário já tem acesso ao curso ${testCourse.name}`);
    }

    // Enviar magic link
    logger.info(`📧 Enviando magic link para ${user.email}...`);

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

    // Usar a API correta do Better Auth
    const magicLinkResult = await auth.api.signInMagicLink({
      body: {
        email: user.email,
        callbackURL: `${baseUrl}/app/courses/${testCourse.id}`,
      },
      headers: new Headers(),
    });

    if (magicLinkResult.error) {
      logger.error(`❌ Erro ao enviar magic link:`, magicLinkResult.error);
    } else {
      logger.info(`✅ Magic link enviado com sucesso!`);
      logger.info(`🔗 O usuário receberá um email com link de acesso direto ao curso`);
      logger.info(`📧 Email: ${user.email}`);
      logger.info(`🎯 Curso: ${testCourse.name}`);
      logger.info(`🏢 Organização: ${defaultOrganization.name}`);
      logger.info(`\n📬 Verifique sua caixa de entrada em: ${user.email}`);
    }

  } catch (error) {
    logger.error("❌ Erro no teste:", error);
  }
}

// Executar o teste
testMagicLinkWelcome().then(() => {
  logger.info("🏁 Teste concluído!");
  process.exit(0);
}).catch((error) => {
  logger.error("💥 Erro fatal:", error);
  process.exit(1);
});
