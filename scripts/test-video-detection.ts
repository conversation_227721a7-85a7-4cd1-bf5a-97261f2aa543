#!/usr/bin/env tsx

import { detectVideoProvider } from '../apps/web/modules/saas/organizations/course/lib/video-utils'

// URLs de teste para diferentes provedores
const testUrls = [
  // Bunny.net URLs
  'https://iframe.mediadelivery.net/embed/759/aec1f124-28b1-49aa-8e69-07e659e0106e',
  'https://iframe.mediadelivery.net/embed/759/92ce3920-ac1a-4c27-9c85-85902b460eb4',
  'https://bunnycdn.com/video/759/test-video-id',

  // YouTube URLs
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
  'https://youtu.be/dQw4w9WgXcQ',
  'https://www.youtube.com/embed/dQw4w9WgXcQ',
  'https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ',

  // Vimeo URLs
  'https://vimeo.com/123456789',
  'https://player.vimeo.com/video/123456789',

  // URLs inválidas/desconhecidas
  'https://example.com/video.mp4',
  'https://vimeo.com/invalid-url',
  ''
]

function testVideoDetection() {
  console.log('🧪 Testando detecção de provedores de vídeo...\n')

  testUrls.forEach((url, index) => {
    console.log(`Teste ${index + 1}: ${url || '(URL vazia)'}`)

    try {
      const provider = detectVideoProvider(url)
      console.log(`  ✅ Tipo detectado: ${provider.type}`)

      if (provider.embedUrl) {
        console.log(`  🔗 URL de embed: ${provider.embedUrl}`)
      }

      if (provider.thumbnailUrl) {
        console.log(`  🖼️  Thumbnail: ${provider.thumbnailUrl}`)
      }

      if (provider.config) {
        console.log(`  ⚙️  Config:`, provider.config)
      }

    } catch (error) {
      console.log(`  ❌ Erro: ${error}`)
    }

    console.log('')
  })

  console.log('🎉 Teste de detecção concluído!')
}

// Executar o teste
testVideoDetection()
