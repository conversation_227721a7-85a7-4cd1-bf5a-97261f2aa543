#!/usr/bin/env tsx

import { execSync } from "child_process";
import { copyFileSync, existsSync } from "fs";
import { join } from "path";

async function deployVercel() {
  console.log("🚀 Fazendo deploy para Vercel...");
  console.log("================================");

  try {
    // 1. Verificar se estamos no diretório correto
    console.log("📁 Verificando estrutura do projeto...");

    if (!existsSync("apps/web/package.json")) {
      console.error("❌ Não foi possível encontrar apps/web/package.json");
      process.exit(1);
    }

    // 2. Copiar vercel.json para apps/web se não existir
    const vercelJsonPath = join("apps/web", "vercel.json");
    if (!existsSync(vercelJsonPath)) {
      console.log("📋 Copiando vercel.json para apps/web...");
      copyFileSync("vercel.json", vercelJsonPath);
    }

        // 3. Fazer deploy do diretório raiz
    console.log("🌐 Iniciando deploy...");
    execSync("vercel --prod", {
      stdio: "inherit"
    });

    console.log("\n✅ Deploy concluído com sucesso!");
    console.log("\n🔐 Contas de teste disponíveis:");
    console.log("Super Admin: <EMAIL> / admin123");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Ismael Costa: <EMAIL> / user123");

  } catch (error) {
    console.error("❌ Erro durante deploy:", error);
    process.exit(1);
  }
}

deployVercel();
