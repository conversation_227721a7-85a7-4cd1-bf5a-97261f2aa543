#!/bin/bash

# =============================================================================
# SCRIPT DE AUTOMAÇÃO DE MIGRAÇÃO - CAKTO MEMBERS → CAKTO MEMBERS
# =============================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ] || [ ! -f "pnpm-workspace.yaml" ]; then
    error "Este script deve ser executado no diretório raiz do members-base"
    exit 1
fi

# Função para verificar pré-requisitos
check_prerequisites() {
    log "Verificando pré-requisitos..."

    # Verificar Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js não está instalado"
        exit 1
    fi

    # Verificar pnpm
    if ! command -v pnpm &> /dev/null; then
        error "pnpm não está instalado"
        exit 1
    fi

    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado"
        exit 1
    fi

    # Verificar arquivo .env.local
    if [ ! -f ".env.local" ]; then
        warn "Arquivo .env.local não encontrado. Copiando do exemplo..."
        cp env.local.example .env.local
        warn "Por favor, configure as variáveis de ambiente no arquivo .env.local"
        exit 1
    fi

    log "✅ Pré-requisitos verificados"
}

# Função para backup do banco atual
backup_database() {
    log "Criando backup do banco de dados atual..."

    BACKUP_DIR="./backups/$(date +'%Y%m%d_%H%M%S')"
    mkdir -p "$BACKUP_DIR"

    # Backup do banco atual (se existir)
    if [ -n "$OLD_DATABASE_URL" ]; then
        pg_dump "$OLD_DATABASE_URL" > "$BACKUP_DIR/old_database.sql"
        log "✅ Backup criado em $BACKUP_DIR/old_database.sql"
    else
        warn "URL do banco antigo não configurada, pulando backup"
    fi
}

# Função para instalar dependências
install_dependencies() {
    log "Instalando dependências..."
    pnpm install
    log "✅ Dependências instaladas"
}

# Função para configurar banco de dados
setup_database() {
    log "Configurando banco de dados..."

    # Iniciar serviços do Docker
    docker-compose up -d postgres redis

    # Aguardar PostgreSQL estar pronto
    log "Aguardando PostgreSQL..."
    until docker-compose exec -T postgres pg_isready -U cakto; do
        sleep 2
    done

    # Executar migrações
    log "Executando migrações do banco..."
    pnpm --filter database push
    pnpm --filter database generate

    log "✅ Banco de dados configurado"
}

# Função para migrar schema
migrate_schema() {
    log "Migrando schema do banco..."

    if [ -f "scripts/migrate-schema.ts" ]; then
        pnpm tsx scripts/migrate-schema.ts
        log "✅ Schema migrado"
    else
        warn "Script de migração não encontrado, pulando..."
    fi
}

# Função para build do projeto
build_project() {
    log "Fazendo build do projeto..."

    pnpm build

    log "✅ Build concluído"
}

# Função para executar testes
run_tests() {
    log "Executando testes..."

    pnpm test

    log "✅ Testes concluídos"
}

# Função para iniciar desenvolvimento
start_development() {
    log "Iniciando ambiente de desenvolvimento..."

    pnpm dev &
    DEV_PID=$!

    log "✅ Servidor de desenvolvimento iniciado (PID: $DEV_PID)"
    log "🌐 Acesse: http://localhost:3000"
    log "📊 Prisma Studio: http://localhost:5555"

    # Aguardar interrupção
    trap "kill $DEV_PID; log 'Servidor parado'; exit" INT
    wait
}

# Função para mostrar menu principal
show_menu() {
    echo
    echo "🚀 MIGRAÇÃO CAKTO MEMBERS → CAKTO MEMBERS"
    echo "========================================"
    echo
    echo "1. Verificar pré-requisitos"
    echo "2. Backup do banco atual"
    echo "3. Instalar dependências"
    echo "4. Configurar banco de dados"
    echo "5. Migrar schema"
    echo "6. Build do projeto"
    echo "7. Executar testes"
    echo "8. Iniciar desenvolvimento"
    echo "9. Migração completa (1-7)"
    echo "0. Sair"
    echo
    read -p "Escolha uma opção: " choice
}

# Função para migração completa
full_migration() {
    log "Iniciando migração completa..."

    check_prerequisites
    backup_database
    install_dependencies
    setup_database
    migrate_schema
    build_project
    run_tests

    log "🎉 Migração completa finalizada!"
    log "Próximos passos:"
    log "1. Configure as variáveis de ambiente"
    log "2. Teste as funcionalidades principais"
    log "3. Execute 'pnpm dev' para desenvolvimento"
}

# Função principal
main() {
    echo
    log "Bem-vindo ao script de migração Cakto Members → Cakto Members"
    echo

    # Carregar variáveis de ambiente
    if [ -f ".env.local" ]; then
        export $(cat .env.local | grep -v '^#' | xargs)
    fi

    while true; do
        show_menu

        case $choice in
            1)
                check_prerequisites
                ;;
            2)
                backup_database
                ;;
            3)
                install_dependencies
                ;;
            4)
                setup_database
                ;;
            5)
                migrate_schema
                ;;
            6)
                build_project
                ;;
            7)
                run_tests
                ;;
            8)
                start_development
                ;;
            9)
                full_migration
                ;;
            0)
                log "Saindo..."
                exit 0
                ;;
            *)
                error "Opção inválida"
                ;;
        esac

        echo
        read -p "Pressione Enter para continuar..."
    done
}

# Executar função principal
main "$@"
