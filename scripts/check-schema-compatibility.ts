import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient();

async function checkSchemaCompatibility() {
	console.log("🔍 Verificando compatibilidade do schema...");

	try {
		let hasIssues = false;

		// Verificar se as colunas existem antes de tentar acessá-las
		console.log("🔍 Verificando estrutura atual do banco...");

		const tableInfo = await prisma.$queryRaw<Array<{ column_name: string; table_name: string }>>`
			SELECT column_name, table_name
			FROM information_schema.columns
			WHERE table_name IN ('user', 'account', 'member')
			AND column_name IN ('mainAppUserId', 'role')
		`;

		const existingColumns = tableInfo.map(col => `${col.table_name}.${col.column_name}`);
		console.log("📋 Colunas existentes:", existingColumns);

		// 1. Verificar duplicatas na tabela account
		console.log("📋 Verificando duplicatas na tabela account...");
		const duplicateAccounts = await prisma.$queryRaw<Array<{ userId: string; providerId: string; count: number }>>`
			SELECT "userId", "providerId", COUNT(*) as count
			FROM account
			GROUP BY "userId", "providerId"
			HAVING COUNT(*) > 1
		`;

		if (duplicateAccounts.length > 0) {
			console.log(`❌ Encontradas ${duplicateAccounts.length} duplicatas na tabela account`);
			hasIssues = true;

			for (const duplicate of duplicateAccounts) {
				console.log(`   - userId: ${duplicate.userId}, providerId: ${duplicate.providerId} (${duplicate.count} registros)`);
			}
		} else {
			console.log("✅ Nenhuma duplicata encontrada na tabela account");
		}

		// 2. Verificar duplicatas na tabela user (mainAppUserId) - apenas se a coluna existir
		if (existingColumns.includes('user.mainAppUserId')) {
			console.log("📋 Verificando duplicatas na tabela user (mainAppUserId)...");
			const duplicateUsers = await prisma.$queryRaw<Array<{ mainAppUserId: string; count: number }>>`
				SELECT "mainAppUserId", COUNT(*) as count
				FROM "user"
				WHERE "mainAppUserId" IS NOT NULL
				GROUP BY "mainAppUserId"
				HAVING COUNT(*) > 1
			`;

			if (duplicateUsers.length > 0) {
				console.log(`❌ Encontradas ${duplicateUsers.length} duplicatas na tabela user (mainAppUserId)`);
				hasIssues = true;

				for (const duplicate of duplicateUsers) {
					console.log(`   - mainAppUserId: ${duplicate.mainAppUserId} (${duplicate.count} registros)`);
				}
			} else {
				console.log("✅ Nenhuma duplicata encontrada na tabela user (mainAppUserId)");
			}
		} else {
			console.log("ℹ️  Coluna mainAppUserId não existe ainda - será criada pelo schema");
		}

		// 3. Verificar roles inválidas na tabela member - apenas se a coluna existir
		if (existingColumns.includes('member.role')) {
			console.log("📋 Verificando roles inválidas na tabela member...");
			const invalidRoles = await prisma.$queryRaw<Array<{ id: string; role: string }>>`
				SELECT id, role FROM member
				WHERE role NOT IN ('owner', 'admin', 'member') AND role IS NOT NULL
			`;

			if (invalidRoles.length > 0) {
				console.log(`❌ Encontrados ${invalidRoles.length} membros com roles inválidas`);
				hasIssues = true;

				for (const member of invalidRoles) {
					console.log(`   - member ${member.id}: role "${member.role}"`);
				}
			} else {
				console.log("✅ Nenhuma role inválida encontrada na tabela member");
			}

			// 4. Verificar membros sem role
			const membersWithoutRole = await prisma.$queryRaw<Array<{ id: string }>>`
				SELECT id FROM member WHERE role IS NULL
			`;

			if (membersWithoutRole.length > 0) {
				console.log(`❌ Encontrados ${membersWithoutRole.length} membros sem role definida`);
				hasIssues = true;
			} else {
				console.log("✅ Todos os membros têm role definida");
			}
		} else {
			console.log("ℹ️  Coluna role não existe ainda - será criada pelo schema");
		}

		if (hasIssues) {
			console.log("\n🚨 PROBLEMAS ENCONTRADOS!");
			console.log("Execute o script de migração antes do push:");
			console.log("pnpm db:migrate");
			process.exit(1);
		} else {
			console.log("\n✅ SCHEMA COMPATÍVEL!");
			console.log("Você pode executar o push com segurança:");
			console.log("pnpm db:push");
		}

	} catch (error) {
		console.error("❌ Erro durante a verificação:", error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Executar verificação se o script for chamado diretamente
if (require.main === module) {
	checkSchemaCompatibility()
		.then(() => {
			console.log("🎉 Verificação concluída!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("💥 Erro na verificação:", error);
			process.exit(1);
		});
}

export { checkSchemaCompatibility };
