import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkDatabase() {
  try {
    console.log('🔍 Checking database data...')

    // Check organizations
    const organizations = await prisma.organization.findMany({
      select: { id: true, name: true, slug: true }
    })

    console.log('Organizations:', organizations)

    // Check courses
    const courses = await prisma.courses.findMany({
      select: { id: true, name: true, organizationId: true }
    })

    console.log('Courses:', courses)

    // Check users
    const users = await prisma.user.findMany({
      select: { id: true, name: true, email: true, role: true }
    })

    console.log('Users:', users)

    // Check user courses
    const userCourses = await prisma.userCourses.findMany({
      select: { userId: true, courseId: true }
    })

    console.log('User Courses:', userCourses)

    // Check members
    const members = await prisma.member.findMany({
      select: { userId: true, organizationId: true, role: true }
    })

    console.log('Members:', members)

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDatabase()
