// Set environment variables
process.env.DATABASE_URL = "postgresql://cakto:cakto@localhost:5432/cakto";

import { db } from "@repo/database";

async function testCourseAPI() {
  const courseId = "cmdfiqvu3001tyoyus55o5yg1";
  const organizationSlug = "cakto-academy";
  const userId = "cmdfiqu9e0009yoyu9fs9ifcb";

  console.log("🔍 Testing Course API logic...");

  // Step 1: Find organization by slug
  const organization = await db.organization.findFirst({
    where: { slug: organizationSlug },
  });

  console.log("1. Organization found:", organization ? { id: organization.id, name: organization.name } : "NOT FOUND");

  if (!organization) {
    console.log("❌ Organization not found");
    return;
  }

  // Step 2: Get user
  const user = await db.user.findFirst({
    where: { id: userId },
    select: { id: true, name: true, email: true, role: true }
  });

  console.log("2. User found:", user ? { id: user.id, name: user.name, email: user.email, role: user.role } : "NOT FOUND");

  if (!user) {
    console.log("❌ User not found");
    return;
  }

  // Step 3: Check user membership
  const userMembership = await db.member.findFirst({
    where: {
      userId: userId,
      organizationId: organization.id,
    },
  });

  console.log("3. User membership:", userMembership ? { role: userMembership.role, organizationId: userMembership.organizationId } : "NOT FOUND");

  // Step 4: Find course
  const course = await db.courses.findFirst({
    where: {
      id: courseId,
      organizationId: organization.id
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
        }
      },
      courseModules: {
        include: {
          module: {
            include: {
              lessons: {
                orderBy: { position: 'asc' },
                include: {
                  userWatchedLessons: {
                    where: { userId: user.id },
                    select: {
                      isCompleted: true,
                      currentTime: true,
                      duration: true,
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: { module: { position: 'asc' } }
      }
    }
  });

  console.log("4. Course found:", course ? { id: course.id, name: course.name, organizationId: course.organizationId } : "NOT FOUND");

  if (!course) {
    console.log("❌ Course not found");
    return;
  }

  // Step 5: Check user course access
  const userCourseAccess = await db.userCourses.findFirst({
    where: {
      userId: userId,
      courseId: courseId,
    },
  });

  console.log("5. User course access:", userCourseAccess ? "FOUND" : "NOT FOUND");

  // Step 6: Simulate API logic
  console.log("\n🔍 Simulating API logic...");

  // Check if user is admin
  if (user.role === 'admin') {
    console.log("✅ User is admin, bypassing membership check");
  } else {
    // Verify user is member of organization
    if (!userMembership) {
      console.log("❌ User is not a member of organization - would return 403");
      return;
    }
    console.log("✅ User is member of organization");
  }

  // Check if user has access to the course (unless admin)
  if (user.role !== 'admin') {
    if (!userCourseAccess) {
      console.log("❌ User does not have access to this course - would return 403");
      return;
    }
    console.log("✅ User has access to course");
  }

  console.log("✅ All checks passed - course would be returned successfully");
}

testCourseAPI().catch(console.error);
