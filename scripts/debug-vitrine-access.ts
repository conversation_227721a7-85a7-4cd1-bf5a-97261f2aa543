import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "postgresql://cakto:cakto@localhost:5432/cakto"
    }
  }
})

async function debugVitrineAccess() {
  console.log('🔍 Debugando acesso à vitrine...')

  // 1. Verificar usuário
  const user = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })
  console.log('1. Usuário:', user?.name, user?.email, user?.id)

  // 2. Verificar organização
  const organization = await prisma.organization.findUnique({
    where: { slug: 'techcorp-premium' }
  })
  console.log('2. Organização:', organization?.name, organization?.slug, organization?.id)

  // 3. Verificar membro
  const membership = await prisma.member.findFirst({
    where: {
      userId: user?.id,
      organizationId: organization?.id
    }
  })
  console.log('3. Membro:', membership ? 'SIM' : 'NÃO', membership?.role)

  // 4. Verificar vitrines
  const vitrines = await prisma.vitrine.findMany({
    where: {
      organizationId: organization?.id,
      status: 'PUBLISHED',
      visibility: 'PUBLIC'
    }
  })
  console.log('4. Vitrines:', vitrines.length, 'vitrines encontradas')
  vitrines.forEach(v => {
    console.log('   -', v.title, 'ID:', v.id)
  })

  // 5. Verificar seções
  if (vitrines.length > 0) {
    const sections = await prisma.vitrineSection.findMany({
      where: {
        vitrineId: vitrines[0].id
      },
      include: {
        courses: {
          include: {
            course: true
          }
        }
      }
    })
    console.log('5. Seções:', sections.length, 'seções encontradas')
    sections.forEach(s => {
      console.log('   -', s.title, 'Cursos:', s.courses.length)
    })
  }

  // 6. Verificar todos os membros da organização
  const allMembers = await prisma.member.findMany({
    where: {
      organizationId: organization?.id
    },
    include: {
      user: {
        select: {
          name: true,
          email: true
        }
      }
    }
  })
  console.log('6. Todos os membros da organização:')
  allMembers.forEach(m => {
    console.log('   -', m.user.name, m.user.email, 'Role:', m.role)
  })

  await prisma.$disconnect()
}

debugVitrineAccess()
  .catch(e => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
