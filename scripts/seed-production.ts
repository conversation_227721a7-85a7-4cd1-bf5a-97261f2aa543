#!/usr/bin/env tsx

import { PrismaClient } from "@prisma/client";
import { auth } from "@repo/auth";
import { createUser, createUserAccount, getUserByEmail } from "@repo/database";

const prisma = new PrismaClient();

async function main() {
  console.log("🚀 Iniciando seed de produção...");
  console.log("📊 Verificando ambiente:", process.env.NODE_ENV);
  console.log("🗄️ Database URL:", process.env.DATABASE_URL ? "Configurado" : "Não configurado");

  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL não configurada");
    process.exit(1);
  }

  try {
    // Testar conexão com o banco
    await prisma.$connect();
    console.log("✅ Conexão com banco estabelecida");

    // Verificar se já existem dados
    const userCount = await prisma.user.count();
    const orgCount = await prisma.organization.count();

    if (userCount > 0 || orgCount > 0) {
      console.log("⚠️ Banco já possui dados:");
      console.log(`   👥 Usuários: ${userCount}`);
      console.log(`   🏢 Organizações: ${orgCount}`);

      const shouldContinue = process.argv.includes("--force");
      if (!shouldContinue) {
        console.log("❌ Use --force para executar mesmo com dados existentes");
        process.exit(1);
      }
      console.log("🔄 Continuando com --force...");
    }

    // Executar seed
    console.log("🌱 Executando seed...");

    // Importar e executar o seed principal
    const { main: seedMain } = await import("../packages/database/prisma/seed.ts");
    await seedMain();

    console.log("✅ Seed de produção concluído com sucesso!");

  } catch (error) {
    console.error("❌ Erro durante seed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
