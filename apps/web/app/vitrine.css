/* Vitrine Netflix-style Styles */

/* Global dark theme for vitrine pages */
.vitrine-layout,
.vitrine-layout * {
    color-scheme: dark;
}

/* Unified background - now handled globally in globals.css */
.vitrine-layout {
    min-height: 100vh;
}

/* Ensure body matches the vitrine background for seamless transition */
body:has(.vitrine-layout) {
    min-height: 100vh;
}

/* Smooth hero to content transition */
.hero-to-content-transition {
    background: linear-gradient(to bottom,
            transparent 0%,
            rgba(17, 24, 39, 0.5) 50%,
            #111827 100%);
    height: 80px;
    position: relative;
    z-index: 5;
}

/* Smooth scrolling behavior */
html {
    scroll-behavior: smooth;
}

/* Netflix Carousel Styles */
.netflix-carousel-container {
    position: relative;
    padding: 12px 0;
    /* Vertical padding to accommodate hover effects */
    overflow: visible;
    /* Allow cards to extend beyond container on hover */
}

.netflix-carousel-item {
    transition: transform 0.3s ease;
    padding: 8px 0;
    /* Vertical padding for individual items */
}

/* Custom scrollbar for carousel */
.netflix-carousel::-webkit-scrollbar {
    height: 8px;
}

.netflix-carousel::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

.netflix-carousel::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
}

.netflix-carousel::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.7);
}

/* Line clamp utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Header gradient - Based on cakto-members-frontend */
.header-gradient {
    background: linear-gradient(135deg, #30004f 0%, #4a0066 25%, #5a007a 50%, #4a0066 75%, #30004f 100%);
    color: white;
    position: relative;
}

.header-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(114, 9, 183, 0.1) 0%, rgba(74, 0, 102, 0.1) 50%, rgba(114, 9, 183, 0.1) 100%);
    pointer-events: none;
    opacity: 0.1;
}

/* Notification badge pulse */
@keyframes notification-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.notification-badge {
    animation: notification-pulse 2s infinite;
}

/* ShowcaseCard specific styles - Enhanced for dark theme */
.showcase-card-wrapper {
    isolation: isolate;
    contain: layout style;
    position: relative;
    /* Prevent cropping on hover by ensuring enough space */
    padding: 6px 8px 16px 8px;
    /* Extra bottom padding for hover lift */
}

.showcase-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border-radius: 0.75rem;
    /* Remove any default padding that could create gaps */
    padding: 0 !important;
    margin: 0;
    /* Ensure card fills its container */
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #1f2937 100%);
}

/* Enhanced hover effects for better visual feedback */
.showcase-card-wrapper:hover .showcase-card {
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(147, 51, 234, 0.3);
}

/* Image container styles - Using background-image for complete coverage */
.showcase-card-image-container {
    position: relative;
    overflow: hidden;
    background-color: #374151;
    border-radius: 0.75rem;
    /* Ensure container has no padding/margin that could create gaps */
    margin: 0;
    padding: 0;
    /* Ensure container fills the card completely */
    width: 100%;
    height: 100%;
    /* Background image properties are set inline for complete coverage */
    transition: transform 0.4s ease-out;
}

/* Image scaling - enhanced for better visual impact */
.showcase-card-wrapper:hover .showcase-card-image-container {
    transform: scale(1.1);
}

/* Title overlay - Always visible with improved contrast */
.showcase-card-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    z-index: 30;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.6) 70%, transparent 100%);
    opacity: 1;
    /* Always visible */
    transition: all 0.4s ease-out;
}

/* Button overlay - Enhanced visibility and styling */
.showcase-card-button {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    z-index: 40;
    opacity: 0;
    transform: translateY(1rem);
    transition: all 0.4s ease-out;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.7) 80%, transparent 100%);
}

.showcase-card-wrapper:hover .showcase-card-button {
    opacity: 1;
    transform: translateY(0);
}

/* Hover overlay - Enhanced for dark theme */
.showcase-card-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.4s ease-out;
    z-index: 20;
}

.showcase-card-wrapper:hover .showcase-card-overlay {
    opacity: 1;
}

/* Interaction feedback overlay */
.showcase-card-interaction {
    position: absolute;
    inset: 0;
    background-color: rgba(147, 51, 234, 0.1);
    opacity: 0;
    transition: opacity 0.4s ease-out;
    pointer-events: none;
    z-index: 10;
}

.showcase-card-wrapper:hover .showcase-card-interaction {
    opacity: 1;
}

/* Prevent hover bleeding between cards */
.showcase-card-wrapper:not(:hover) .showcase-card-overlay,
.showcase-card-wrapper:not(:hover) .showcase-card-button,
.showcase-card-wrapper:not(:hover) .showcase-card-interaction {
    opacity: 0;
}

.showcase-card-wrapper:not(:hover) .showcase-card-image-container {
    transform: scale(1);
}

.showcase-card-wrapper:not(:hover) .showcase-card {
    transform: scale(1);
}

/* Ensure title remains visible when not hovering */
.showcase-card-wrapper:not(:hover) .showcase-card-title {
    opacity: 1;
}

/* Loading animations */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Notification badge animation */
.notification-badge {
    animation: pulse 2s infinite;
}

/* Floating elements animation */
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

.float-animation {
    animation: float 6s ease-in-out infinite;
}

/* Enhanced gradient text for dark theme */
.gradient-text {
    background: linear-gradient(135deg, #a855f7, #6366f1, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced button styles for dark theme */
.btn-primary {
    background: linear-gradient(135deg, #7c3aed, #4f46e5);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.4);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #6d28d9, #4338ca);
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(147, 51, 234, 0.5);
}

/* Enhanced backdrop blur utility */
.backdrop-blur-custom {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
}

/* Enhanced progress bar styles */
.progress-bar {
    background: linear-gradient(90deg, #a855f7, #6366f1);
    transition: width 0.3s ease;
}

/* Enhanced card overlay effects */
.card-overlay {
    background: linear-gradient(135deg,
            rgba(147, 51, 234, 0.15) 0%,
            rgba(79, 70, 229, 0.15) 50%,
            rgba(59, 130, 246, 0.15) 100%);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .netflix-carousel {
        padding: 0 1rem;
    }

    .netflix-carousel>* {
        min-width: 200px;
    }

    .showcase-card-wrapper {
        padding: 4px 6px 12px 6px;
    }
}

/* Enhanced focus styles for accessibility */
.focus-visible:focus-visible {
    outline: 2px solid #a855f7;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.3);
}

/* Spinner animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Global styles */
* {
    box-sizing: border-box;
}

/* Enhanced shadow utilities for dark theme */
.shadow-netflix {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
        0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.shadow-netflix-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4),
        0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* Enhanced glow effects */
.glow-on-hover {
    transition: all 0.3s ease;
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.6);
}

/* Selection styles for dark theme */
::selection {
    background: rgba(147, 51, 234, 0.4);
    color: white;
}

/* Enhanced custom scrollbar for dark theme */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.7);
}
