"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { ArrowLeftIcon, CheckCircleIcon, SearchIcon, LinkIcon, AlertCircleIcon, LoaderIcon } from "lucide-react";

import { Badge } from "@ui/components/badge";
import { toast } from "sonner";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  alreadyLinked: boolean;
}

export default function AssociateProductPage() {
  const params = useParams();
  const courseId = params.courseId as string;
  const router = useRouter();


  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [associating, setAssociating] = useState<string | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [courseName, setCourseName] = useState<string>("");

  // Carregar token de acesso do localStorage
  useEffect(() => {
    const token = localStorage.getItem("cakto_access_token");
    if (token) {
      setAccessToken(token);
    } else {
      setError("Token de acesso da Cakto não encontrado. Faça login novamente via SSO.");
    }
  }, []);

  // Carregar informações do curso
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        const response = await fetch(`/api/admin/courses/${courseId}`);
        if (!response.ok) {
          throw new Error("Erro ao carregar informações do curso");
        }
        const data = await response.json();
        setCourseName(data.name);
      } catch (err) {
        setError("Erro ao carregar informações do curso");
      }
    };

    fetchCourse();
  }, [courseId]);

  // Carregar produtos da Cakto
  useEffect(() => {
    if (!accessToken) return;

    const fetchProducts = async () => {
      setLoading(true);
      try {
        const response = await fetch("/api/admin/cakto-products", {
          headers: {
            "Authorization": `Bearer ${accessToken}`
          }
        });

        if (!response.ok) {
          throw new Error("Erro ao carregar produtos da Cakto");
        }

        const data = await response.json();
        setProducts(data);
        setFilteredProducts(data);
        setLoading(false);
      } catch (err) {
        setError("Erro ao carregar produtos da Cakto");
        setLoading(false);
      }
    };

    fetchProducts();
  }, [accessToken]);

  // Filtrar produtos com base na busca
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredProducts(products);
      return;
    }

    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setFilteredProducts(filtered);
  }, [searchQuery, products]);

  // Associar produto ao curso
  const associateProduct = async (product: Product) => {
    if (product.alreadyLinked) {
      toast.error("Produto já associado");
      return;
    }

    setAssociating(product.id);

    try {
      const response = await fetch("/api/admin/cakto-products/associate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`
        },
        body: JSON.stringify({
          courseId,
          caktoProductId: product.id,
          caktoProductName: product.name
        })
      });

      if (!response.ok) {
        throw new Error("Erro ao associar produto ao curso");
      }

      const data = await response.json();

      // Atualizar estado local
      setProducts(products.map(p =>
        p.id === product.id ? { ...p, alreadyLinked: true } : p
      ));

      setFilteredProducts(filteredProducts.map(p =>
        p.id === product.id ? { ...p, alreadyLinked: true } : p
      ));

      toast.success(`O produto "${product.name}" foi associado ao curso "${courseName}"`);
    } catch (err) {
      toast.error("Não foi possível associar o produto ao curso");
    } finally {
      setAssociating(null);
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-1"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Voltar
        </Button>
        <h1 className="text-2xl font-bold">Associar Produto ao Curso</h1>
      </div>

      {courseName && (
        <div className="text-lg mb-6">
          Curso: <span className="font-semibold">{courseName}</span>
        </div>
      )}

      {error ? (
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircleIcon className="h-5 w-5 text-destructive" />
              Erro
            </CardTitle>
            <CardDescription>
              {error}
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => router.back()}>Voltar</Button>
          </CardFooter>
        </Card>
      ) : (
        <>
          <div className="flex items-center gap-2 mb-4">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar produtos por nome ou descrição..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <LoaderIcon className="h-8 w-8 animate-spin text-primary mr-2" />
              <span>Carregando produtos...</span>
            </div>
          ) : filteredProducts.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertCircleIcon className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-1">Nenhum produto encontrado</h3>
                <p className="text-muted-foreground text-center mb-4">
                  {searchQuery ? "Tente ajustar sua busca" : "Não há produtos disponíveis para associação"}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {filteredProducts.map((product) => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="h-40 bg-muted relative">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/5 to-primary/10">
                        <LinkIcon className="h-12 w-12 text-muted-foreground" />
                      </div>
                    )}
                    {product.alreadyLinked && (
                      <div className="absolute top-2 right-2">
                        <Badge status="success" className="flex items-center gap-1">
                          <CheckCircleIcon className="h-3 w-3" />
                          Associado
                        </Badge>
                      </div>
                    )}
                  </div>
                  <CardHeader>
                    <CardTitle className="line-clamp-1">{product.name}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {product.description || "Sem descrição"}
                    </CardDescription>
                  </CardHeader>
                  <CardFooter className="flex justify-between">
                    <div className="text-sm font-medium">
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(product.price || 0)}
                    </div>
                    <Button
                      variant={product.alreadyLinked ? "outline" : "primary"}
                      size="sm"
                      disabled={associating === product.id}
                      onClick={() => associateProduct(product)}
                    >
                      {associating === product.id ? (
                        <>
                          <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                          Associando...
                        </>
                      ) : product.alreadyLinked ? (
                        <>
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          Associado
                        </>
                      ) : (
                        <>
                          <LinkIcon className="h-4 w-4 mr-2" />
                          Associar
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
