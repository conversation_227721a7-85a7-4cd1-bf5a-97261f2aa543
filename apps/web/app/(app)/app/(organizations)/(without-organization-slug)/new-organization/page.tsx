import { config } from "@repo/config";
import { getOrganizationList } from "@saas/auth/lib/server";
import { WorkspaceCreationWizard } from "@saas/organizations/components/WorkspaceCreationWizard";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function NewOrganizationPage() {
	const organizations = await getOrganizationList();

	if (
		!config.organizations.enable ||
		(!config.organizations.enableUsersToCreateOrganizations &&
			(!config.organizations.requireOrganization ||
				organizations.length > 0))
	) {
		return redirect("/app");
	}

	return <WorkspaceCreationWizard />;
}
