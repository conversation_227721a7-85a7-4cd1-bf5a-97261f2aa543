import { OrganizationWizardWrapper } from "@saas/shared/components/OrganizationWizardWrapper";

export default function OrganizationInvitationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <OrganizationWizardWrapper
      title="Convite para organização"
      subtitle="Você foi convidado para participar de uma organização no Cakto Members"
    >
      {children}
    </OrganizationWizardWrapper>
  );
}
