import { CourseCreationWizard } from '@/modules/saas/organizations/course/components/CourseCreator/CourseCreationWizard'

interface CourseCreatePageProps {
  params: Promise<{
    organizationSlug: string
  }>
}

export default async function CourseCreatePage({ params }: CourseCreatePageProps) {
  const { organizationSlug } = await params

  return (
    <CourseCreationWizard
      organizationSlug={organizationSlug}
    />
  )
}
