import { getActiveOrganization, getSession } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { BookOpenIcon, PlusIcon, SettingsIcon, Users2Icon } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import Link from "next/link";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("app.menu.courses"),
	};
}

export default async function CoursesPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const t = await getTranslations();
	const session = await getSession();
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		redirect("/app");
	}

	// Mock data for courses - in real implementation, fetch from API
	const courses = [
		{
			id: "1",
			title: "Curso de React Avançado",
			description: "Aprenda React com conceitos avançados e melhores práticas",
			studentsCount: 156,
			lessonsCount: 24,
			status: "published",
		},
		{
			id: "2",
			title: "JavaScript Moderno",
			description: "ES6+, async/await, modules e muito mais",
			studentsCount: 89,
			lessonsCount: 18,
			status: "draft",
		},
	];

	return (
		<>
			<div className="flex items-center justify-between mb-8">
				<PageHeader
					title={t("app.menu.courses")}
					subtitle="Gerencie os cursos da sua área de membros"
				/>
				<Button asChild>
					<Link href={`/app/${organizationSlug}/courses/new`}>
						<PlusIcon className="size-4 mr-2" />
						Criar Curso
					</Link>
				</Button>
			</div>


			<div className="container py-8">
				<div className="grid gap-6">
					{courses.length === 0 ? (
						<Card>
							<CardContent className="flex flex-col items-center justify-center py-12">
								<BookOpenIcon className="size-12 text-muted-foreground mb-4" />
								<CardTitle className="mb-2">Nenhum curso criado ainda</CardTitle>
								<CardDescription className="text-center mb-6">
									Comece criando seu primeiro curso para disponibilizar conteúdo aos seus membros.
								</CardDescription>
								<Button asChild>
									<Link href={`/app/${organizationSlug}/courses/new`}>
										<PlusIcon className="size-4 mr-2" />
										Criar Primeiro Curso
									</Link>
								</Button>
							</CardContent>
						</Card>
					) : (
						<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
							{courses.map((course) => (
								<Card key={course.id} className="hover:shadow-md transition-shadow">
									<CardHeader>
										<div className="flex items-start justify-between">
											<CardTitle className="text-lg">{course.title}</CardTitle>
											<div className="flex gap-1">
												<Button variant="ghost" size="sm" asChild>
													<Link href={`/app/${organizationSlug}/courses/${course.id}/settings`}>
														<SettingsIcon className="size-4" />
													</Link>
												</Button>
											</div>
										</div>
										<CardDescription>{course.description}</CardDescription>
									</CardHeader>
									<CardContent>
										<div className="flex items-center justify-between text-sm text-muted-foreground">
											<div className="flex items-center gap-1">
												<Users2Icon className="size-4" />
												{course.studentsCount} alunos
											</div>
											<div className="flex items-center gap-1">
												<BookOpenIcon className="size-4" />
												{course.lessonsCount} aulas
											</div>
										</div>
										<div className="mt-4 flex gap-2">
											<Button variant="outline" size="sm" asChild className="flex-1">
												<Link href={`/app/${organizationSlug}/courses/${course.id}`}>
													Ver Curso
												</Link>
											</Button>
											<Button size="sm" asChild className="flex-1">
												<Link href={`/app/${organizationSlug}/courses/${course.id}/edit`}>
													Editar
												</Link>
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</div>
			</div>
		</>
	);
}
