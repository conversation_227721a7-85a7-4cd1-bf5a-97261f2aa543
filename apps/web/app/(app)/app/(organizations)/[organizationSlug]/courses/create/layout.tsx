'use client'

import { Suspense } from 'react'
import { Card } from '@/modules/ui/components/card'
import { Skeleton } from '@/modules/ui/components/skeleton'

interface CourseCreateLayoutProps {
  children: React.ReactNode
  params: Promise<{
    organizationSlug: string
  }>
}

export default async function CourseCreateLayout({ children, params }: CourseCreateLayoutProps) {
  const { organizationSlug } = await params

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="font-bold text-2xl lg:text-3xl mb-2">
              Criar Novo Curso
            </h1>
            <p className="mt-1 opacity-60">
              Configure seu curso seguindo as etapas abaixo
            </p>
          </div>

          <Card className="p-6">
            <Suspense fallback={
              <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-8 w-32" />
              </div>
            }>
              {children}
            </Suspense>
          </Card>
        </div>
      </div>
    </div>
  )
}
