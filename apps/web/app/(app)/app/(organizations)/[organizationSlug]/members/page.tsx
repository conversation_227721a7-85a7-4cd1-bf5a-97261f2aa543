import { getActiveOrganization, getSession } from "@saas/auth/lib/server";
import { OrganizationMembersList } from "@saas/organizations/components/OrganizationMembersList";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("app.menu.members"),
	};
}

export default async function MembersPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const session = await getSession();
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		redirect("/app");
	}

	return <OrganizationMembersList organizationId={organization.id} />;
}
