interface CourseSettingsPageProps {
  params: Promise<{
    organizationSlug: string
    courseId: string
  }>
}

export default async function CourseSettingsPage({ params }: CourseSettingsPageProps) {
  const { organizationSlug, courseId } = await params

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Configurações</h2>
        <p className="text-gray-600">Configure as opções avançadas do curso</p>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <p className="text-gray-500">Funcionalidade em desenvolvimento...</p>
      </div>
    </div>
  )
}
