import { CourseEditor } from '@/modules/saas/organizations/course/components/CourseEditor/CourseEditor'

interface CourseContentEditPageProps {
  params: Promise<{
    organizationSlug: string
    courseId: string
  }>
}

export default async function CourseContentEditPage({ params }: CourseContentEditPageProps) {
  const { organizationSlug, courseId } = await params

  return (
    <CourseEditor
      courseId={courseId}
      organizationSlug={organizationSlug}
      mode="edit"
    />
  )
}
