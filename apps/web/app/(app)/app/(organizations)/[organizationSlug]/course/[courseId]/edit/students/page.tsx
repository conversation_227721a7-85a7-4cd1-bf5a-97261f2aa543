interface CourseStudentsPageProps {
  params: Promise<{
    organizationSlug: string
    courseId: string
  }>
}

export default async function CourseStudentsPage({ params }: CourseStudentsPageProps) {
  const { organizationSlug, courseId } = await params

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Estudantes</h2>
        <p className="text-gray-600">Gerencie os estudantes matriculados no curso</p>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <p className="text-gray-500">Funcionalidade em desenvolvimento...</p>
      </div>
    </div>
  )
}
