import { Suspense } from 'react'
import { Skeleton } from '@/modules/ui/components/skeleton'

interface CourseEditLayoutProps {
  children: React.ReactNode
  params: Promise<{
    organizationSlug: string
    courseId: string
  }>
}

export default async function CourseEditLayout({ children, params }: CourseEditLayoutProps) {
  const { organizationSlug, courseId } = await params

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 py-6">
        <Suspense fallback={<Skeleton className="h-8 w-64 mb-6" />}>
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Editar Curso</h1>
            <p className="text-gray-600">Gerencie o conteúdo e configurações do seu curso</p>
          </div>
        </Suspense>

        <Suspense fallback={<Skeleton className="h-96 w-full" />}>
          {children}
        </Suspense>
      </div>
    </div>
  )
}
