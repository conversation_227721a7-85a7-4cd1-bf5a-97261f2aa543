import { VitrinePage } from "@saas/organizations/vitrine/components/VitrinePage";
import "@/app/vitrine.css";
import { Footer } from "@/modules/saas/shared/components/Footer";
import { UnifiedNavbar } from "@/modules/saas/shared/components/UnifiedNavbar";

interface VitrinePageProps {
	params: Promise<{
		organizationSlug: string;
	}>;
}

export default async function OrganizationVitrinePage({
	params,
}: VitrinePageProps) {
	const { organizationSlug } = await params;

	return (
		<>
			<UnifiedNavbar />
			<div className="vitrine-layout min-h-screen">
				<VitrinePage organizationSlug={organizationSlug} />
			</div>
			<Footer />
		</>
	);
}
