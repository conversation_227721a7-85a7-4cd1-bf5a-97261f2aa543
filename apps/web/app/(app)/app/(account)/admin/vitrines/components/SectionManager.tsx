"use client";

import { useState, useCallback } from "react";
import { Drag<PERSON><PERSON><PERSON>ontext, Droppable, Draggable } from "@hello-pangea/dnd";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { Switch } from "@ui/components/switch";
import {
	PlusIcon,
	GripVerticalIcon,
	EditIcon,
	TrashIcon,
	PackageIcon,
	LockIcon,
	UnlockIcon,
	DollarSignIcon,
	UsersIcon,
} from "lucide-react";
import { toast } from "sonner";

interface VitrineSection {
	id: string;
	title: string;
	subtitle?: string;
	description?: string;
	position: number;
	isLocked: boolean;
	requiresPurchase: boolean;
	checkoutUrl?: string;
	webhookUrl?: string;
	price?: number;
	originalPrice?: number;
	accessType: "FREE" | "PAID" | "MEMBER_ONLY";
	visibility: "PUBLIC" | "PRIVATE";
	courses: any[];
}

interface SectionManagerProps {
	sections: VitrineSection[];
	onUpdate: (sections: VitrineSection[]) => void;
}

interface SectionFormData {
	title: string;
	subtitle: string;
	description: string;
	accessType: "FREE" | "PAID" | "MEMBER_ONLY";
	visibility: "PUBLIC" | "PRIVATE";
	isLocked: boolean;
	requiresPurchase: boolean;
	price: number | null;
	originalPrice: number | null;
	checkoutUrl: string;
	webhookUrl: string;
}

export function SectionManager({ sections, onUpdate }: SectionManagerProps) {
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [editingSection, setEditingSection] = useState<VitrineSection | null>(null);
	const [formData, setFormData] = useState<SectionFormData>({
		title: "",
		subtitle: "",
		description: "",
		accessType: "FREE",
		visibility: "PUBLIC",
		isLocked: false,
		requiresPurchase: false,
		price: null,
		originalPrice: null,
		checkoutUrl: "",
		webhookUrl: "",
	});

	const resetForm = useCallback(() => {
		setFormData({
			title: "",
			subtitle: "",
			description: "",
			accessType: "FREE",
			visibility: "PUBLIC",
			isLocked: false,
			requiresPurchase: false,
			price: null,
			originalPrice: null,
			checkoutUrl: "",
			webhookUrl: "",
		});
		setEditingSection(null);
	}, []);

	const openCreateDialog = useCallback(() => {
		resetForm();
		setIsDialogOpen(true);
	}, [resetForm]);

	const openEditDialog = useCallback((section: VitrineSection) => {
		setFormData({
			title: section.title,
			subtitle: section.subtitle || "",
			description: section.description || "",
			accessType: section.accessType,
			visibility: section.visibility,
			isLocked: section.isLocked,
			requiresPurchase: section.requiresPurchase,
			price: section.price || null,
			originalPrice: section.originalPrice || null,
			checkoutUrl: section.checkoutUrl || "",
			webhookUrl: section.webhookUrl || "",
		});
		setEditingSection(section);
		setIsDialogOpen(true);
	}, []);

	const handleSaveSection = useCallback(() => {
		if (!formData.title.trim()) {
			toast.error("Título da seção é obrigatório");
			return;
		}

		if (formData.accessType === "PAID" && !formData.price) {
			toast.error("Preço é obrigatório para seções pagas");
			return;
		}

		const sectionData: VitrineSection = {
			id: editingSection?.id || `section-${Date.now()}`,
			title: formData.title.trim(),
			subtitle: formData.subtitle.trim() || undefined,
			description: formData.description.trim() || undefined,
			position: editingSection?.position || sections.length,
			isLocked: formData.isLocked,
			requiresPurchase: formData.requiresPurchase,
			checkoutUrl: formData.checkoutUrl.trim() || undefined,
			webhookUrl: formData.webhookUrl.trim() || undefined,
			price: formData.price || undefined,
			originalPrice: formData.originalPrice || undefined,
			accessType: formData.accessType,
			visibility: formData.visibility,
			courses: editingSection?.courses || [],
		};

		let updatedSections: VitrineSection[];
		if (editingSection) {
			updatedSections = sections.map((section) =>
				section.id === editingSection.id ? sectionData : section
			);
		} else {
			updatedSections = [...sections, sectionData];
		}

		onUpdate(updatedSections);
		setIsDialogOpen(false);
		resetForm();
		toast.success(editingSection ? "Seção atualizada!" : "Seção criada!");
	}, [formData, editingSection, sections, onUpdate, resetForm]);

	const handleDeleteSection = useCallback((sectionId: string) => {
		if (confirm("Tem certeza que deseja excluir esta seção?")) {
			const updatedSections = sections.filter((section) => section.id !== sectionId);
			// Reorder positions
			const reorderedSections = updatedSections.map((section, index) => ({
				...section,
				position: index,
			}));
			onUpdate(reorderedSections);
			toast.success("Seção excluída!");
		}
	}, [sections, onUpdate]);

	const handleDragEnd = useCallback((result: any) => {
		if (!result.destination) return;

		const items = Array.from(sections);
		const [reorderedItem] = items.splice(result.source.index, 1);
		items.splice(result.destination.index, 0, reorderedItem);

		// Update positions
		const reorderedSections = items.map((section, index) => ({
			...section,
			position: index,
		}));

		onUpdate(reorderedSections);
	}, [sections, onUpdate]);

	const getAccessTypeBadge = (accessType: string) => {
		switch (accessType) {
			case "FREE":
				return <Badge className="bg-green-100 text-green-800 border-green-200">Gratuito</Badge>;
			case "PAID":
				return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Pago</Badge>;
			case "MEMBER_ONLY":
				return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Só Membros</Badge>;
			default:
				return <Badge>{accessType}</Badge>;
		}
	};

	const getVisibilityBadge = (visibility: string) => {
		switch (visibility) {
			case "PUBLIC":
				return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Público</Badge>;
			case "PRIVATE":
				return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Privado</Badge>;
			default:
				return <Badge>{visibility}</Badge>;
		}
	};

	return (
		<div className="max-w-4xl mx-auto space-y-6">
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<PackageIcon className="h-5 w-5 text-primary" />
								Gerenciar Seções
							</CardTitle>
							<p className="text-muted-foreground">
								Organize seus cursos em seções temáticas
							</p>
						</div>
						<Button onClick={openCreateDialog}>
							<PlusIcon className="mr-2 h-4 w-4" />
							Nova Seção
						</Button>
					</div>
				</CardHeader>
				<CardContent>
					{sections.length === 0 ? (
						<div className="text-center py-12">
							<PackageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
							<h3 className="text-lg font-semibold mb-2">Nenhuma seção criada</h3>
							<p className="text-muted-foreground mb-6">
								Crie seções para organizar seus cursos de forma temática
							</p>

						</div>
					) : (
						<DragDropContext onDragEnd={handleDragEnd}>
							<Droppable droppableId="sections">
								{(provided) => (
									<div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
										{sections.map((section, index) => (
											<Draggable key={section.id} draggableId={section.id} index={index}>
												{(provided, snapshot) => (
													<Card
														ref={provided.innerRef}
														{...provided.draggableProps}
														className={`transition-shadow ${
															snapshot.isDragging ? "shadow-lg" : ""
														}`}
													>
														<CardContent className="p-4">
															<div className="flex items-start gap-4">
																<div
																	{...provided.dragHandleProps}
																	className="mt-2 cursor-grab active:cursor-grabbing"
																>
																	<GripVerticalIcon className="h-5 w-5 text-muted-foreground" />
																</div>

																<div className="flex-1 space-y-3">
																	<div className="flex items-start justify-between">
																		<div>
																			<h3 className="font-semibold text-lg">{section.title}</h3>
																			{section.subtitle && (
																				<p className="text-muted-foreground">{section.subtitle}</p>
																			)}
																		</div>
																		<div className="flex gap-2">
																			<Button
																				variant="outline"
																				size="sm"
																				onClick={() => openEditDialog(section)}
																			>
																				<EditIcon className="h-4 w-4" />
																			</Button>
																			<Button
																				variant="outline"
																				size="sm"
																				onClick={() => handleDeleteSection(section.id)}
																				className="text-red-600 hover:text-red-700"
																			>
																				<TrashIcon className="h-4 w-4" />
																			</Button>
																		</div>
																	</div>

																	{section.description && (
																		<p className="text-sm text-muted-foreground">
																			{section.description}
																		</p>
																	)}

																	<div className="flex flex-wrap gap-2">
																		{getAccessTypeBadge(section.accessType)}
																		{getVisibilityBadge(section.visibility)}
																		{section.isLocked && (
																			<Badge className="bg-red-100 text-red-800 border-red-200">
																				<LockIcon className="mr-1 h-3 w-3" />
																				Bloqueado
																			</Badge>
																		)}
																		{section.requiresPurchase && (
																			<Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
																				<DollarSignIcon className="mr-1 h-3 w-3" />
																				Requer Compra
																			</Badge>
																		)}
																		{section.price && (
																			<Badge className="bg-blue-100 text-blue-800 border-blue-200">
																				R$ {section.price.toFixed(2)}
																			</Badge>
																		)}
																	</div>

																	<div className="text-sm text-muted-foreground">
																		{section.courses.length} curso(s) adicionado(s)
																	</div>
																</div>
															</div>
														</CardContent>
													</Card>
												)}
											</Draggable>
										))}
										{provided.placeholder}
									</div>
								)}
							</Droppable>
						</DragDropContext>
					)}


				</CardContent>
			</Card>

			{/* Section Form Dialog */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>
							{editingSection ? "Editar Seção" : "Nova Seção"}
						</DialogTitle>
					</DialogHeader>

					<div className="space-y-4 max-h-[60vh] overflow-y-auto">
						<div>
							<label className="text-sm font-medium">Título *</label>
							<Input
								value={formData.title}
								onChange={(e) => setFormData({ ...formData, title: e.target.value })}
								placeholder="Ex: Módulo Básico"
								className="mt-1"
							/>
						</div>

						<div>
							<label className="text-sm font-medium">Subtítulo</label>
							<Input
								value={formData.subtitle}
								onChange={(e) => setFormData({ ...formData, subtitle: e.target.value })}
								placeholder="Breve descrição da seção"
								className="mt-1"
							/>
						</div>

						<div>
							<label className="text-sm font-medium">Descrição</label>
							<Textarea
								value={formData.description}
								onChange={(e) => setFormData({ ...formData, description: e.target.value })}
								placeholder="Descrição detalhada da seção"
								className="mt-1"
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div>
								<label className="text-sm font-medium">Tipo de Acesso</label>
								<Select
									value={formData.accessType}
									onValueChange={(value: "FREE" | "PAID" | "MEMBER_ONLY") =>
										setFormData({ ...formData, accessType: value })
									}
								>
									<SelectTrigger className="mt-1">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="FREE">Gratuito</SelectItem>
										<SelectItem value="PAID">Pago</SelectItem>
										<SelectItem value="MEMBER_ONLY">Só Membros</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div>
								<label className="text-sm font-medium">Visibilidade</label>
								<Select
									value={formData.visibility}
									onValueChange={(value: "PUBLIC" | "PRIVATE") =>
										setFormData({ ...formData, visibility: value })
									}
								>
									<SelectTrigger className="mt-1">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="PUBLIC">Público</SelectItem>
										<SelectItem value="PRIVATE">Privado</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						{formData.accessType === "PAID" && (
							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="text-sm font-medium">Preço (R$) *</label>
									<Input
										type="number"
										value={formData.price || ""}
										onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) || null })}
										placeholder="99.90"
										className="mt-1"
									/>
								</div>
								<div>
									<label className="text-sm font-medium">Preço Original (R$)</label>
									<Input
										type="number"
										value={formData.originalPrice || ""}
										onChange={(e) => setFormData({ ...formData, originalPrice: Number(e.target.value) || null })}
										placeholder="199.90"
										className="mt-1"
									/>
								</div>
							</div>
						)}

						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<label className="text-sm font-medium">Seção Bloqueada</label>
								<Switch
									checked={formData.isLocked}
									onCheckedChange={(checked) => setFormData({ ...formData, isLocked: checked })}
								/>
							</div>

							<div className="flex items-center justify-between">
								<label className="text-sm font-medium">Requer Compra</label>
								<Switch
									checked={formData.requiresPurchase}
									onCheckedChange={(checked) => setFormData({ ...formData, requiresPurchase: checked })}
								/>
							</div>
						</div>

						{formData.requiresPurchase && (
							<div className="space-y-4">
								<div>
									<label className="text-sm font-medium">URL de Checkout</label>
									<Input
										value={formData.checkoutUrl}
										onChange={(e) => setFormData({ ...formData, checkoutUrl: e.target.value })}
										placeholder="https://pay.cakto.com.br/..."
										className="mt-1"
									/>
								</div>

							</div>
						)}
					</div>

					<div className="flex justify-end gap-2 pt-4">
						<Button variant="outline" onClick={() => setIsDialogOpen(false)}>
							Cancelar
						</Button>
						<Button onClick={handleSaveSection}>
							{editingSection ? "Atualizar" : "Criar"} Seção
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
