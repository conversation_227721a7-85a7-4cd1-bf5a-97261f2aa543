"use client";

import { use<PERSON>allback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";

import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	FormDescription,
} from "@ui/components/form";
import { Badge } from "@ui/components/badge";
import { ImageIcon, InfoIcon, StarIcon } from "lucide-react";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useOrganizationListQuery } from "@saas/organizations/lib/api";
import { Switch } from "@/modules/ui/components/switch";
import { VitrineBannerUpload } from "./VitrineBannerUpload";
import type { VitrineFormData } from "../create/types";

const vitrineSchema = z.object({
	title: z.string().min(1, "Título é obrigatório").max(100, "Título deve ter no máximo 100 caracteres"),
	description: z.string().optional(),
	bannerImage: z.string().optional().or(z.literal("")),
	organizationId: z.string().min(1, "Organização é obrigatória"),
	visibility: z.enum(["PUBLIC", "PRIVATE"]),
	isDefault: z.boolean(),
});

type VitrineBasicFormData = z.infer<typeof vitrineSchema>;

interface VitrineFormProps {
	data: VitrineFormData;
	onUpdate: (data: Partial<VitrineFormData>) => void;
}

export function VitrineForm({ data, onUpdate }: VitrineFormProps) {
	const { activeOrganization, loaded } = useActiveOrganization();
	const { data: organizations, isLoading: organizationsLoading } = useOrganizationListQuery();

	const form = useForm<VitrineBasicFormData>({
		resolver: zodResolver(vitrineSchema),
		defaultValues: {
			title: data.title || "",
			description: data.description || "",
			bannerImage: data.bannerImage || "",
			organizationId: data.organizationId || "",
			visibility: data.visibility || "PUBLIC",
			isDefault: data.isDefault || false,
		},
	});

	// Auto-select active organization when loaded
	useEffect(() => {
		if (loaded && activeOrganization && !data.organizationId) {
			const organizationId = activeOrganization.id;
			form.setValue("organizationId", organizationId);
			onUpdate({ organizationId });
		}
	}, [loaded, activeOrganization, data.organizationId, form, onUpdate]);

	const onSubmit = useCallback((formData: VitrineBasicFormData) => {
		onUpdate(formData);
	}, [onUpdate]);

	const handleFieldChange = useCallback((field: keyof VitrineBasicFormData, value: any) => {
		onUpdate({ [field]: value });
	}, [onUpdate]);

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<InfoIcon className="h-5 w-5 text-primary" />
						Informações Básicas
					</CardTitle>
					<p className="text-muted-foreground">
						Configure as informações principais da sua vitrine
					</p>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<FormField
								control={form.control}
								name="title"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Título da Vitrine *</FormLabel>
										<FormControl>
											<Input
												placeholder="Ex: Cursos de Programação"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													handleFieldChange("title", e.target.value);
												}}
											/>
										</FormControl>
										<FormDescription>
											Este será o título principal exibido na vitrine
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="description"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Descrição</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Descreva brevemente o que os visitantes encontrarão nesta vitrine..."
												className="min-h-[100px]"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													handleFieldChange("description", e.target.value);
												}}
											/>
										</FormControl>
										<FormDescription>
											Uma descrição atrativa ajuda os visitantes a entenderem o conteúdo
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="organizationId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Organização *</FormLabel>
										{activeOrganization ? (
											<div className="flex items-center gap-2 p-3 border rounded-md bg-muted">
												<Badge className="bg-primary/10 text-primary">
													{activeOrganization.name}
												</Badge>
												<span className="text-sm text-muted-foreground">
													Organização ativa selecionada automaticamente
												</span>
											</div>
										) : organizationsLoading ? (
											<div className="p-3 border rounded-md bg-muted">
												<span className="text-sm text-muted-foreground">
													Carregando organizações...
												</span>
											</div>
										) : organizations && organizations.length > 0 ? (
											<Select
												value={field.value}
												onValueChange={(value) => {
													field.onChange(value);
													handleFieldChange("organizationId", value);
												}}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Selecione uma organização" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{organizations.map((org) => (
														<SelectItem key={org.id} value={org.id}>
															{org.name}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										) : (
											<div className="p-3 border rounded-md bg-muted">
												<span className="text-sm text-muted-foreground">
													Nenhuma organização encontrada
												</span>
											</div>
										)}
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="bannerImage"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Imagem de Banner</FormLabel>
										<FormControl>
											<VitrineBannerUpload
												organizationId={form.getValues("organizationId") || ""}
												currentImageUrl={field.value}
												onImageUploaded={(url) => {
													field.onChange(url);
													handleFieldChange("bannerImage", url);
												}}
												onImageRemoved={() => {
													field.onChange("");
													handleFieldChange("bannerImage", "");
												}}
											/>
										</FormControl>
										<FormDescription>
											Faça upload de uma imagem para ser exibida como banner da vitrine
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="visibility"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Visibilidade</FormLabel>
											<Select
												value={field.value}
												onValueChange={(value) => {
													field.onChange(value);
													handleFieldChange("visibility", value as "PUBLIC" | "PRIVATE");
												}}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="PUBLIC">
														<div className="flex items-center gap-2">
															<div className="w-2 h-2 bg-green-500 rounded-full" />
															Pública
														</div>
													</SelectItem>
													<SelectItem value="PRIVATE">
														<div className="flex items-center gap-2">
															<div className="w-2 h-2 bg-orange-500 rounded-full" />
															Privada
														</div>
													</SelectItem>
												</SelectContent>
											</Select>
											<FormDescription>
												Vitrines públicas são visíveis para todos os visitantes
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="isDefault"
									render={({ field }) => (
										<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
											<div className="space-y-0.5">
												<FormLabel className="text-base flex items-center gap-2">
													<StarIcon className="h-4 w-4 text-yellow-500" />
													Vitrine Padrão
												</FormLabel>
												<FormDescription>
													Esta será a vitrine principal da organização
												</FormDescription>
											</div>
											<FormControl>
												<Switch
													checked={field.value}
													onCheckedChange={(checked) => {
														field.onChange(checked);
														handleFieldChange("isDefault", checked);
													}}
												/>
											</FormControl>
										</FormItem>
									)}
								/>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
