import { useState, useEffect } from "react";
import { apiClient } from "@shared/lib/api-client";

interface Course {
	id: string;
	name: string;
	logo?: string;
	community?: string;
	description?: string;
	studentsCount?: number;
	duration?: string;
}

export function useCourses(organizationId: string) {
	const [courses, setCourses] = useState<Course[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchCourses = async () => {
			if (!organizationId) {
				setIsLoading(false);
				return;
			}

			try {
				setIsLoading(true);
				setError(null);

				const response = await apiClient.courses.organization.$get({
					query: {
						organizationId,
					},
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error((errorData as any).error || "Failed to fetch courses");
				}

				const coursesData = await response.json();
				// Transform the data to match the Course interface
				const transformedData = coursesData.map((course: any) => ({
					...course,
					logo: course.logo || undefined, // Convert null to undefined
					community: course.community || undefined, // Convert null to undefined
				}));
				setCourses(transformedData);
			} catch (err) {
				console.error("Error fetching courses:", err);
				setError(err instanceof Error ? err.message : "Failed to fetch courses");
			} finally {
				setIsLoading(false);
			}
		};

		fetchCourses();
	}, [organizationId]);

	return {
		courses,
		isLoading,
		error,
		refetch: () => {
			if (organizationId) {
				const fetchCourses = async () => {
					try {
						setIsLoading(true);
						setError(null);

						const response = await apiClient.courses.organization.$get({
							query: {
								organizationId,
							},
						});

						if (!response.ok) {
							const errorData = await response.json();
							throw new Error((errorData as any).error || "Failed to fetch courses");
						}

						const coursesData = await response.json();
						// Transform the data to match the Course interface
						const transformedData = coursesData.map((course: any) => ({
							...course,
							logo: course.logo || undefined, // Convert null to undefined
							community: course.community || undefined, // Convert null to undefined
						}));
						setCourses(transformedData);
					} catch (err) {
						console.error("Error fetching courses:", err);
						setError(err instanceof Error ? err.message : "Failed to fetch courses");
					} finally {
						setIsLoading(false);
					}
				};

				fetchCourses();
			}
		},
	};
}
