"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Textarea } from "@ui/components/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	EyeIcon,
	CheckIcon,
	SaveIcon,
	LockIcon,
	DollarSignIcon,
	UsersIcon,
	BookOpenIcon,
	GlobeIcon,
	ShieldIcon,
	StarIcon,
} from "lucide-react";
import { motion } from "framer-motion";
import type { VitrineFormData, VitrineSection, VitrineSectionCourse } from "./types";
import { VitrinePreviewModal } from "./VitrinePreviewModal";
import { HeroBannerFrontend } from "@saas/organizations/vitrine/components/HeroBannerFrontend";
import { NetflixCarousel } from "@saas/organizations/vitrine/components/NetflixCarousel";
import { ShowcaseItem } from "@saas/organizations/vitrine/types";

interface VitrinePreviewProps {
	data: VitrineFormData;
	onSave: (asDraft?: boolean) => Promise<void>;
	isLoading: boolean;
}

export function VitrinePreview({ data, onSave, isLoading }: VitrinePreviewProps) {
	const [publishStatus, setPublishStatus] = useState<"DRAFT" | "PUBLISHED">(
		data.status === "ARCHIVED" ? "DRAFT" : data.status
	);
	const [publishNotes, setPublishNotes] = useState("");
	const [previewModalOpen, setPreviewModalOpen] = useState(false);

	const totalCourses = data.sections.reduce((acc, section) => acc + section.courses.length, 0);
	const freeSections = data.sections.filter(section => section.accessType === "FREE").length;
	const paidSections = data.sections.filter(section => section.accessType === "PAID").length;

	const convertToShowcaseItems = (section: VitrineSection): ShowcaseItem[] => {
		return section.courses.map(course => ({
			id: course.courseId,
			title: course.course?.name || `Curso ${course.courseId}`,
			image: course.course?.logo || "/images/cards/card1.jpg",
			description: course.course?.community || "Curso disponível na plataforma",
			category: section.title,
			progress: 0,
			duration: "2h 30min",
			studentsCount: 100,
			isLocked: section.isLocked,
			sectionLocked: section.isLocked,
			sectionId: section.id,
			isContinueWatching: false,
		}));
	};

	const handleHeroPlay = () => {
		console.log("Hero play clicked - preview mode");
	};

	const handleHeroInfo = () => {
		console.log("Hero info clicked - preview mode");
	};

	const handlePlay = (item: ShowcaseItem) => {
		console.log("Course play clicked - preview mode:", item.title);
	};

	const handlePurchase = (item: ShowcaseItem) => {
		console.log("Course purchase clicked - preview mode:", item.title);
	};

	const getAccessTypeBadge = (accessType: string) => {
		switch (accessType) {
			case "FREE":
				return <Badge className="bg-green-100 text-green-800 border-green-200 flex items-center">Gratuito</Badge>;
			case "PAID":
				return <Badge className="bg-blue-100 text-blue-800 border-blue-200 flex items-center">Pago</Badge>;
			case "MEMBER_ONLY":
				return <Badge className="bg-purple-100 text-purple-800 border-purple-200 flex items-center">Só Membros</Badge>;
			default:
				return <Badge className="flex items-center">{accessType}</Badge>;
		}
	};

	const getVisibilityBadge = (visibility: string) => {
		switch (visibility) {
			case "PUBLIC":
				return (
					<Badge className="bg-gray-100 text-gray-800 border-gray-200 flex items-center">
						<GlobeIcon className="mr-1 h-3 w-3" />
						Público
					</Badge>
				);
			case "PRIVATE":
				return (
					<Badge className="bg-orange-100 text-orange-800 border-orange-200 flex items-center">
						<ShieldIcon className="mr-1 h-3 w-3" />
						Privado
					</Badge>
				);
			default:
				return <Badge className="flex items-center">{visibility}</Badge>;
		}
	};

	return (
		<div className="w-full mx-auto space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<EyeIcon className="h-5 w-5 text-primary" />
						Visualizar e Publicar
					</CardTitle>
					<p className="text-muted-foreground">
						Revise sua vitrine antes de publicar. Esta é uma prévia exata de como ficará para os usuários.
					</p>
				</CardHeader>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Prévia da Vitrine (Visual Final)</CardTitle>
					<p className="text-muted-foreground">
						Esta é exatamente como sua vitrine aparecerá para os visitantes
					</p>
				</CardHeader>
				<CardContent className="p-0">
					<div className="bg-black text-white rounded-lg overflow-hidden">
						<HeroBannerFrontend
							title={data.title}
							subtitle="Cursos e treinamentos"
							description={data.description || "Explore nossos cursos especializados"}
							backgroundImage={data.bannerImage || "/images/banner1.jpg"}
							rating={4.8}
							duration="50h+ de conteúdo"
							studentsCount={totalCourses * 100}
							onPlay={handleHeroPlay}
							onInfo={handleHeroInfo}
						/>

						<div className="container mx-auto px-4 py-16 space-y-16">
							{data.sections.map((section, index) => {
								const showcaseItems = convertToShowcaseItems(section);

								if (showcaseItems.length === 0) return null;

								return (
									<motion.div
										key={section.id}
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ delay: index * 0.1 }}
									>
										<NetflixCarousel
											title={section.title}
											subtitle={section.subtitle}
											items={showcaseItems}
											onPlay={handlePlay}
											onPurchase={handlePurchase}
										/>
									</motion.div>
								);
							})}
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Resumo da Vitrine</CardTitle>
				</CardHeader>
				<CardContent className="space-y-6">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="md:col-span-2">
							<h3 className="text-xl font-bold mb-2">{data.title}</h3>
							{data.description && (
								<p className="text-muted-foreground mb-4">{data.description}</p>
							)}
							<div className="flex flex-wrap items-center gap-2">
								{getVisibilityBadge(data.visibility)}
								<Badge className="bg-blue-100 text-blue-800 border-blue-200">
									{data.sections.length} seção(ões)
								</Badge>
								<Badge className="bg-purple-100 text-purple-800 border-purple-200">
									{totalCourses} curso(s)
								</Badge>
								{data.isDefault && (
									<Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 flex items-center">
										<StarIcon className="mr-1 h-3 w-3" />
										Vitrine Padrão
									</Badge>
								)}
							</div>
						</div>
						{data.bannerImage && (
							<div className="aspect-video rounded-lg overflow-hidden bg-muted">
								<img
									src={data.bannerImage}
									alt="Banner da vitrine"
									className="w-full h-full object-cover"
								/>
							</div>
						)}
					</div>

					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-primary mb-1">{data.sections.length}</div>
								<div className="text-sm text-muted-foreground">Seções</div>
							</CardContent>
						</Card>
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-green-600 mb-1">{freeSections}</div>
								<div className="text-sm text-muted-foreground">Gratuitas</div>
							</CardContent>
						</Card>
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-blue-600 mb-1">{paidSections}</div>
								<div className="text-sm text-muted-foreground">Pagas</div>
							</CardContent>
						</Card>
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-purple-600 mb-1">{totalCourses}</div>
								<div className="text-sm text-muted-foreground">Cursos Total</div>
							</CardContent>
						</Card>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Seções da Vitrine</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{data.sections.map((section, index) => (
						<Card key={section.id} className="border-l-4 border-l-primary">
							<CardContent className="p-4">
								<div className="flex items-start justify-between mb-3">
									<div className="flex-1">
										<div className="flex items-center gap-3 mb-2">
											<h4 className="font-semibold text-lg">{section.title}</h4>
											<Badge className="bg-gray-100 text-gray-800 border-gray-200">
												Posição {section.position + 1}
											</Badge>
										</div>
										{section.subtitle && (
											<p className="text-muted-foreground mb-1">{section.subtitle}</p>
										)}
										{section.description && (
											<p className="text-sm text-muted-foreground">{section.description}</p>
										)}
									</div>
								</div>

								<div className="flex flex-wrap items-center gap-2 mb-4">
									{getAccessTypeBadge(section.accessType)}
									{getVisibilityBadge(section.visibility)}
									{section.isLocked && (
										<Badge className="bg-red-100 text-red-800 border-red-200 flex items-center">
											<LockIcon className="mr-1 h-3 w-3" />
											Bloqueado
										</Badge>
									)}
									{section.requiresPurchase && (
										<Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 flex items-center">
											<DollarSignIcon className="mr-1 h-3 w-3" />
											Requer Compra
										</Badge>
									)}
									{section.price && (
										<Badge className="bg-blue-100 text-blue-800 border-blue-200">
											R$ {section.price.toFixed(2)}
										</Badge>
									)}
								</div>

								<div className="space-y-2">
									<h5 className="font-medium text-sm text-muted-foreground">
										Cursos ({section.courses.length})
									</h5>
									<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
										{section.courses.map((courseItem) => (
											<div
												key={courseItem.courseId}
												className="flex items-center gap-3 p-3 border rounded-lg bg-muted/50"
											>
												<div className="w-8 h-8 rounded bg-primary/10 flex items-center justify-center flex-shrink-0">
													<BookOpenIcon className="h-4 w-4 text-primary" />
												</div>
												<div className="flex-1 min-w-0">
													<div className="font-medium text-sm truncate">
														{courseItem.course?.name || `Curso ${courseItem.courseId}`}
													</div>
													{courseItem.course?.community && (
														<div className="text-xs text-muted-foreground truncate">
															{courseItem.course.community}
														</div>
													)}
												</div>
											</div>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Opções de Publicação</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<label className="text-sm font-medium mb-2 block">Status da Publicação</label>
						<Select
							value={publishStatus}
							onValueChange={(value: "DRAFT" | "PUBLISHED") => setPublishStatus(value)}
						>
							<SelectTrigger className="max-w-sm">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="DRAFT">
									<div className="flex items-center gap-2">
										<SaveIcon className="h-4 w-4" />
										<div>
											<div className="font-medium">Salvar como Rascunho</div>
											<div className="text-xs text-muted-foreground">
												Pode ser editado antes da publicação
											</div>
										</div>
									</div>
								</SelectItem>
								<SelectItem value="PUBLISHED">
									<div className="flex items-center gap-2">
										<GlobeIcon className="h-4 w-4" />
										<div>
											<div className="font-medium">Publicar Agora</div>
											<div className="text-xs text-muted-foreground">
												Vitrine ficará visível imediatamente
											</div>
										</div>
									</div>
								</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div>
						<label className="text-sm font-medium mb-2 block">
							Notas da Publicação (Opcional)
						</label>
						<Textarea
							value={publishNotes}
							onChange={(e) => setPublishNotes(e.target.value)}
							placeholder="Adicione observações sobre esta versão da vitrine..."
							className="min-h-[80px]"
						/>
					</div>

					{publishStatus === "PUBLISHED" && (
						<div className="p-4 border border-green-200 bg-green-50 rounded-lg">
							<div className="flex items-start gap-3">
								<CheckIcon className="h-5 w-5 text-green-600 mt-0.5" />
								<div>
									<h5 className="font-medium text-green-800">Pronto para Publicar</h5>
									<p className="text-sm text-green-700 mt-1">
										Sua vitrine será publicada e ficará visível para os visitantes imediatamente.
										Você pode continuar editando após a publicação.
									</p>
								</div>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
