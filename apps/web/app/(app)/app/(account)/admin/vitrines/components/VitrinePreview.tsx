"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Textarea } from "@ui/components/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	EyeIcon,
	CheckIcon,
	SaveIcon,
	LockIcon,
	DollarSignIcon,
	UsersIcon,
	BookOpenIcon,
	GlobeIcon,
	ShieldIcon,
	UploadIcon,
} from "lucide-react";

interface VitrineFormData {
	title: string;
	description: string;
	bannerImage?: string;
	organizationId: string;
	status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
	visibility: "PUBLIC" | "PRIVATE";
	sections: VitrineSection[];
}

interface VitrineSection {
	id: string;
	title: string;
	subtitle?: string;
	description?: string;
	position: number;
	isLocked: boolean;
	requiresPurchase: boolean;
	checkoutUrl?: string;
	webhookUrl?: string;
	price?: number;
	originalPrice?: number;
	accessType: "FREE" | "PAID" | "MEMBER_ONLY";
	visibility: "PUBLIC" | "PRIVATE";
	courses: VitrineSectionCourse[];
}

interface VitrineSectionCourse {
	courseId: string;
	position: number;
	course?: {
		id: string;
		name: string;
		logo?: string;
		community?: string;
	};
}

interface VitrinePreviewProps {
	data: VitrineFormData;
	onSave: (asDraft?: boolean) => Promise<void>;
	onPrevious: () => void;
	isLoading: boolean;
}

export function VitrinePreview({ data, onSave, onPrevious, isLoading }: VitrinePreviewProps) {
	const [publishStatus, setPublishStatus] = useState<"DRAFT" | "PUBLISHED">(
		data.status === "ARCHIVED" ? "DRAFT" : data.status
	);
	const [publishNotes, setPublishNotes] = useState("");

	const totalCourses = data.sections.reduce((acc, section) => acc + section.courses.length, 0);
	const freeSections = data.sections.filter(section => section.accessType === "FREE").length;
	const paidSections = data.sections.filter(section => section.accessType === "PAID").length;

	const getAccessTypeBadge = (accessType: string) => {
		switch (accessType) {
			case "FREE":
				return <Badge className="bg-green-100 text-green-800 border-green-200">Gratuito</Badge>;
			case "PAID":
				return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Pago</Badge>;
			case "MEMBER_ONLY":
				return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Só Membros</Badge>;
			default:
				return <Badge>{accessType}</Badge>;
		}
	};

	const getVisibilityBadge = (visibility: string) => {
		switch (visibility) {
			case "PUBLIC":
				return (
					<Badge className="bg-gray-100 text-gray-800 border-gray-200">
						<GlobeIcon className="mr-1 h-3 w-3" />
						Público
					</Badge>
				);
			case "PRIVATE":
				return (
					<Badge className="bg-orange-100 text-orange-800 border-orange-200">
						<ShieldIcon className="mr-1 h-3 w-3" />
						Privado
					</Badge>
				);
			default:
				return <Badge>{visibility}</Badge>;
		}
	};

	return (
		<div className="max-w-4xl mx-auto space-y-6">
			{/* Preview Header */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<EyeIcon className="h-5 w-5 text-primary" />
						Visualizar e Publicar
					</CardTitle>
					<p className="text-muted-foreground">
						Revise sua vitrine antes de publicar
					</p>
				</CardHeader>
			</Card>

			{/* Vitrine Summary */}
			<Card>
				<CardHeader>
					<CardTitle>Resumo da Vitrine</CardTitle>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Basic Info */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="md:col-span-2">
							<h3 className="text-xl font-bold mb-2">{data.title}</h3>
							{data.description && (
								<p className="text-muted-foreground mb-4">{data.description}</p>
							)}
							<div className="flex gap-2">
								{getVisibilityBadge(data.visibility)}
								<Badge className="bg-blue-100 text-blue-800 border-blue-200">
									{data.sections.length} seção(ões)
								</Badge>
								<Badge className="bg-purple-100 text-purple-800 border-purple-200">
									{totalCourses} curso(s)
								</Badge>
							</div>
						</div>
						{data.bannerImage && (
							<div className="aspect-video rounded-lg overflow-hidden bg-muted">
								<img
									src={data.bannerImage}
									alt="Banner da vitrine"
									className="w-full h-full object-cover"
								/>
							</div>
						)}
					</div>

					{/* Stats */}
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-primary mb-1">{data.sections.length}</div>
								<div className="text-sm text-muted-foreground">Seções</div>
							</CardContent>
						</Card>
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-green-600 mb-1">{freeSections}</div>
								<div className="text-sm text-muted-foreground">Gratuitas</div>
							</CardContent>
						</Card>
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-blue-600 mb-1">{paidSections}</div>
								<div className="text-sm text-muted-foreground">Pagas</div>
							</CardContent>
						</Card>
						<Card className="border-dashed">
							<CardContent className="p-4 text-center">
								<div className="text-2xl font-bold text-purple-600 mb-1">{totalCourses}</div>
								<div className="text-sm text-muted-foreground">Cursos Total</div>
							</CardContent>
						</Card>
					</div>
				</CardContent>
			</Card>

			{/* Sections Preview */}
			<Card>
				<CardHeader>
					<CardTitle>Seções da Vitrine</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{data.sections.map((section, index) => (
						<Card key={section.id} className="border-l-4 border-l-primary">
							<CardContent className="p-4">
								<div className="flex items-start justify-between mb-3">
									<div className="flex-1">
										<div className="flex items-center gap-3 mb-2">
											<h4 className="font-semibold text-lg">{section.title}</h4>
											<Badge className="bg-gray-100 text-gray-800 border-gray-200">
												Posição {section.position + 1}
											</Badge>
										</div>
										{section.subtitle && (
											<p className="text-muted-foreground mb-1">{section.subtitle}</p>
										)}
										{section.description && (
											<p className="text-sm text-muted-foreground">{section.description}</p>
										)}
									</div>
								</div>

								<div className="flex flex-wrap gap-2 mb-4">
									{getAccessTypeBadge(section.accessType)}
									{getVisibilityBadge(section.visibility)}
									{section.isLocked && (
										<Badge className="bg-red-100 text-red-800 border-red-200">
											<LockIcon className="mr-1 h-3 w-3" />
											Bloqueado
										</Badge>
									)}
									{section.requiresPurchase && (
										<Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
											<DollarSignIcon className="mr-1 h-3 w-3" />
											Requer Compra
										</Badge>
									)}
									{section.price && (
										<Badge className="bg-blue-100 text-blue-800 border-blue-200">
											R$ {section.price.toFixed(2)}
										</Badge>
									)}
								</div>

								{/* Section Courses */}
								<div className="space-y-2">
									<h5 className="font-medium text-sm text-muted-foreground">
										Cursos ({section.courses.length})
									</h5>
									<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
										{section.courses.map((courseItem) => (
											<div
												key={courseItem.courseId}
												className="flex items-center gap-3 p-3 border rounded-lg bg-muted/50"
											>
												<div className="w-8 h-8 rounded bg-primary/10 flex items-center justify-center flex-shrink-0">
													<BookOpenIcon className="h-4 w-4 text-primary" />
												</div>
												<div className="flex-1 min-w-0">
													<div className="font-medium text-sm truncate">
														{courseItem.course?.name || `Curso ${courseItem.courseId}`}
													</div>
													{courseItem.course?.community && (
														<div className="text-xs text-muted-foreground truncate">
															{courseItem.course.community}
														</div>
													)}
												</div>
											</div>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</CardContent>
			</Card>

			{/* Publish Options */}
			<Card>
				<CardHeader>
					<CardTitle>Opções de Publicação</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<label className="text-sm font-medium mb-2 block">Status da Publicação</label>
						<Select
							value={publishStatus}
							onValueChange={(value: "DRAFT" | "PUBLISHED") => setPublishStatus(value)}
						>
							<SelectTrigger className="max-w-sm">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="DRAFT">
									<div className="flex items-center gap-2">
										<SaveIcon className="h-4 w-4" />
										<div>
											<div className="font-medium">Salvar como Rascunho</div>
											<div className="text-xs text-muted-foreground">
												Pode ser editado antes da publicação
											</div>
										</div>
									</div>
								</SelectItem>
								<SelectItem value="PUBLISHED">
									<div className="flex items-center gap-2">
										<UploadIcon className="h-4 w-4" />
										<div>
											<div className="font-medium">Publicar Agora</div>
											<div className="text-xs text-muted-foreground">
												Vitrine ficará visível imediatamente
											</div>
										</div>
									</div>
								</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div>
						<label className="text-sm font-medium mb-2 block">
							Notas da Publicação (Opcional)
						</label>
						<Textarea
							value={publishNotes}
							onChange={(e) => setPublishNotes(e.target.value)}
							placeholder="Adicione observações sobre esta versão da vitrine..."
							className="min-h-[80px]"
						/>
					</div>

					{publishStatus === "PUBLISHED" && (
						<div className="p-4 border border-green-200 bg-green-50 rounded-lg">
							<div className="flex items-start gap-3">
								<CheckIcon className="h-5 w-5 text-green-600 mt-0.5" />
								<div>
									<h5 className="font-medium text-green-800">Pronto para Publicar</h5>
									<p className="text-sm text-green-700 mt-1">
										Sua vitrine será publicada e ficará visível para os visitantes imediatamente.
										Você pode continuar editando após a publicação.
									</p>
								</div>
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Actions */}
			<Card>
				<CardContent className="py-6">
					<div className="flex justify-between items-center">
						<Button variant="outline" onClick={onPrevious} disabled={isLoading}>
							Voltar para Cursos
						</Button>

						<div className="flex gap-3">
							<Button
								variant="outline"
								onClick={() => onSave(true)}
								disabled={isLoading}
							>
								<SaveIcon className="mr-2 h-4 w-4" />
								Salvar Rascunho
							</Button>

							<Button
								onClick={() => onSave(publishStatus === "DRAFT")}
								disabled={isLoading}
								className={publishStatus === "PUBLISHED" ? "bg-green-600 hover:bg-green-700" : ""}
							>
								{isLoading ? (
									<div className="flex items-center gap-2">
										<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
										Salvando...
									</div>
								) : publishStatus === "PUBLISHED" ? (
									<>
										<UploadIcon className="mr-2 h-4 w-4" />
										Publicar Vitrine
									</>
								) : (
									<>
										<SaveIcon className="mr-2 h-4 w-4" />
										Salvar Rascunho
									</>
								)}
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
