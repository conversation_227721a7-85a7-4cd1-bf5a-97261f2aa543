"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	CheckIcon,
	SaveIcon,
	ArrowLeftIcon,
	EyeIcon,
} from "lucide-react";
import { toast } from "sonner";
import { AdminPageLayout } from "@saas/admin/component/shared/AdminPageLayout";
import { VitrineForm as VitrineBasicForm } from "./VitrineBasicForm";
import { SectionManager } from "./SectionManager";
import { CourseSelector } from "./CourseSelector";
import { VitrinePreview } from "../create/VitrinePreview";
import { VitrinePreviewModal } from "../create/VitrinePreviewModal";
import type { VitrineFormData } from "../create/types";
import { apiClient } from "@shared/lib/api-client";

const STEPS = [
	{
		id: "basic",
		title: "Informações Básicas",
		description: "Título, descrição e configurações gerais",
	},
	{
		id: "sections",
		title: "Gerenciar Seções",
		description: "Adicione e configure seções da vitrine",
	},
	{
		id: "courses",
		title: "Selecionar Cursos",
		description: "Associe cursos às seções criadas",
	},
	{
		id: "preview",
		title: "Visualizar e Publicar",
		description: "Revise e publique sua vitrine",
	},
];

interface VitrineFormProps {
	mode: "create" | "edit";
	vitrineId?: string;
}

export default function VitrineForm({ mode, vitrineId }: VitrineFormProps) {
	const router = useRouter();
	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingVitrine, setIsLoadingVitrine] = useState(mode === "edit");
	const [previewModalOpen, setPreviewModalOpen] = useState(false);
	const [formData, setFormData] = useState<VitrineFormData>({
		title: "",
		description: "",
		organizationId: "",
		status: "DRAFT",
		visibility: "PUBLIC",
		isDefault: false,
		sections: [],
	});

	// Load vitrine data when in edit mode
	useEffect(() => {
		if (mode === "edit" && vitrineId) {
			loadVitrineData();
		}
	}, [mode, vitrineId]);

	const loadVitrineData = useCallback(async () => {
		if (!vitrineId) return;

		setIsLoadingVitrine(true);
		try {
			console.log('🔄 Loading vitrine data for editing:', vitrineId);

			const response = await fetch(`/api/vitrines/admin/${vitrineId}`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
			});

			const responseData = await response.json();

			if (!response.ok) {
				throw new Error(responseData.error || 'Failed to load vitrine');
			}

			console.log('✅ Vitrine data loaded successfully:', responseData);

			// Transform the API data to match form data structure
			setFormData({
				title: responseData.title || "",
				description: responseData.description || "",
				bannerImage: responseData.bannerImage || "",
				organizationId: responseData.organizationId || "",
				status: responseData.status || "DRAFT",
				visibility: responseData.visibility || "PUBLIC",
				isDefault: responseData.isDefault || false,
				sections: responseData.sections || [],
			});

		} catch (error) {
			console.error('❌ Error loading vitrine:', error);
			toast.error(`Erro ao carregar vitrine: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
			router.push('/app/admin/vitrines');
		} finally {
			setIsLoadingVitrine(false);
		}
	}, [vitrineId, router]);

	const updateFormData = useCallback((updates: Partial<VitrineFormData>) => {
		setFormData((prev) => ({ ...prev, ...updates }));
	}, []);

	const handleNext = useCallback(() => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	}, [currentStep]);

	const handleSave = useCallback(async (asDraft = true) => {
		setIsLoading(true);
		try {
			// Transform sections to match API schema
			const transformedSections = formData.sections.map((section, index) => ({
				title: section.title,
				subtitle: section.subtitle,
				description: section.description,
				position: section.position || index,
				isLocked: section.isLocked || false,
				requiresPurchase: section.requiresPurchase || false,
				checkoutUrl: section.checkoutUrl,
				webhookUrl: section.webhookUrl,
				price: section.price,
				originalPrice: section.originalPrice,
				accessType: section.accessType || "FREE" as const,
				visibility: section.visibility || "PUBLIC" as const,
				courses: section.courses.map(course => ({
					courseId: course.courseId,
					position: course.position,
				})),
			}));

			const payload = {
				title: formData.title,
				description: formData.description,
				bannerImage: formData.bannerImage,
				organizationId: formData.organizationId,
				status: (asDraft ? "DRAFT" : "PUBLISHED") as "DRAFT" | "PUBLISHED" | "ARCHIVED",
				visibility: formData.visibility,
				isDefault: formData.isDefault,
				sections: transformedSections,
			};

			let response;
			if (mode === "create") {
				// Create new vitrine
				response = await apiClient.vitrines.$post({
					json: payload,
				});
			} else {
				// Update existing vitrine
				response = await fetch(`/api/vitrines/${vitrineId}`, {
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify(payload),
				});
			}

			const responseData = await response.json();

			if (!response.ok) {
				console.error("API Error Response:", responseData);

				// Handle Zod validation errors
				if (responseData?.error?.issues) {
					const validationErrors = responseData.error.issues.map((issue: any) => {
						const field = issue.path.join('.');
						return `${field}: ${issue.message}`;
					}).join(', ');
					throw new Error(`Validation errors: ${validationErrors}`);
				}

				const errorMessage = responseData?.error || responseData?.message || `Failed to ${mode} vitrine`;
				throw new Error(errorMessage);
			}

			toast.success(
				asDraft
					? `Vitrine ${mode === "create" ? "salva" : "atualizada"} como rascunho!`
					: `Vitrine ${mode === "create" ? "criada" : "atualizada"} e publicada com sucesso!`
			);

			// Redirect to vitrines list
			router.push("/app/admin/vitrines");
		} catch (error) {
			console.error(`Error ${mode === "create" ? "creating" : "updating"} vitrine:`, error);
			toast.error(`Erro ao ${mode === "create" ? "criar" : "atualizar"} vitrine: ${error instanceof Error ? error.message : "Erro desconhecido"}`);
		} finally {
			setIsLoading(false);
		}
	}, [formData, mode, vitrineId, router]);

	const isStepValid = useCallback((step: number) => {
		switch (step) {
			case 0: // Basic info
				return formData.title && formData.organizationId;
			case 1: // Sections
				return true; // Sections are optional
			case 2: // Courses
				return true; // Courses are optional
			case 3: // Preview
				return formData.title && formData.organizationId;
			default:
				return false;
		}
	}, [formData]);

	const getStepNavigationLabels = useCallback(() => {
		if (currentStep === STEPS.length - 1) {
			return { previous: "Voltar", next: null };
		}
		return {
			previous: currentStep > 0 ? "Voltar" : null,
			next: "Próximo",
		};
	}, [currentStep]);

	// Show loading state while loading vitrine data
	if (isLoadingVitrine) {
		return (
			<AdminPageLayout
				title={`${mode === "create" ? "Criar" : "Editar"} Vitrine`}
				subtitle={`${mode === "create" ? "Crie uma nova" : "Edite a"} vitrine para sua organização`}
			>
				<div className="flex items-center justify-center min-h-[400px]">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
						<p className="text-muted-foreground">Carregando dados da vitrine...</p>
					</div>
				</div>
			</AdminPageLayout>
		);
	}

	const canProceed = isStepValid(currentStep);
	const progress = ((currentStep + 1) / STEPS.length) * 100;
	const navigationLabels = getStepNavigationLabels();

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<VitrineBasicForm
						data={formData}
						onUpdate={updateFormData}
					/>
				);
			case 1:
				return (
					<SectionManager
						sections={formData.sections as any}
						onUpdate={(sections) => updateFormData({ sections: sections as any })}
					/>
				);
			case 2:
				return (
					<CourseSelector
						sections={formData.sections as any}
						organizationId={formData.organizationId}
						onUpdate={(sections) => updateFormData({ sections: sections as any })}
					/>
				);
			case 3:
				return (
					<VitrinePreview
						data={formData}
						onSave={handleSave}
						isLoading={isLoading}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<AdminPageLayout
			title={`${mode === "create" ? "Criar" : "Editar"} Vitrine`}
			subtitle={`${mode === "create" ? "Crie uma nova" : "Edite a"} vitrine para sua organização`}
		>
			<div className="w-full mx-auto space-y-8">
				{/* Header with back button */}
				<div className="flex items-center gap-4">
					<Button
						variant="ghost"
						size="sm"
						onClick={() => router.push("/app/admin/vitrines")}
						className="flex items-center gap-2"
					>
						<ArrowLeftIcon className="h-4 w-4" />
						Voltar para Vitrines
					</Button>
				</div>

				{/* Progress indicator */}
				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<div>
								<CardTitle className="text-lg">
									{STEPS[currentStep].title}
								</CardTitle>
								<p className="text-muted-foreground text-sm">
									{STEPS[currentStep].description}
								</p>
							</div>
							<Badge status="info">
								Passo {currentStep + 1} de {STEPS.length}
							</Badge>
						</div>
						<Progress value={progress} className="mt-4" />
					</CardHeader>
				</Card>

				{/* Step content */}
				<div className="min-h-[400px]">
					{renderStepContent()}
				</div>

				{/* Navigation */}
				<Card>
					<CardContent className="pt-6">
						<div className="flex items-center justify-between">
							<div>
								{navigationLabels.previous && (
									<Button
										variant="outline"
										onClick={handlePrevious}
										disabled={isLoading}
									>
										<ChevronLeftIcon className="mr-2 h-4 w-4" />
										{navigationLabels.previous}
									</Button>
								)}
							</div>

							<div className="flex gap-2">
								<Button
									variant="outline"
									onClick={() => handleSave(true)}
									disabled={isLoading || !formData.title}
								>
									<SaveIcon className="mr-2 h-4 w-4" />
									Salvar rascunho
								</Button>

								{navigationLabels.next ? (
									<Button
										onClick={handleNext}
										disabled={!canProceed || isLoading}
									>
										{navigationLabels.next}
										<ChevronRightIcon className="ml-2 h-4 w-4" />
									</Button>
								) : (
									<div className="flex gap-2">
										<Button
											variant="outline"
											onClick={() => setPreviewModalOpen(true)}
										>
											<EyeIcon className="mr-2 h-4 w-4" />
											Visualizar
										</Button>
										<Button
											onClick={() => handleSave(false)}
											disabled={!canProceed || isLoading}
											className="bg-green-600 hover:bg-green-700"
										>
											<CheckIcon className="mr-2 h-4 w-4" />
											{mode === "create" ? "Publicar Vitrine" : "Atualizar Vitrine"}
										</Button>
									</div>
								)}
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Preview Modal */}
				<VitrinePreviewModal
					isOpen={previewModalOpen}
					onClose={() => setPreviewModalOpen(false)}
					data={formData}
				/>
			</div>
		</AdminPageLayout>
	);
}


