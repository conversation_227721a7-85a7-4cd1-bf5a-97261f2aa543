"use client";

import { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
} from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Card, CardContent } from "@ui/components/card";
import {
	PlayIcon,
	LockIcon,
	ShoppingCartIcon,
	EyeIcon,
	XIcon,
} from "lucide-react";
import { motion } from "framer-motion";
import type { VitrineFormData } from "./types";
import { HeroBannerFrontend } from "@saas/organizations/vitrine/components/HeroBannerFrontend";
import { NetflixCarousel } from "@saas/organizations/vitrine/components/NetflixCarousel";
import { ShowcaseItem } from "@saas/organizations/vitrine/types";

interface VitrinePreviewModalProps {
	data: VitrineFormData;
	isOpen: boolean;
	onClose: () => void;
}

export function VitrinePreviewModal({ data, isOpen, onClose }: VitrinePreviewModalProps) {
	const convertToShowcaseItems = (section: any): ShowcaseItem[] => {
		return section.courses.map((course: any) => ({
			id: course.courseId,
			title: course.course?.name || `Curso ${course.courseId}`,
			image: course.course?.logo || "/images/cards/card1.jpg",
			description: course.course?.community || "Curso disponível na plataforma",
			category: section.title,
			progress: 0,
			duration: "2h 30min",
			studentsCount: 100,
			isLocked: section.isLocked,
			sectionLocked: section.isLocked,
			sectionId: section.id,
			isContinueWatching: false,
		}));
	};

	const handleHeroPlay = () => {
		console.log("Hero play clicked - preview mode");
	};

	const handleHeroInfo = () => {
		console.log("Hero info clicked - preview mode");
	};

	const handlePlay = (item: ShowcaseItem) => {
		console.log("Course play clicked - preview mode:", item.title);
	};

	const handlePurchase = (item: ShowcaseItem) => {
		console.log("Course purchase clicked - preview mode:", item.title);
	};

	const totalCourses = data.sections.reduce((acc, section) => acc + section.courses.length, 0);

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-[90vw] max-h-[90vh] overflow-hidden p-0">
				<DialogHeader className="p-6 pb-0">
					<DialogTitle className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<EyeIcon className="h-5 w-5 text-primary" />
							Prévia da Vitrine - {data.title}
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={onClose}
							className="h-8 w-8"
						>
							<XIcon className="h-4 w-4" />
						</Button>
					</DialogTitle>
				</DialogHeader>

				<div className="flex-1 overflow-y-auto">
					<div className="bg-black text-white">
						<HeroBannerFrontend
							title={data.title}
							subtitle="Cursos e treinamentos"
							description={data.description || "Explore nossos cursos especializados"}
							backgroundImage={data.bannerImage || "/images/banner1.jpg"}
							rating={4.8}
							duration="50h+ de conteúdo"
							studentsCount={totalCourses * 100}
							onPlay={handleHeroPlay}
							onInfo={handleHeroInfo}
							className="min-h-[50vh]"
						/>

						<div className="container mx-auto px-4 py-8 space-y-12">
							{data.sections.map((section, index) => {
								const showcaseItems = convertToShowcaseItems(section);

								if (showcaseItems.length === 0) return null;

								return (
									<motion.div
										key={section.id}
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ delay: index * 0.1 }}
									>
										<NetflixCarousel
											title={section.title}
											subtitle={section.subtitle}
											items={showcaseItems}
											onPlay={handlePlay}
											onPurchase={handlePurchase}
										/>
									</motion.div>
								);
							})}
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
