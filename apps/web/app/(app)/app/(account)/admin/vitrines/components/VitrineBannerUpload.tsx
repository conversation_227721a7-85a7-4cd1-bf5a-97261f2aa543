"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Card, CardContent } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Progress } from "@/modules/ui/components/progress";
import { Badge } from "@ui/components/badge";
import {
	UploadIcon,
	ImageIcon,
	XIcon,
	CheckIcon,
	AlertCircleIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useVitrineImageUpload } from "../hooks/useVitrineImageUpload";

interface VitrineBannerUploadProps {
	organizationId: string;
	currentImageUrl?: string;
	onImageUploaded: (url: string) => void;
	onImageRemoved: () => void;
}

export function VitrineBannerUpload({
	organizationId,
	currentImageUrl,
	onImageUploaded,
	onImageRemoved,
}: VitrineBannerUploadProps) {
	const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
	const [dragActive, setDragActive] = useState(false);

	const { uploadImage, isUploading, uploadProgress, error, resetUpload } = useVitrineImageUpload({
		organizationId,
		onSuccess: (url, fileName) => {
			setPreviewUrl(url);
			onImageUploaded(url);
			toast.success(`✅ Banner "${fileName}" enviado com sucesso!`);
		},
		onError: (errorMsg) => {
			toast.error(`❌ Erro ao enviar imagem: ${errorMsg}`);
		},
	});

	const onDrop = useCallback(
		async (acceptedFiles: File[]) => {
			if (acceptedFiles.length === 0) return;

			const file = acceptedFiles[0];

			// Validate file type
			if (!file.type.startsWith('image/')) {
				toast.error("❌ Apenas arquivos de imagem são aceitos");
				return;
			}

			// Validate file size (max 5MB)
			if (file.size > 5 * 1024 * 1024) {
				toast.error("❌ Arquivo muito grande. Tamanho máximo: 5MB");
				return;
			}

			// Create preview
			const preview = URL.createObjectURL(file);
			setPreviewUrl(preview);

			// Upload file
			const uploadedUrl = await uploadImage(file);

			// Clean up preview if upload failed
			if (!uploadedUrl) {
				URL.revokeObjectURL(preview);
				setPreviewUrl(currentImageUrl || null);
			}
		},
		[uploadImage, currentImageUrl]
	);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		accept: {
			'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp']
		},
		multiple: false,
		disabled: isUploading,
	});

	const handleRemoveImage = useCallback(() => {
		if (previewUrl && previewUrl !== currentImageUrl) {
			URL.revokeObjectURL(previewUrl);
		}
		setPreviewUrl(null);
		onImageRemoved();
		resetUpload();
	}, [previewUrl, currentImageUrl, onImageRemoved, resetUpload]);

	return (
		<div className="space-y-4">
			{/* Current Image Preview */}
			{previewUrl && (
				<div className="relative group">
					<div className="relative w-full h-48 rounded-lg overflow-hidden border-2 border-dashed border-muted-foreground/25">
						<img
							src={previewUrl}
							alt="Banner preview"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
							<Button
								variant="outline"
								size="sm"
								className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
								onClick={handleRemoveImage}
								disabled={isUploading}
							>
								<XIcon className="h-4 w-4 mr-2" />
								Remover
							</Button>
						</div>
					</div>
					{isUploading && (
						<div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
							<div className="bg-background p-4 rounded-lg">
								<div className="flex items-center gap-2 mb-2">
									<UploadIcon className="h-4 w-4 animate-pulse" />
									<span className="text-sm font-medium">Enviando...</span>
								</div>
								<Progress value={uploadProgress} className="w-48" />
							</div>
						</div>
					)}
				</div>
			)}

			{/* Upload Area */}
			{!previewUrl && (
				<Card
					{...getRootProps()}
					className={`cursor-pointer transition-all duration-200 ${
						isDragActive
							? "border-primary bg-primary/5"
							: "border-dashed hover:border-primary/50 hover:bg-muted/50"
					} ${isUploading ? "pointer-events-none opacity-50" : ""}`}
				>
					<CardContent className="p-8">
						<div className="flex flex-col items-center justify-center text-center space-y-4">
							<div className="p-4 rounded-full bg-primary/10">
								{isUploading ? (
									<UploadIcon className="h-8 w-8 text-primary animate-pulse" />
								) : (
									<ImageIcon className="h-8 w-8 text-primary" />
								)}
							</div>

							<div className="space-y-2">
								<h3 className="text-lg font-semibold">
									{isUploading ? "Enviando imagem..." : "Adicionar Banner"}
								</h3>
								<p className="text-sm text-muted-foreground">
									{isUploading
										? "Aguarde enquanto enviamos sua imagem..."
										: "Arraste uma imagem aqui ou clique para selecionar"
									}
								</p>
								{!isUploading && (
									<p className="text-xs text-muted-foreground">
										PNG, JPG, GIF ou WebP • Máximo 5MB
									</p>
								)}
							</div>

							{!isUploading && (
								<Button variant="outline" size="sm">
									<UploadIcon className="h-4 w-4 mr-2" />
									Selecionar Imagem
								</Button>
							)}
						</div>
					</CardContent>
					<input {...getInputProps()} />
				</Card>
			)}

			{/* Error Display */}
			{error && (
				<div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
					<AlertCircleIcon className="h-4 w-4 text-destructive" />
					<span className="text-sm text-destructive">{error}</span>
				</div>
			)}

			{/* Upload Progress */}
			{isUploading && !previewUrl && (
				<div className="space-y-2">
					<div className="flex items-center justify-between text-sm">
						<span>Enviando imagem...</span>
						<span>{uploadProgress}%</span>
					</div>
					<Progress value={uploadProgress} />
				</div>
			)}

			{/* Success Message */}
			{!isUploading && previewUrl && uploadProgress === 100 && (
				<div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
					<CheckIcon className="h-4 w-4 text-green-600" />
					<span className="text-sm text-green-700">Imagem enviada com sucesso!</span>
				</div>
			)}
		</div>
	);
}
