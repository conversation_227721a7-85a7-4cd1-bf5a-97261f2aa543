"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	GripVerticalIcon,
	PlusIcon,
	SearchIcon,
	BookOpenIcon,
	UsersIcon,
	TrashIcon,
	GraduationCapIcon,
	AlertCircleIcon,
	LoaderIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useCourses } from "../hooks/useCourses";

interface Course {
	id: string;
	name: string;
	logo?: string;
	community?: string;
	description?: string;
	studentsCount?: number;
	duration?: string;
}

interface VitrineSectionCourse {
	courseId: string;
	position: number;
	course?: Course;
}

interface VitrineSection {
	id: string;
	title: string;
	courses: VitrineSectionCourse[];
}

interface CourseSelectorProps {
	sections: VitrineSection[];
	organizationId: string;
	onUpdate: (sections: VitrineSection[]) => void;
}

export function CourseSelector({ sections, organizationId, onUpdate }: CourseSelectorProps) {
	// Fetch courses from API
	const { courses: allCourses, isLoading: isLoadingCourses, error: coursesError } = useCourses(organizationId);
	const [selectedSectionId, setSelectedSectionId] = useState<string>(sections[0]?.id || "");
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [filterCategory, setFilterCategory] = useState("all");

	// Get available courses (not assigned to current section)
	const availableCourses = useMemo(() => {
		const currentSection = sections.find(s => s.id === selectedSectionId);
		const assignedCourseIds = currentSection?.courses.map(c => c.courseId) || [];

		return allCourses.filter((course: Course) => {
			const matchesSearch = course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				course.description?.toLowerCase().includes(searchTerm.toLowerCase());
			const notAssigned = !assignedCourseIds.includes(course.id);

			return matchesSearch && notAssigned;
		});
	}, [sections, selectedSectionId, searchTerm, allCourses]);

	// Get current section
	const currentSection = useMemo(() => {
		return sections.find(s => s.id === selectedSectionId);
	}, [sections, selectedSectionId]);

	// Get courses with full data for current section
	const sectionCoursesWithData = useMemo(() => {
		if (!currentSection) return [];

		return currentSection.courses
			.map(sectionCourse => ({
				...sectionCourse,
				course: allCourses.find((c: Course) => c.id === sectionCourse.courseId),
			}))
			.filter(item => item.course)
			.sort((a, b) => a.position - b.position);
	}, [currentSection, allCourses]);

	const handleAddCourse = useCallback((course: Course) => {
		if (!currentSection) return;

		const newCourse: VitrineSectionCourse = {
			courseId: course.id,
			position: currentSection.courses.length,
			course,
		};

		const updatedSections = sections.map(section => {
			if (section.id === selectedSectionId) {
				return {
					...section,
					courses: [...section.courses, newCourse],
				};
			}
			return section;
		});

		onUpdate(updatedSections);
		toast.success(`Curso "${course.name}" adicionado à seção!`);
	}, [sections, selectedSectionId, currentSection, onUpdate]);

	const handleRemoveCourse = useCallback((courseId: string) => {
		const updatedSections = sections.map(section => {
			if (section.id === selectedSectionId) {
				const filteredCourses = section.courses.filter(c => c.courseId !== courseId);
				// Reorder positions
				const reorderedCourses = filteredCourses.map((course, index) => ({
					...course,
					position: index,
				}));
				return {
					...section,
					courses: reorderedCourses,
				};
			}
			return section;
		});

		onUpdate(updatedSections);
		toast.success("Curso removido da seção!");
	}, [sections, selectedSectionId, onUpdate]);

	const handleDragEnd = useCallback((result: any) => {
		if (!result.destination || !currentSection) return;

		const items = Array.from(sectionCoursesWithData);
		const [reorderedItem] = items.splice(result.source.index, 1);
		items.splice(result.destination.index, 0, reorderedItem);

		// Update positions
		const reorderedCourses = items.map((item, index) => ({
			courseId: item.courseId,
			position: index,
		}));

		const updatedSections = sections.map(section => {
			if (section.id === selectedSectionId) {
				return {
					...section,
					courses: reorderedCourses,
				};
			}
			return section;
		});

		onUpdate(updatedSections);
	}, [sections, selectedSectionId, currentSection, sectionCoursesWithData, onUpdate]);

	const canProceed = sections.every(section => section.courses.length > 0);

	// Show loading state
	if (isLoadingCourses) {
		return (
			<div className="max-w-6xl mx-auto space-y-6">
				<Card>
					<CardContent className="py-12">
						<div className="flex items-center justify-center">
							<LoaderIcon className="h-8 w-8 animate-spin text-muted-foreground" />
							<span className="ml-2 text-muted-foreground">Carregando cursos...</span>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Show error state
	if (coursesError) {
		return (
			<div className="max-w-6xl mx-auto space-y-6">
				<Card>
					<CardContent className="py-12">
						<div className="flex flex-col items-center justify-center">
							<AlertCircleIcon className="h-12 w-12 text-red-500 mb-4" />
							<h3 className="text-lg font-semibold mb-2">Erro ao carregar cursos</h3>
							<p className="text-muted-foreground text-center mb-4">{coursesError}</p>
							<Button
								onClick={() => window.location.reload()}
								variant="outline"
							>
								Tentar novamente
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="max-w-6xl mx-auto space-y-6">
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<GraduationCapIcon className="h-5 w-5 text-primary" />
								Selecionar Cursos
							</CardTitle>
							<p className="text-muted-foreground">
								Adicione cursos às suas seções e organize-os
							</p>
						</div>
						<Button onClick={() => setIsDialogOpen(true)}>
							<PlusIcon className="mr-2 h-4 w-4" />
							Adicionar Cursos
						</Button>
					</div>
				</CardHeader>
				<CardContent>
					{/* Section Selector */}
					<div className="mb-6">
						<label className="text-sm font-medium mb-2 block">Seção Atual</label>
						<Select value={selectedSectionId} onValueChange={setSelectedSectionId}>
							<SelectTrigger className="max-w-sm">
								<SelectValue placeholder="Selecione uma seção" />
							</SelectTrigger>
							<SelectContent>
								{sections.map((section) => (
									<SelectItem key={section.id} value={section.id}>
										{section.title} ({section.courses.length} cursos)
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Current Section Courses */}
					{currentSection && (
						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">
									Cursos em "{currentSection.title}"
								</h3>
								<Badge className="bg-blue-100 text-blue-800 border-blue-200">
									{sectionCoursesWithData.length} curso(s)
								</Badge>
							</div>

							{sectionCoursesWithData.length === 0 ? (
								<div className="text-center justify-center flex flex-col items-center py-8 border border-dashed rounded-lg">
									<BookOpenIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
									<h4 className="text-lg font-semibold mb-2">Nenhum curso adicionado</h4>
									<p className="text-muted-foreground mb-4">
										Adicione cursos a esta seção para criar sua vitrine
									</p>
									<Button onClick={() => setIsDialogOpen(true)}>
										<PlusIcon className="mr-2 h-4 w-4" />
										Adicionar Cursos
									</Button>
								</div>
							) : (
								<DragDropContext onDragEnd={handleDragEnd}>
									<Droppable droppableId="section-courses">
										{(provided) => (
											<div
												{...provided.droppableProps}
												ref={provided.innerRef}
												className="space-y-3"
											>
												{sectionCoursesWithData.map((item, index) => (
													<Draggable
														key={item.courseId}
														draggableId={item.courseId}
														index={index}
													>
														{(provided, snapshot) => (
															<Card
																ref={provided.innerRef}
																{...provided.draggableProps}
																className={`transition-shadow ${
																	snapshot.isDragging ? "shadow-lg" : ""
																}`}
															>
																<CardContent className="p-4">
																	<div className="flex items-center gap-4">
																		<div
																			{...provided.dragHandleProps}
																			className="cursor-grab active:cursor-grabbing"
																		>
																			<GripVerticalIcon className="h-5 w-5 text-muted-foreground" />
																		</div>

																		<div className="w-16 h-16 rounded-lg overflow-hidden bg-muted flex-shrink-0">
																			{item.course?.logo ? (
																				<img
																					src={item.course.logo}
																					alt={item.course.name}
																					className="w-full h-full object-cover"
																				/>
																			) : (
																				<div className="w-full h-full flex items-center justify-center">
																					<BookOpenIcon className="h-6 w-6 text-muted-foreground" />
																				</div>
																			)}
																		</div>

																		<div className="flex-1">
																			<h4 className="font-semibold">{item.course?.name}</h4>
																			{item.course?.community && (
																				<p className="text-sm text-muted-foreground">
																					{item.course.community}
																				</p>
																			)}
																			<div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
																				{item.course?.studentsCount && (
																					<span className="flex items-center gap-1">
																						<UsersIcon className="h-3 w-3" />
																						{item.course.studentsCount} alunos
																					</span>
																				)}
																				{item.course?.duration && (
																					<span>{item.course.duration}</span>
																				)}
																			</div>
																		</div>

																		<Badge className="bg-gray-100 text-gray-800 border-gray-200">
																			Posição {item.position + 1}
																		</Badge>

																		<Button
																			variant="outline"
																			size="sm"
																			onClick={() => handleRemoveCourse(item.courseId)}
																			className="text-red-600 hover:text-red-700"
																		>
																			<TrashIcon className="h-4 w-4" />
																		</Button>
																	</div>
																</CardContent>
															</Card>
														)}
													</Draggable>
												))}
												{provided.placeholder}
											</div>
										)}
									</Droppable>
								</DragDropContext>
							)}
						</div>
					)}


				</CardContent>
			</Card>

			{/* Course Selection Dialog */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="max-w-4xl max-h-[80vh]">
					<DialogHeader>
						<DialogTitle>Adicionar Cursos à Seção</DialogTitle>
					</DialogHeader>

					<div className="space-y-4">
						{/* Search and Filters */}
						<div className="flex gap-4">
							<div className="relative flex-1">
								<SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder="Buscar cursos..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-9"
								/>
							</div>
						</div>

						{/* Available Courses */}
						<div className="max-h-[50vh] overflow-y-auto space-y-3">
							{availableCourses.length === 0 ? (
								<div className="text-center py-8">
									<BookOpenIcon className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
									<p className="text-muted-foreground">
										{allCourses.length === 0
											? "Nenhum curso encontrado. Crie um curso primeiro para adicioná-lo à vitrine."
											: searchTerm
											? "Nenhum curso encontrado com este termo de busca"
											: "Todos os cursos já foram adicionados a esta seção"
										}
									</p>
									{allCourses.length === 0 && (
										<Button
											onClick={() => {
												setIsDialogOpen(false);
												window.location.href = "/app/admin/courses/create";
											}}
											className="mt-4"
										>
											Criar Primeiro Curso
										</Button>
									)}
								</div>
							) : (
								availableCourses.map((course: Course) => (
									<Card key={course.id} className="cursor-pointer hover:shadow-md transition-shadow">
										<CardContent className="p-4">
											<div className="flex items-center gap-4">
												<div className="w-16 h-16 rounded-lg overflow-hidden bg-muted flex-shrink-0">
													{course.logo ? (
														<img
															src={course.logo}
															alt={course.name}
															className="w-full h-full object-cover"
														/>
													) : (
														<div className="w-full h-full flex items-center justify-center">
															<BookOpenIcon className="h-6 w-6 text-muted-foreground" />
														</div>
													)}
												</div>

												<div className="flex-1">
													<h4 className="font-semibold">{course.name}</h4>
													{course.community && (
														<p className="text-sm text-muted-foreground">
															{course.community}
														</p>
													)}
													{course.description && (
														<p className="text-sm text-muted-foreground mt-1">
															{course.description}
														</p>
													)}
													<div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
														{course.studentsCount && (
															<span className="flex items-center gap-1">
																<UsersIcon className="h-3 w-3" />
																{course.studentsCount} alunos
															</span>
														)}
														{course.duration && <span>{course.duration}</span>}
													</div>
												</div>

												<Button onClick={() => handleAddCourse(course)}>
													<PlusIcon className="mr-2 h-4 w-4" />
													Adicionar
												</Button>
											</div>
										</CardContent>
									</Card>
								))
							)}
						</div>
					</div>

					<div className="flex justify-end">
						<Button variant="outline" onClick={() => setIsDialogOpen(false)}>
							Fechar
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
