"use client";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@shared/lib/api-client";

interface UseAdminVitrinesOptions {
	page?: number;
	limit?: number;
	query?: string;
	status?: "DRAFT" | "PUBLISHED" | "ARCHIVED";
	visibility?: "PUBLIC" | "PRIVATE";
	organizationSlug?: string;
	enabled?: boolean;
}

interface VitrineStats {
	total: number;
	published: number;
	views: number;
	revenue: number;
}

interface AdminVitrinesResponse {
	vitrines: Array<{
		id: string;
		title: string;
		description?: string | null;
		status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
		visibility: "PUBLIC" | "PRIVATE";
		bannerImage?: string | null;
		createdAt: string;
		updatedAt: string;
		organization: {
			id: string;
			name: string;
			slug: string | null;
		};
		creator: {
			id: string;
			name: string;
			email: string;
		} | null;
		sectionsCount: number;
		viewsCount: number;
	}>;
	organizations: Array<{
		id: string;
		name: string;
		slug: string | null;
		vitrinesCount: number;
	}>;
	stats: VitrineStats;
	total: number;
}

export function useAdminVitrines({
	page = 1,
	limit = 12,
	query = "",
	status,
	visibility,
	organizationSlug,
	enabled = true,
}: UseAdminVitrinesOptions) {
	return useQuery({
		queryKey: ["admin-vitrines", { page, limit, query, status, visibility, organizationSlug }],
		queryFn: async (): Promise<{
			data: {
				vitrines: AdminVitrinesResponse["vitrines"];
				organizations: AdminVitrinesResponse["organizations"];
				stats: VitrineStats;
			};
			meta: {
				total: number;
				totalPages: number;
				page: number;
				limit: number;
				hasNextPage: boolean;
				hasPreviousPage: boolean;
			};
		}> => {
			const response = await apiClient.admin.vitrines.$get({
				query: {
					limit: limit.toString(),
					offset: ((page - 1) * limit).toString(),
					query,
					...(status && { status }),
					...(visibility && { visibility }),
					...(organizationSlug && { organizationSlug }),
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch admin vitrines");
			}

			const data = await response.json();

			return {
				data: {
					vitrines: data.vitrines,
					organizations: data.organizations,
					stats: data.stats,
				},
				meta: {
					total: data.total,
					totalPages: Math.ceil(data.total / limit),
					page,
					limit,
					hasNextPage: page * limit < data.total,
					hasPreviousPage: page > 1,
				},
			};
		},
		enabled,
	});
}
