export interface VitrineFormData {
	title: string;
	description: string;
	bannerImage?: string;
	organizationId: string;
	status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
	visibility: "PUBLIC" | "PRIVATE";
	isDefault: boolean;
	sections: VitrineSection[];
}

export interface VitrineSection {
	id: string;
	title: string;
	subtitle?: string;
	description?: string;
	position: number;
	isLocked: boolean;
	requiresPurchase: boolean;
	checkoutUrl?: string;
	webhookUrl?: string;
	price?: number;
	originalPrice?: number;
	accessType: "FREE" | "PAID" | "MEMBER_ONLY";
	visibility: "PUBLIC" | "PRIVATE";
	courses: VitrineSectionCourse[];
}

export interface VitrineSectionCourse {
	courseId: string;
	position: number;
	course?: {
		id: string;
		name: string;
		logo?: string;
		community?: string;
	};
}
