import { useState, useCallback } from "react";
import { toast } from "sonner";

interface UseVitrineImageUploadOptions {
	organizationId: string;
	onSuccess?: (url: string, fileName: string) => void;
	onError?: (error: string) => void;
}

interface UploadResponse {
	uploadUrl?: string;
	filePath: string;
	publicUrl: string;
}

export function useVitrineImageUpload({ organizationId, onSuccess, onError }: UseVitrineImageUploadOptions) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [error, setError] = useState<string | null>(null);

	const uploadImage = async (file: File): Promise<string | null> => {
		if (!file || !organizationId) {
			const errorMsg = "Arquivo ou organização não fornecidos";
			setError(errorMsg);
			onError?.(errorMsg);
			return null;
		}

		setIsUploading(true);
		setError(null);
		setUploadProgress(0);

		try {
			// Convert file to base64
			const base64Data = await new Promise<string>((resolve, reject) => {
				const reader = new FileReader();
				reader.onload = () => resolve(reader.result as string);
				reader.onerror = reject;
				reader.readAsDataURL(file);
			});

			setUploadProgress(50);

			// Upload file through API (proxy approach to avoid CORS)
			const uploadResponse = await fetch("/api/vitrines/upload-url", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					fileName: file.name,
					organizationId,
					fileData: base64Data,
				}),
			});

			if (!uploadResponse.ok) {
				const errorData = await uploadResponse.json() as { error?: string };
				throw new Error(errorData.error || "Failed to upload file");
			}

			const { publicUrl }: UploadResponse = await uploadResponse.json();

			setUploadProgress(100);
			onSuccess?.(publicUrl, file.name);
			return publicUrl;
		} catch (err) {
			console.error("Upload error:", err);
			const errorMsg = err instanceof Error ? err.message : "Upload failed";
			setError(errorMsg);
			onError?.(errorMsg);
			return null;
		} finally {
			setIsUploading(false);
		}
	};

	const resetUpload = () => {
		setIsUploading(false);
		setUploadProgress(0);
		setError(null);
	};

	return {
		uploadImage,
		isUploading,
		uploadProgress,
		error,
		resetUpload,
	};
}
