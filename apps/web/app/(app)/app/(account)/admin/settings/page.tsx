import { getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { AdminSettingsDashboard } from "@/modules/saas/admin/component/settings/AdminSettingsDashboard";

export default async function AdminSettingsPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	if (session.user?.role !== "admin") {
		redirect("/app");
	}

	return <AdminSettingsDashboard />;
}
