import { getSession } from "@saas/auth/lib/server";
import { CourseAdminDashboard } from "@/modules/saas/admin/component/courses/CourseAdminDashboard";
import { redirect } from "next/navigation";

export default async function AdminCoursesPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	if (session.user?.role !== "admin") {
		redirect("/app");
	}

	return <CourseAdminDashboard />;
}
