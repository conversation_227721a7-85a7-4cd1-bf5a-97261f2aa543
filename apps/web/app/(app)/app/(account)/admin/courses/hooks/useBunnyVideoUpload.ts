import { useState, useCallback } from 'react'
import { toast } from 'sonner'

interface BunnyVideoUploadState {
  isUploading: boolean
  progress: number
  status: 'idle' | 'creating' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
  videoId?: string
  videoUrl?: string
}

interface UseBunnyVideoUploadOptions {
  onProgress?: (progress: number) => void
  onSuccess?: (videoUrl: string, videoId: string) => void
  onError?: (error: string) => void
}

const BUNNY_CONFIG = {
  apiKey: process.env.NEXT_PUBLIC_BUNNY_SIGNATURE,
  libraryId: process.env.NEXT_PUBLIC_BUNNY_LIBRARY_ID,
  expire: process.env.NEXT_PUBLIC_BUNNY_EXPIRE
}

export function useBunnyVideoUpload(options: UseBunnyVideoUploadOptions = {}) {
  const [uploadState, setUploadState] = useState<BunnyVideoUploadState>({
    isUploading: false,
    progress: 0,
    status: 'idle'
  })

  const isConfigured = !!(BUNNY_CONFIG.apiKey && BUNNY_CONFIG.libraryId)

  const createVideoObject = async (title: string): Promise<{ videoId: string; signature: string; expireTime: number }> => {
    if (!BUNNY_CONFIG.apiKey || !BUNNY_CONFIG.libraryId) {
      throw new Error('Bunny.net não configurado')
    }

    const createResponse = await fetch(
      `https://video.bunnycdn.com/library/${BUNNY_CONFIG.libraryId}/videos`,
      {
        method: 'POST',
        headers: {
          AccessKey: BUNNY_CONFIG.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title }),
      }
    )

    if (!createResponse.ok) {
      throw new Error('Falha ao criar vídeo no Bunny.net')
    }

    const videoData = await createResponse.json()
    const videoId = videoData.guid

    // Gerar assinatura SHA256
    const expireTime = Math.floor(Date.now() / 1000) + 3600
    const signatureString = `${BUNNY_CONFIG.libraryId}${BUNNY_CONFIG.apiKey}${expireTime}${videoId}`

    const encoder = new TextEncoder()
    const data = encoder.encode(signatureString)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const signature = hashArray
      .map((b) => b.toString(16).padStart(2, '0'))
      .join('')

    return { videoId, signature, expireTime }
  }

  const uploadVideo = useCallback(async (file: File, title?: string) => {
    if (!isConfigured) {
      const error = 'Bunny.net não está configurado. Verifique as variáveis de ambiente.'
      toast.error(error)
      options.onError?.(error)
      return
    }

    if (!file.type.startsWith('video/')) {
      const error = 'Apenas arquivos de vídeo são aceitos'
      toast.error(error)
      options.onError?.(error)
      return
    }

    // 5GB limit
    if (file.size > 5 * 1024 * 1024 * 1024) {
      const error = 'Arquivo muito grande. Tamanho máximo: 5GB'
      toast.error(error)
      options.onError?.(error)
      return
    }

    const videoTitle = title || file.name.replace(/\.[^/.]+$/, '')

    try {
      setUploadState({
        isUploading: true,
        progress: 0,
        status: 'creating'
      })

      toast.loading('🎬 Preparando upload do vídeo...', { id: 'video-upload' })

      // Criar objeto de vídeo no Bunny.net
      const { videoId, signature, expireTime } = await createVideoObject(videoTitle)

      setUploadState(prev => ({
        ...prev,
        status: 'uploading',
        videoId
      }))

            // Upload direto via FormData (simplificado para demonstração)
      const formData = new FormData()
      formData.append('file', file)

      const uploadPromise = new Promise<string>((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100)

            setUploadState(prev => ({
              ...prev,
              progress
            }))

            options.onProgress?.(progress)

            if (progress === 50) {
              toast.loading('Upload em andamento...', { id: 'video-upload' })
            } else if (progress === 90) {
              toast.loading('Finalizando...', { id: 'video-upload' })
            }
          }
        })

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            setUploadState(prev => ({
              ...prev,
              status: 'processing',
              progress: 100
            }))

            toast.loading('Processando vídeo...', { id: 'video-upload' })

            // Simular processamento
            setTimeout(() => {
              const videoUrl = `https://iframe.mediadelivery.net/embed/${BUNNY_CONFIG.libraryId}/${videoId}`

              setUploadState({
                isUploading: false,
                progress: 100,
                status: 'completed',
                videoId,
                videoUrl
              })

              toast.dismiss('video-upload')
              toast.success('✅ Vídeo enviado com sucesso!')
              options.onSuccess?.(videoUrl, videoId)
              resolve(videoUrl)
            }, 3000)
          } else {
            const errorMessage = `Upload falhou: ${xhr.statusText}`

            setUploadState({
              isUploading: false,
              progress: 0,
              status: 'error',
              error: errorMessage
            })

            toast.dismiss('video-upload')
            toast.error(`❌ ${errorMessage}`)
            options.onError?.(errorMessage)
            reject(new Error(errorMessage))
          }
        })

        xhr.addEventListener('error', () => {
          const errorMessage = 'Erro de rede durante o upload'

          setUploadState({
            isUploading: false,
            progress: 0,
            status: 'error',
            error: errorMessage
          })

          toast.dismiss('video-upload')
          toast.error(`❌ ${errorMessage}`)
          options.onError?.(errorMessage)
          reject(new Error(errorMessage))
        })

        // Para demonstração, vamos usar um endpoint mock
        xhr.open('PUT', `https://video.bunnycdn.com/library/${BUNNY_CONFIG.libraryId}/videos/${videoId}`)
        xhr.setRequestHeader('AccessKey', BUNNY_CONFIG.apiKey!)
        xhr.send(file) // Enviar arquivo diretamente
      })

      return uploadPromise

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro no upload do vídeo'

      setUploadState({
        isUploading: false,
        progress: 0,
        status: 'error',
        error: errorMessage
      })

      toast.dismiss('video-upload')
      toast.error(errorMessage)
      options.onError?.(errorMessage)
    }
  }, [isConfigured, options])

  const resetUpload = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      status: 'idle'
    })
  }, [])

  return {
    uploadState,
    uploadVideo,
    resetUpload,
    isConfigured
  }
}
