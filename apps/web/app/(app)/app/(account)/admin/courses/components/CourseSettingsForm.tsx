"use client";

import { useState, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	SettingsIcon,
	GlobeIcon,
	LockIcon,
	DollarSignIcon,
	UsersIcon,
} from "lucide-react";
import type { CourseFormData, CourseSettings } from "../types";

interface CourseSettingsFormProps {
	data: CourseFormData;
	onUpdate: (data: Partial<CourseFormData>) => void;
	onNext: () => void;
	onPrevious: () => void;
}

export function CourseSettingsForm({ data, onUpdate, onNext, onPrevious }: CourseSettingsFormProps) {
	const [settings, setSettings] = useState<CourseSettings>({
		visibility: 'public',
		pricing: 'free',
		currency: 'BRL',
		allowComments: true,
		certificateEnabled: false,
	});

	const handleSettingChange = useCallback((key: keyof CourseSettings, value: any) => {
		setSettings(prev => ({ ...prev, [key]: value }));
	}, []);

		const handleNext = useCallback(() => {
		// Update parent data with settings
		onUpdate({
			settings: settings
		});
		onNext();
	}, [onUpdate, onNext, settings]);

	return (
		<div className="max-w-4xl mx-auto space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<SettingsIcon className="h-5 w-5 text-primary" />
						Configurações do Curso
					</CardTitle>
					<p className="text-muted-foreground">
						Configure as opções de acesso, preços e funcionalidades
					</p>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Visibility Settings */}
					<div className="space-y-4">
						<h3 className="font-semibold text-foreground flex items-center gap-2">
							<GlobeIcon className="h-4 w-4" />
							Visibilidade
						</h3>
						<div className="grid gap-3">
							<label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<input
									type="radio"
									name="visibility"
									value="public"
									checked={settings.visibility === 'public'}
									onChange={(e) => handleSettingChange('visibility', e.target.value)}
									className="text-primary"
								/>
								<div className="flex-1">
									<div className="font-medium">Público</div>
									<div className="text-sm text-muted-foreground">
										Qualquer pessoa pode encontrar e acessar este curso
									</div>
								</div>
								<Badge status="success">Recomendado</Badge>
							</label>
							<label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<input
									type="radio"
									name="visibility"
									value="unlisted"
									checked={settings.visibility === 'unlisted'}
									onChange={(e) => handleSettingChange('visibility', e.target.value)}
									className="text-primary"
								/>
								<div className="flex-1">
									<div className="font-medium">Não listado</div>
									<div className="text-sm text-muted-foreground">
										Apenas pessoas com o link podem acessar
									</div>
								</div>
							</label>
							<label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<input
									type="radio"
									name="visibility"
									value="private"
									checked={settings.visibility === 'private'}
									onChange={(e) => handleSettingChange('visibility', e.target.value)}
									className="text-primary"
								/>
								<div className="flex-1">
									<div className="font-medium">Privado</div>
									<div className="text-sm text-muted-foreground">
										Apenas alunos convidados podem acessar
									</div>
								</div>
								<LockIcon className="h-4 w-4 text-muted-foreground" />
							</label>
						</div>
					</div>

					{/* Pricing Settings */}
					<div className="space-y-4">
						<h3 className="font-semibold text-foreground flex items-center gap-2">
							<DollarSignIcon className="h-4 w-4" />
							Preços
						</h3>
						<div className="grid gap-3">
							<label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<input
									type="radio"
									name="pricing"
									value="free"
									checked={settings.pricing === 'free'}
									onChange={(e) => handleSettingChange('pricing', e.target.value)}
									className="text-primary"
								/>
								<div className="flex-1">
									<div className="font-medium">Gratuito</div>
									<div className="text-sm text-muted-foreground">
										Curso totalmente gratuito para todos os alunos
									</div>
								</div>
								<Badge status="info">Gratuito</Badge>
							</label>
							<label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<input
									type="radio"
									name="pricing"
									value="paid"
									checked={settings.pricing === 'paid'}
									onChange={(e) => handleSettingChange('pricing', e.target.value)}
									className="text-primary"
								/>
								<div className="flex-1">
									<div className="font-medium">Pago</div>
									<div className="text-sm text-muted-foreground">
										Alunos precisam pagar para acessar o curso
									</div>
								</div>
							</label>
						</div>

						{/* Price Input */}
						{settings.pricing === 'paid' && (
							<div className="flex gap-2 ml-6">
								<div className="flex-1">
									<Input
										type="number"
										placeholder="0.00"
										value={settings.price || ''}
										onChange={(e) => handleSettingChange('price', parseFloat(e.target.value) || 0)}
										className="text-right"
									/>
								</div>
								<Select
									value={settings.currency}
									onValueChange={(value) => handleSettingChange('currency', value)}
								>
									<SelectTrigger className="w-24">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="BRL">BRL</SelectItem>
										<SelectItem value="USD">USD</SelectItem>
										<SelectItem value="EUR">EUR</SelectItem>
									</SelectContent>
								</Select>
							</div>
						)}
					</div>

					{/* Additional Settings */}
					<div className="space-y-4">
						<h3 className="font-semibold text-foreground flex items-center gap-2">
							<UsersIcon className="h-4 w-4" />
							Funcionalidades
						</h3>
						<div className="space-y-3">
							<label className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<div>
									<div className="font-medium">Permitir comentários</div>
									<div className="text-sm text-muted-foreground">
										Alunos podem comentar nas aulas
									</div>
								</div>
								<input
									type="checkbox"
									checked={settings.allowComments}
									onChange={(e) => handleSettingChange('allowComments', e.target.checked)}
									className="text-primary"
								/>
							</label>

							<label className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-muted/50">
								<div>
									<div className="font-medium">Certificado de conclusão</div>
									<div className="text-sm text-muted-foreground">
										Gerar certificado ao completar o curso
									</div>
								</div>
								<input
									type="checkbox"
									checked={settings.certificateEnabled}
									onChange={(e) => handleSettingChange('certificateEnabled', e.target.checked)}
									className="text-primary"
								/>
							</label>
						</div>
					</div>

					{/* Max Students */}
					<div className="space-y-4">
						<h3 className="font-semibold text-foreground">Limite de Alunos</h3>
						<div className="space-y-2">
							<label className="flex items-center space-x-3">
								<input
									type="checkbox"
									checked={!!settings.maxStudents}
									onChange={(e) => {
										if (e.target.checked) {
											handleSettingChange('maxStudents', 100);
										} else {
											handleSettingChange('maxStudents', undefined);
										}
									}}
									className="text-primary"
								/>
								<span className="font-medium">Limitar número de alunos</span>
							</label>
							{settings.maxStudents && (
								<Input
									type="number"
									placeholder="100"
									value={settings.maxStudents}
									onChange={(e) => handleSettingChange('maxStudents', parseInt(e.target.value) || 0)}
									className="ml-6 max-w-32"
								/>
							)}
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Navigation */}
			<div className="flex justify-between">
				<Button variant="outline" onClick={onPrevious}>
					Anterior
				</Button>
				<Button onClick={handleNext}>
					Continuar
				</Button>
			</div>
		</div>
	);
}
