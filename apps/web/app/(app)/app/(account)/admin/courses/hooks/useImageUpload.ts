import { useState } from "react";
import { apiClient } from "@shared/lib/api-client";

interface UploadResponse {
	uploadUrl?: string;
	filePath: string;
	publicUrl: string;
}

interface UseImageUploadOptions {
	organizationId: string;
	fileType: "course-logo" | "module-cover" | "lesson-cover";
}

export function useImageUpload({ organizationId, fileType }: UseImageUploadOptions) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [error, setError] = useState<string | null>(null);

	const uploadImage = async (file: File): Promise<string | null> => {
		if (!file || !organizationId) {
			setError("Arquivo ou organização não fornecidos");
			return null;
		}

		setIsUploading(true);
		setError(null);
		setUploadProgress(0);

		try {
			// Convert file to base64
			const base64Data = await new Promise<string>((resolve, reject) => {
				const reader = new FileReader();
				reader.onload = () => resolve(reader.result as string);
				reader.onerror = reject;
				reader.readAsDataURL(file);
			});

			setUploadProgress(50);

			// Upload file through API (proxy approach to avoid CORS)
			const uploadResponse = await apiClient.courses["upload-url"].$post({
				json: {
					fileName: file.name,
					fileType,
					organizationId,
					fileData: base64Data,
				},
			});

			if (!uploadResponse.ok) {
				const errorData = await uploadResponse.json() as { error?: string };
				throw new Error(errorData.error || "Failed to upload file");
			}

			const { publicUrl }: UploadResponse = await uploadResponse.json();

			setUploadProgress(100);
			return publicUrl;
		} catch (err) {
			console.error("Upload error:", err);
			setError(err instanceof Error ? err.message : "Upload failed");
			return null;
		} finally {
			setIsUploading(false);
		}
	};

	const resetUpload = () => {
		setIsUploading(false);
		setUploadProgress(0);
		setError(null);
	};

	return {
		uploadImage,
		isUploading,
		uploadProgress,
		error,
		resetUpload,
	};
}
