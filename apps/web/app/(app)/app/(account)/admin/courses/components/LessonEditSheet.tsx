"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";

import {
	XIcon,
	VideoIcon,
	ImageIcon,
	UploadIcon,
	PlayIcon,
	FileTextIcon,
	SaveIcon,
	TrashIcon,
	EyeIcon,
	AlertCircleIcon,
	CheckCircleIcon,
	RefreshCwIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useBunnyVideoUpload } from "../hooks/useBunnyVideoUpload";
import { useS3FileUpload } from "../hooks/useS3FileUpload";
import type { CourseLesson, CourseLessonFile } from "../types";
import { AuxiliaryMaterialsUpload } from "./AuxiliaryMaterialsUpload";

interface LessonEditSheetProps {
	isOpen: boolean;
	onClose: () => void;
	lesson: CourseLesson | null;
	moduleId: string;
	organizationId: string;
	onSave: (lessonData: CourseLesson) => void;
	onDelete?: () => void;
}

interface UploadProgress {
	progress: number;
	status: 'idle' | 'uploading' | 'completed' | 'error';
	error?: string;
}

export function LessonEditSheet({
	isOpen,
	onClose,
	lesson,
	moduleId,
	organizationId,
	onSave,
	onDelete,
}: LessonEditSheetProps) {
	const [formData, setFormData] = useState<CourseLesson>({
		id: '',
		name: '',
		description: '',
		duration: '',
		position: 0,
		videoUrl: '',
		thumbnail: '',
		files: [],
	});

	// Upload hooks
	const videoUpload = useBunnyVideoUpload({
		onSuccess: (videoUrl, videoId) => {
			handleInputChange('videoUrl', videoUrl);
			// Gerar thumbnail automaticamente
			const thumbnailUrl = `https://video.bunnycdn.com/library/${process.env.NEXT_PUBLIC_BUNNY_LIBRARY_ID}/videos/${videoId}/thumbnail.jpg`;
			handleInputChange('thumbnail', thumbnailUrl);
		},
		onError: (error) => {
			console.error('Erro no upload de vídeo:', error);
		}
	});

	const fileUpload = useS3FileUpload({
		organizationId,
		lessonId: lesson?.id,
		moduleId,
		onSuccess: (fileUrl) => {
			// Adicionar arquivo à lista de files
			const newFile = {
				id: `file-${Date.now()}`,
				name: 'Arquivo',
				url: fileUrl,
				type: 'document' as const,
				size: 0,
				status: 'completed' as const
			};
			setFormData(prev => ({
				...prev,
				files: [...(prev.files || []), newFile]
			}));
		},
		onError: (error) => {
			console.error('Erro no upload de arquivo:', error);
		}
	});

	const [contentHtml, setContentHtml] = useState('');
	const videoInputRef = useRef<HTMLInputElement>(null);

	// Initialize form data when lesson changes
	useEffect(() => {
		if (lesson) {
			setFormData(lesson);
			setContentHtml(lesson.description || '');
		} else {
			setFormData({
				id: `lesson-${Date.now()}`,
				name: '',
				description: '',
				duration: '',
				position: 0,
				videoUrl: '',
				thumbnail: '',
				files: [],
			});
			setContentHtml('');
		}
	}, [lesson]);

	const handleInputChange = useCallback((field: keyof CourseLesson, value: any) => {
		setFormData(prev => ({
			...prev,
			[field]: value,
		}));
	}, []);

	const handleVideoUpload = useCallback(async (file: File) => {
		await videoUpload.uploadVideo(file, formData.name || 'Video da aula');
	}, [videoUpload, formData.name]);

	const handleFileUpload = useCallback(async (file: File) => {
		await fileUpload.uploadFile(file, 'course-materials');
	}, [fileUpload]);

	const handleSave = useCallback(() => {
		if (!formData.name.trim()) {
			toast.error('Nome da aula é obrigatório');
			return;
		}

		const lessonData: CourseLesson = {
			...formData,
			description: contentHtml,
		};

		onSave(lessonData);
		toast.success('Aula salva com sucesso!');
		onClose();
	}, [formData, contentHtml, onSave, onClose]);

	const handleDelete = useCallback(() => {
		if (confirm('Tem certeza que deseja excluir esta aula?')) {
			onDelete?.();
			onClose();
		}
	}, [onDelete, onClose]);

	const getStatusIcon = (status: UploadProgress['status']) => {
		switch (status) {
			case 'uploading':
				return <RefreshCwIcon className="h-4 w-4 animate-spin text-blue-500" />;
			case 'completed':
				return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
			case 'error':
				return <AlertCircleIcon className="h-4 w-4 text-red-500" />;
			default:
				return null;
		}
	};

	return (
								<Sheet open={isOpen} onOpenChange={onClose}>
			<SheetContent
				side="right"
				className="w-full overflow-y-auto"
				width="min(900px, 90vw)"
			>
				<SheetHeader className="border-b pb-4 mb-6">
					<div className="flex items-center justify-between">
						<div>
							<SheetTitle className="text-lg font-semibold">
								{lesson ? 'Editar Aula' : 'Nova Aula'}
							</SheetTitle>
							<SheetDescription>
								Configure o conteúdo da aula
							</SheetDescription>
						</div>
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={onClose}
							>
								<XIcon className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</SheetHeader>

				<div className="space-y-8">
					{/* Informações Básicas */}
					<div className="space-y-4">
						<div className="flex items-center gap-2">
							<VideoIcon className="h-5 w-5 text-primary" />
							<h3 className="font-semibold text-lg">Informações da Aula</h3>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-2">
								<label className="text-sm font-medium">Nome da Aula *</label>
								<Input
									placeholder="Ex: Introdução ao React"
									value={formData.name}
									onChange={(e) => handleInputChange('name', e.target.value)}
									className="text-base"
								/>
							</div>
							<div className="space-y-2">
								<label className="text-sm font-medium">Duração estimada</label>
								<Select
									value={formData.duration || ''}
									onValueChange={(value) => handleInputChange('duration', value)}
								>
									<SelectTrigger>
										<SelectValue placeholder="Selecionar duração" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="5 min">5 minutos</SelectItem>
										<SelectItem value="10 min">10 minutos</SelectItem>
										<SelectItem value="15 min">15 minutos</SelectItem>
										<SelectItem value="30 min">30 minutos</SelectItem>
										<SelectItem value="45 min">45 minutos</SelectItem>
										<SelectItem value="1 hora">1 hora</SelectItem>
										<SelectItem value="1:30 hora">1h 30min</SelectItem>
										<SelectItem value="2 horas">2 horas</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						<div className="space-y-2">
							<label className="text-sm font-medium">Descrição</label>
							<Textarea
								placeholder="Descreva o que será abordado nesta aula..."
								value={contentHtml}
								onChange={(e) => setContentHtml(e.target.value)}
								rows={3}
								className="text-base"
							/>
						</div>
					</div>

					{/* Upload de Vídeo */}
					<div className="space-y-4">
						<div className="flex items-center gap-2">
							<VideoIcon className="h-5 w-5 text-primary" />
							<h3 className="font-semibold text-lg">Vídeo da Aula</h3>
						</div>

						{formData.videoUrl ? (
							<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
								<div className="relative">
									<div className="aspect-video bg-black rounded-lg overflow-hidden">
										<div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-400 to-green-600">
											{formData.thumbnail ? (
												<img
													src={formData.thumbnail}
													alt="Video thumbnail"
													className="w-full h-full object-cover"
												/>
											) : (
												<PlayIcon className="h-16 w-16 text-white/80" />
											)}
											<div className="absolute inset-0 flex items-center justify-center">
												<Button
													variant="secondary"
													size="sm"
													className="bg-white/20 backdrop-blur-sm border-white/30"
													onClick={() => window.open(formData.videoUrl, '_blank')}
												>
													<PlayIcon className="h-4 w-4 mr-2" />
													Visualizar
												</Button>
											</div>
										</div>
									</div>
									<Button
										variant="outline"
										size="sm"
										className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm text-red-600 hover:text-red-700"
										onClick={() => handleInputChange('videoUrl', '')}
									>
										<TrashIcon className="h-3 w-3" />
									</Button>
								</div>
								<div className="space-y-4">
									<div className="p-4 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/20 rounded-lg">
										<div className="flex items-center gap-2 mb-2">
											<CheckCircleIcon className="h-4 w-4 text-green-500" />
											<span className="text-sm font-medium text-green-500">Vídeo carregado com sucesso!</span>
										</div>
										<p className="text-sm text-green-500/80">
											O vídeo está pronto para ser visualizado pelos alunos.
										</p>
									</div>
									<div className="space-y-2">
										<p className="text-sm text-muted-foreground">
											📹 Thumbnail gerado automaticamente
										</p>
										<p className="text-sm text-muted-foreground">
											🔗 URL: {formData.videoUrl.slice(0, 50)}...
										</p>
									</div>
								</div>
							</div>
						) : (
							<div
								className="border-2 border-dashed border-primary/30 rounded-lg p-12 text-center cursor-pointer hover:bg-primary/5 transition-colors"
								onClick={() => videoInputRef.current?.click()}
							>
								<div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
									<VideoIcon className="h-8 w-8 text-primary" />
								</div>
								<p className="font-medium text-base mb-2">Clique para adicionar vídeo</p>
								<p className="text-sm text-muted-foreground mb-4">
									Formatos aceitos: MP4, MOV, AVI, MKV • Tamanho máximo: 5GB
								</p>
								<Button variant="outline" className="mt-2">
									<UploadIcon className="h-4 w-4 mr-2" />
									Selecionar Arquivo
								</Button>
							</div>
						)}

												{videoUpload.uploadState.status === 'uploading' && (
							<div className="space-y-3 p-4 bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-lg">
								<div className="flex items-center justify-between text-sm">
									<div className="flex items-center gap-2">
										<div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
										<span className="font-medium text-primary">Enviando vídeo...</span>
									</div>
									<span className="text-primary/80 font-mono">{Math.round(videoUpload.uploadState.progress)}%</span>
								</div>
								<Progress
									value={videoUpload.uploadState.progress}
									className="h-2 bg-background/50 border border-primary/20"
								/>
							</div>
						)}

						<input
							ref={videoInputRef}
							type="file"
							accept="video/*"
							className="hidden"
							onChange={(e) => {
								const file = e.target.files?.[0];
								if (file) {
									handleVideoUpload(file);
								}
							}}
						/>
					</div>

					{/* Upload de Arquivos Complementares */}
					<div className="space-y-4">
						<AuxiliaryMaterialsUpload
							value={formData.files?.map(file => ({
								id: file.id,
								name: file.name,
								url: file.url,
								type: file.type,
								size: file.size,
								status: file.status,
							})) || []}
							onChange={(materials) => {
								const files: CourseLessonFile[] = materials.map(material => ({
									id: material.id,
									name: material.name,
									url: material.url,
									type: material.type,
									size: material.size,
									status: material.status,
								}));
								handleInputChange('files', files);
							}}
							organizationId={organizationId}
							label="Materiais Complementares"
							description="Faça upload de PDFs, documentos e outros materiais de apoio para esta aula"
						/>
					</div>
				</div>

				{/* Actions */}
				<div className="sticky bottom-0 bg-background border-t pt-4 mt-8 flex items-center justify-between">
					<div>
						{lesson && onDelete && (
							<Button variant="outline" onClick={handleDelete} className="text-red-600">
								<TrashIcon className="h-4 w-4 mr-2" />
								Excluir
							</Button>
						)}
					</div>
					<div className="flex gap-2">
						<Button variant="outline" onClick={onClose}>
							Cancelar
						</Button>
						<Button
							onClick={handleSave}
							className="bg-green-600 hover:bg-green-700"
						>
							<SaveIcon className="h-4 w-4 mr-2" />
							Salvar Alterações
						</Button>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
