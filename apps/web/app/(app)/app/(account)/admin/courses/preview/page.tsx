"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
	GraduationCapIcon,
	VideoIcon,
	FileTextIcon,
	ArrowLeftIcon,
} from "lucide-react";
import { Button } from "@ui/components/button";
import { useRouter, useSearchParams } from "next/navigation";
import type { CourseFormData } from "../types";

export default function CoursePreviewPage() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [courseData, setCourseData] = useState<CourseFormData | null>(null);

	useEffect(() => {
		const dataParam = searchParams.get('data');
		if (dataParam) {
			try {
				const decodedData = JSON.parse(decodeURIComponent(dataParam));
				setCourseData(decodedData);
			} catch (error) {
				console.error('Error parsing course data:', error);
			}
		}
	}, [searchParams]);

	if (!courseData) {
		return (
			<div className="min-h-screen bg-background flex items-center justify-center">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">Carregando prévia...</h1>
					<p className="text-muted-foreground">Aguarde enquanto carregamos os dados do curso</p>
				</div>
			</div>
		);
	}

	const totalLessons = courseData.modules.reduce((acc, module) =>
		acc + (module.lessons?.length || 0), 0
	);

	const totalVideos = courseData.modules.reduce((acc, module) =>
		acc + (module.lessons?.filter(lesson => lesson.videoUrl)?.length || 0), 0
	);

	const totalFiles = courseData.modules.reduce((acc, module) =>
		acc + (module.lessons?.reduce((lessonAcc, lesson) =>
			lessonAcc + (lesson.files?.length || 0), 0
		) || 0), 0
	);

	return (
		<div className="min-h-screen bg-background">
			{/* Header */}
			<div className="border-b bg-card">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-4">
							<Button
								variant="ghost"
								size="sm"
								onClick={() => window.close()}
							>
								<ArrowLeftIcon className="h-4 w-4 mr-2" />
								Fechar
							</Button>
							<div>
								<h1 className="text-xl font-semibold">Prévia do Curso</h1>
								<p className="text-sm text-muted-foreground">
									Visualização em nova aba
								</p>
							</div>
						</div>
						<Badge status="info">Prévia</Badge>
					</div>
				</div>
			</div>

			{/* Content */}
			<div className="container mx-auto px-4 py-8">
				<div className="max-w-4xl mx-auto space-y-6">
					{/* Course Header */}
					<Card>
						<CardHeader>
							<CardTitle className="text-2xl">{courseData.name}</CardTitle>
							{courseData.description && (
								<p className="text-muted-foreground text-lg">
									{courseData.description}
								</p>
							)}
						</CardHeader>
						<CardContent>
							{courseData.community && (
								<div className="flex items-center gap-2 text-sm text-muted-foreground">
									<span>Comunidade:</span>
									<span className="text-foreground">{courseData.community}</span>
								</div>
							)}
						</CardContent>
					</Card>

					{/* Course Statistics */}
					<Card>
						<CardHeader>
							<CardTitle>Estatísticas do Curso</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
								<div className="text-center p-4 bg-muted/50 rounded-lg">
									<GraduationCapIcon className="h-8 w-8 text-primary mx-auto mb-2" />
									<p className="text-lg font-semibold text-foreground">{courseData.modules.length}</p>
									<p className="text-sm text-muted-foreground">Módulos</p>
								</div>
								<div className="text-center p-4 bg-muted/50 rounded-lg">
									<VideoIcon className="h-8 w-8 text-primary mx-auto mb-2" />
									<p className="text-lg font-semibold text-foreground">{totalLessons}</p>
									<p className="text-sm text-muted-foreground">Aulas</p>
								</div>
								<div className="text-center p-4 bg-muted/50 rounded-lg">
									<VideoIcon className="h-8 w-8 text-primary mx-auto mb-2" />
									<p className="text-lg font-semibold text-foreground">{totalVideos}</p>
									<p className="text-sm text-muted-foreground">Vídeos</p>
								</div>
								<div className="text-center p-4 bg-muted/50 rounded-lg">
									<FileTextIcon className="h-8 w-8 text-primary mx-auto mb-2" />
									<p className="text-lg font-semibold text-foreground">{totalFiles}</p>
									<p className="text-sm text-muted-foreground">Materiais</p>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Course Structure */}
					<Card>
						<CardHeader>
							<CardTitle>Estrutura do Curso</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{courseData.modules.map((module, index) => (
									<Card key={module.id} className="bg-muted/20">
										<CardContent className="p-4">
											<div className="flex items-start gap-3">
												<div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold">
													{index + 1}
												</div>
												<div className="flex-1">
													<h4 className="font-semibold text-foreground text-lg">{module.name}</h4>
													{module.description && (
														<p className="text-sm text-muted-foreground mt-1">
															{module.description}
														</p>
													)}
													<div className="flex items-center gap-4 mt-2">
														<Badge status="info">
															{module.lessons?.length || 0} aulas
														</Badge>
														{module.cover && (
															<Badge status="muted">Com imagem</Badge>
														)}
													</div>

													{/* Lessons List */}
													{module.lessons && module.lessons.length > 0 && (
														<div className="mt-3 pl-4 border-l-2 border-muted space-y-2">
															{module.lessons.map((lesson, lessonIndex) => (
																<div key={lesson.id} className="flex items-center gap-2 text-sm">
																	<span className="text-muted-foreground font-medium">
																		{lessonIndex + 1}.
																	</span>
																	<span className="text-foreground">{lesson.name}</span>
																	{lesson.videoUrl && (
																		<VideoIcon className="h-3 w-3 text-green-500" />
																	)}
																	{lesson.files && lesson.files.length > 0 && (
																		<Badge status="muted" className="text-xs">
																			{lesson.files.length} arquivos
																		</Badge>
																	)}
																	{lesson.duration && (
																		<Badge status="muted" className="text-xs">
																			{lesson.duration}
																		</Badge>
																	)}
																</div>
															))}
														</div>
													)}
												</div>
											</div>
										</CardContent>
									</Card>
								))}
							</div>
						</CardContent>
					</Card>

					{/* Footer */}
					<div className="text-center py-8">
						<p className="text-sm text-muted-foreground">
							Esta é uma prévia do curso. Para criar o curso, volte à página de criação.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
