"use client";

import { useCallback, useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Progress } from "@ui/components/progress";
import { Badge } from "@ui/components/badge";
import {
	UploadIcon,
	ImageIcon,
	XIcon,
	LoaderIcon,
	AlertCircleIcon,
} from "lucide-react";
import { useImageUpload } from "../hooks/useImageUpload";

interface ImageUploadProps {
	value?: string;
	onChange: (url: string) => void;
	organizationId: string;
	fileType: "course-logo" | "module-cover" | "lesson-cover";
	label?: string;
	description?: string;
	className?: string;
}

export function ImageUpload({
	value,
	onChange,
	organizationId,
	fileType,
	label = "Upload de Imagem",
	description = "Selecione uma imagem para fazer upload",
	className = "",
}: ImageUploadProps) {
	const [isDragOver, setIsDragOver] = useState(false);
	const { uploadImage, isUploading, uploadProgress, error, resetUpload } = useImageUpload({
		organizationId,
		fileType,
	});

	const handleFileSelect = useCallback(
		async (file: File) => {
			if (!file.type.startsWith("image/")) {
				return;
			}

			const uploadedUrl = await uploadImage(file);
			if (uploadedUrl) {
				onChange(uploadedUrl);
			}
		},
		[uploadImage, onChange]
	);

	const handleFileChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0];
			if (file) {
				handleFileSelect(file);
			}
		},
		[handleFileSelect]
	);

	const handleDrop = useCallback(
		(event: React.DragEvent<HTMLDivElement>) => {
			event.preventDefault();
			setIsDragOver(false);

			const file = event.dataTransfer.files[0];
			if (file) {
				handleFileSelect(file);
			}
		},
		[handleFileSelect]
	);

	const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		setIsDragOver(true);
	}, []);

	const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		setIsDragOver(false);
	}, []);

	const handleRemove = useCallback(() => {
		onChange("");
		resetUpload();
	}, [onChange, resetUpload]);

	return (
		<div className={`space-y-3 ${className}`}>
			<label className="text-sm font-medium">{label}</label>

			{/* Current Image Preview */}
			{value && !isUploading && (
				<div className="relative inline-block">
					<img
						src={value}
						alt="Preview"
						className="w-32 h-32 object-cover rounded-lg border"
					/>
					<Button
						type="button"
						variant="outline"
						size="sm"
						className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 hover:bg-red-600 text-white border-red-500"
						onClick={handleRemove}
					>
						<XIcon className="h-3 w-3" />
					</Button>
				</div>
			)}

			{/* Upload Area */}
			{!value && (
				<div
					className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
						isDragOver
							? "border-primary bg-primary/5"
							: "border-muted-foreground/25 hover:border-primary/50"
					}`}
					onDrop={handleDrop}
					onDragOver={handleDragOver}
					onDragLeave={handleDragLeave}
				>
					{isUploading ? (
						<div className="space-y-3">
							<LoaderIcon className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
							<p className="text-sm text-muted-foreground">Fazendo upload...</p>
							<Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
						</div>
					) : (
						<>
							<ImageIcon className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
							<p className="text-sm text-muted-foreground mb-2">{description}</p>
							<p className="text-xs text-muted-foreground mb-4">
								Arraste e solte uma imagem aqui ou clique para selecionar
							</p>
							<div className="space-y-2 flex w-full justify-center">
								<Input
									id={`file-${fileType}`}
									type="file"
									accept="image/*"
									onChange={handleFileChange}
									className="hidden"
								/>
								<Button
									type="button"
									variant="outline"
									onClick={() => document.getElementById(`file-${fileType}`)?.click()}
								>
									<UploadIcon className="h-4 w-4 mr-2" />
									Selecionar Imagem
								</Button>
							</div>
						</>
					)}
				</div>
			)}

			{/* Error State */}
			{error && (
				<div className="flex items-center gap-2 text-sm text-red-600">
					<AlertCircleIcon className="h-4 w-4" />
					<span>{error}</span>
				</div>
			)}

			{/* File Info */}
			<p className="text-xs text-muted-foreground">
				Formatos aceitos: JPG, PNG, GIF. Tamanho máximo: 5MB
			</p>
		</div>
	);
}
