import { useState, useEffect } from "react";
import { apiClient } from "@shared/lib/api-client";

interface Organization {
	id: string;
	name: string;
	slug?: string;
	logo?: string;
	role?: string;
}

export function useOrganizations() {
	const [organizations, setOrganizations] = useState<Organization[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchOrganizations = async () => {
			try {
				setIsLoading(true);
				setError(null);

				// Fetch user's organizations from the API
				const response = await apiClient.organizations.user.$get();

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error((errorData as any).error || "Failed to fetch organizations");
				}

				const organizationsData = await response.json();
				// Transform the data to match the Organization interface
				const transformedData = organizationsData.map((org: any) => ({
					...org,
					slug: org.slug || undefined, // Convert null to undefined
					logo: org.logo || undefined, // Convert null to undefined
				}));
				setOrganizations(transformedData);
			} catch (err) {
				console.error("Error fetching organizations:", err);
				setError(err instanceof Error ? err.message : "Failed to fetch organizations");
			} finally {
				setIsLoading(false);
			}
		};

		fetchOrganizations();
	}, []);

	return {
		organizations,
		isLoading,
		error,
	};
}
