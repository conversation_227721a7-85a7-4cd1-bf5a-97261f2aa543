import { useState, useCallback } from 'react'
import { toast } from 'sonner'
import { apiClient } from "@shared/lib/api-client";

interface S3FileUploadState {
  isUploading: boolean
  progress: number
  status: 'idle' | 'uploading' | 'completed' | 'error'
  error?: string
  fileUrl?: string
}

interface UseS3FileUploadOptions {
  onProgress?: (progress: number) => void
  onSuccess?: (fileUrl: string, fileName?: string) => void
  onError?: (error: string, fileName?: string) => void
  organizationId?: string
  lessonId?: string
  moduleId?: string
}

export function useS3FileUpload(options: UseS3FileUploadOptions = {}) {
  const [uploadState, setUploadState] = useState<S3FileUploadState>({
    isUploading: false,
    progress: 0,
    status: 'idle'
  })

  const uploadFile = useCallback(async (file: File, folderPath = 'course-materials') => {
    if (!options.organizationId) {
      const error = 'Organization ID is required for file upload'
      toast.error(error)
      options.onError?.(error)
      return
    }

    try {
      setUploadState({
        isUploading: true,
        progress: 0,
        status: 'uploading'
      })

      toast.loading(`📁 Enviando ${file.name}...`, { id: 'file-upload' })

      // Step 1: Get signed upload URL from our API
      const uploadUrlResponse = await apiClient.courses.files.$post({
        json: {
          fileName: file.name,
          fileType: folderPath as any,
          organizationId: options.organizationId,
          lessonId: options.lessonId,
          moduleId: options.moduleId,
        },
      });

      if (!uploadUrlResponse.ok) {
        const errorData = await uploadUrlResponse.json();
        throw new Error((errorData as any).error || "Failed to get upload URL");
      }

      const { uploadUrl, publicUrl } = await uploadUrlResponse.json();

      // Step 2: Upload file directly to S3 using the signed URL
      const uploadResponse = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload file to storage");
      }

      setUploadState({
        isUploading: false,
        progress: 100,
        status: 'completed',
        fileUrl: publicUrl
      })

      toast.dismiss('file-upload')
      toast.success(`✅ ${file.name} enviado com sucesso!`)
      options.onSuccess?.(publicUrl, file.name)

      return publicUrl

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro no upload do arquivo'

      setUploadState({
        isUploading: false,
        progress: 0,
        status: 'error',
        error: errorMessage
      })

      toast.dismiss('file-upload')
      toast.error(errorMessage)
      options.onError?.(errorMessage, file.name)
      throw error
    }
  }, [options])

  const resetUpload = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      status: 'idle'
    })
  }, [])

  return {
    uploadState,
    uploadFile,
    resetUpload
  }
}
