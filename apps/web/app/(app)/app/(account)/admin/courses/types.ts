export interface CourseFormData {
	id?: string;
	name: string;
	description?: string;
	organizationId: string;
	community?: string;
	link?: string;
	logo?: string;
	modules: CourseModule[];
	settings?: CourseSettings;
}

export interface CourseSettings {
	visibility: 'public' | 'private' | 'unlisted';
	pricing: 'free' | 'paid';
	price?: number;
	currency: string;
	allowComments: boolean;
	certificateEnabled: boolean;
	maxStudents?: number;
}

export interface CourseModule {
	id?: string;
	name: string;
	description?: string;
	position: number;
	cover?: string;
	lessons?: CourseLesson[];
}

export interface CourseLesson {
	id?: string;
	name: string;
	description?: string;
	duration?: string;
	position: number;
	videoUrl?: string;
	thumbnail?: string;
	files?: CourseLessonFile[];
}

export interface CourseLessonFile {
	id?: string;
	name: string;
	url: string;
	type: 'pdf' | 'document' | 'image' | 'video' | 'audio' | 'other';
	size: number;
	uploadProgress?: number;
	status?: 'pending' | 'uploading' | 'completed' | 'error';
}



export interface FileUploadState {
	[key: string]: {
		progress: number;
		status: 'pending' | 'uploading' | 'completed' | 'error';
		error?: string;
	};
}
