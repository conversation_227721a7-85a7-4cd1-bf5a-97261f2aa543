"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
	EyeIcon,
	GraduationCapIcon,
	VideoIcon,
	FileTextIcon,
} from "lucide-react";
import type { CourseFormData } from "../types";

interface CoursePreviewProps {
	data: CourseFormData;
	onPrevious: () => void;
}

export function CoursePreview({ data, onPrevious }: CoursePreviewProps) {
	const totalLessons = data.modules.reduce((acc, module) =>
		acc + (module.lessons?.length || 0), 0
	);

	const totalVideos = data.modules.reduce((acc, module) =>
		acc + (module.lessons?.filter(lesson => lesson.videoUrl)?.length || 0), 0
	);

	const totalFiles = data.modules.reduce((acc, module) =>
		acc + (module.lessons?.reduce((lessonAcc, lesson) =>
			lessonAcc + (lesson.files?.length || 0), 0
		) || 0), 0
	);

	return (
		<div className="max-w-4xl mx-auto space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<EyeIcon className="h-5 w-5 text-primary" />
						Prévia do Curso
					</CardTitle>
					<p className="text-muted-foreground">
						Revise todas as informações antes de criar o curso
					</p>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Basic Information */}
					<div>
						<h3 className="font-semibold text-foreground mb-3">Informações Básicas</h3>
						<div className="space-y-2">
							<div>
								<span className="text-sm font-medium text-muted-foreground">Nome:</span>
								<p className="text-foreground">{data.name}</p>
							</div>
							{data.description && (
								<div>
									<span className="text-sm font-medium text-muted-foreground">Descrição:</span>
									<p className="text-foreground">{data.description}</p>
								</div>
							)}
							{data.community && (
								<div>
									<span className="text-sm font-medium text-muted-foreground">Comunidade:</span>
									<p className="text-foreground">{data.community}</p>
								</div>
							)}
						</div>
					</div>

					{/* Course Statistics */}
					<div>
						<h3 className="font-semibold text-foreground mb-3">Estatísticas do Curso</h3>
						<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
							<div className="text-center p-4 bg-muted/50 rounded-lg">
								<GraduationCapIcon className="h-8 w-8 text-primary mx-auto mb-2" />
								<p className="text-lg font-semibold text-foreground">{data.modules.length}</p>
								<p className="text-sm text-muted-foreground">Módulos</p>
							</div>
							<div className="text-center p-4 bg-muted/50 rounded-lg">
								<VideoIcon className="h-8 w-8 text-primary mx-auto mb-2" />
								<p className="text-lg font-semibold text-foreground">{totalLessons}</p>
								<p className="text-sm text-muted-foreground">Aulas</p>
							</div>
							<div className="text-center p-4 bg-muted/50 rounded-lg">
								<VideoIcon className="h-8 w-8 text-primary mx-auto mb-2" />
								<p className="text-lg font-semibold text-foreground">{totalVideos}</p>
								<p className="text-sm text-muted-foreground">Vídeos</p>
							</div>
							<div className="text-center p-4 bg-muted/50 rounded-lg">
								<FileTextIcon className="h-8 w-8 text-primary mx-auto mb-2" />
								<p className="text-lg font-semibold text-foreground">{totalFiles}</p>
								<p className="text-sm text-muted-foreground">Materiais</p>
							</div>
						</div>
					</div>

					{/* Course Structure */}
					<div>
						<h3 className="font-semibold text-foreground mb-3">Estrutura do Curso</h3>
						<div className="space-y-3">
							{data.modules.map((module, index) => (
								<Card key={module.id} className="bg-muted/20">
									<CardContent className="p-4">
										<div className="flex items-start gap-3">
											<div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold">
												{index + 1}
											</div>
											<div className="flex-1">
												<h4 className="font-semibold text-foreground">{module.name}</h4>
												{module.description && (
													<p className="text-sm text-muted-foreground mt-1">
														{module.description}
													</p>
												)}
												<div className="flex items-center gap-4 mt-2">
													<Badge status="info">
														{module.lessons?.length || 0} aulas
													</Badge>
													{module.cover && (
														<Badge status="muted">Com imagem</Badge>
													)}
												</div>

												{/* Lessons List */}
												{module.lessons && module.lessons.length > 0 && (
													<div className="mt-3 pl-4 border-l-2 border-muted space-y-1">
														{module.lessons.map((lesson, lessonIndex) => (
															<div key={lesson.id} className="flex items-center gap-2 text-sm">
																<span className="text-muted-foreground">
																	{lessonIndex + 1}.
																</span>
																<span className="text-foreground">{lesson.name}</span>
																{lesson.videoUrl && (
																	<VideoIcon className="h-3 w-3 text-green-500" />
																)}
																{lesson.files && lesson.files.length > 0 && (
																	<Badge status="muted" className="text-xs">
																		{lesson.files.length} arquivos
																	</Badge>
																)}
																{lesson.duration && (
																	<Badge status="muted" className="text-xs">
																		{lesson.duration}
																	</Badge>
																)}
															</div>
														))}
													</div>
												)}
											</div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
