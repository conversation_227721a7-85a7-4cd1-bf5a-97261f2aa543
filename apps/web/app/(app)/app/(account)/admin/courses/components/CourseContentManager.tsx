"use client";

import { useState, useCallback, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "@ui/components/tabs";
import {
	UploadIcon,
	VideoIcon,
	FileTextIcon,
	ImageIcon,
	XIcon,
	CheckCircleIcon,
	AlertCircleIcon,
	PlayIcon,
	DownloadIcon,
	RefreshCwIcon,
	PlusIcon,
	TrashIcon,
} from "lucide-react";
import { toast } from "sonner";
import type { CourseFormData, CourseModule, CourseLesson, CourseLessonFile, FileUploadState } from "../types";

interface CourseContentManagerProps {
	data: CourseFormData;
	onUpdate: (data: Partial<CourseFormData>) => void;
	onNext: () => void;
	onPrevious: () => void;
}

interface FileUploadProgress {
	[key: string]: {
		progress: number;
		status: 'pending' | 'uploading' | 'completed' | 'error';
		error?: string;
	};
}

const ACCEPTED_VIDEO_TYPES = {
	'video/mp4': ['.mp4'],
	'video/quicktime': ['.mov'],
	'video/x-msvideo': ['.avi'],
	'video/x-matroska': ['.mkv'],
	'video/webm': ['.webm'],
};

const ACCEPTED_FILE_TYPES = {
	'application/pdf': ['.pdf'],
	'application/msword': ['.doc'],
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
	'application/vnd.ms-powerpoint': ['.ppt'],
	'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
	'image/jpeg': ['.jpg', '.jpeg'],
	'image/png': ['.png'],
	'image/gif': ['.gif'],
	'text/plain': ['.txt'],
};

export function CourseContentManager({ data, onUpdate, onNext, onPrevious }: CourseContentManagerProps) {
	const [uploadProgress, setUploadProgress] = useState<FileUploadProgress>({});
	const [isLessonDialogOpen, setIsLessonDialogOpen] = useState(false);
	const [editingLesson, setEditingLesson] = useState<{ moduleId: string; lesson?: CourseLesson } | null>(null);
	const [draggedOver, setDraggedOver] = useState<string | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const videoInputRef = useRef<HTMLInputElement>(null);

	const getFileType = useCallback((file: File): CourseLessonFile['type'] => {
		if (file.type.startsWith('video/')) return 'video';
		if (file.type === 'application/pdf') return 'pdf';
		if (file.type.startsWith('image/')) return 'image';
		if (file.type.includes('document') || file.type.includes('word') || file.type.includes('presentation')) return 'document';
		return 'other';
	}, []);

	const formatFileSize = useCallback((bytes: number): string => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}, []);

	const simulateUpload = useCallback((fileId: string, file: File): Promise<string> => {
		return new Promise((resolve, reject) => {
			setUploadProgress(prev => ({
				...prev,
				[fileId]: { progress: 0, status: 'uploading' }
			}));

			const interval = setInterval(() => {
				setUploadProgress(prev => {
					const current = prev[fileId]?.progress || 0;
					const newProgress = Math.min(current + Math.random() * 15, 100);

					if (newProgress >= 100) {
						clearInterval(interval);

						// Simulate occasional errors (5% chance)
						const hasError = Math.random() < 0.05;

						if (hasError) {
							const error = 'Erro no upload. Tente novamente.';
							reject(new Error(error));
							return {
								...prev,
								[fileId]: {
									progress: 100,
									status: 'error',
									error
								}
							};
						} else {
							// Generate a mock URL for the uploaded file
							const mockUrl = `https://storage.example.com/courses/${fileId}`;
							resolve(mockUrl);
							return {
								...prev,
								[fileId]: { progress: 100, status: 'completed' }
							};
						}
					}

					return {
						...prev,
						[fileId]: { progress: newProgress, status: 'uploading' }
					};
				});
			}, 200);
		});
	}, []);

	const handleFileUpload = useCallback(async (moduleId: string, lessonId: string, files: FileList | File[]) => {
		const fileArray = Array.from(files);
		const updatedModules = [...data.modules];
		const moduleIndex = updatedModules.findIndex(m => m.id === moduleId);
		const lessonIndex = updatedModules[moduleIndex].lessons?.findIndex(l => l.id === lessonId) ?? -1;

		if (moduleIndex === -1 || lessonIndex === -1) return;

		for (const file of fileArray) {
			const fileId = `file-${Date.now()}-${Math.random()}`;
			const newFile: CourseLessonFile = {
				id: fileId,
				name: file.name,
				url: '',
				type: getFileType(file),
				size: file.size,
				uploadProgress: 0,
				status: 'uploading',
			};

			// Add file to lesson
			const lesson = updatedModules[moduleIndex].lessons![lessonIndex];
			lesson.files = [...(lesson.files || []), newFile];

			// Update parent data
			onUpdate({ modules: updatedModules });

			try {
				const url = await simulateUpload(fileId, file);

				// Update file with URL and completion status
				const currentModules = [...data.modules];
				const currentLesson = currentModules[moduleIndex].lessons![lessonIndex];
				const fileToUpdate = currentLesson.files?.find(f => f.id === fileId);

				if (fileToUpdate) {
					fileToUpdate.url = url;
					fileToUpdate.status = 'completed';
					fileToUpdate.uploadProgress = 100;
					onUpdate({ modules: currentModules });
					toast.success(`${file.name} enviado com sucesso!`);
				}
			} catch (error) {
				// Update file with error status
				const currentModules = [...data.modules];
				const currentLesson = currentModules[moduleIndex].lessons![lessonIndex];
				const fileToUpdate = currentLesson.files?.find(f => f.id === fileId);

				if (fileToUpdate) {
					fileToUpdate.status = 'error';
					onUpdate({ modules: currentModules });
					toast.error(`Erro ao enviar ${file.name}`);
				}
			}
		}
	}, [data.modules, getFileType, onUpdate, simulateUpload]);

	const handleVideoUpload = useCallback(async (moduleId: string, lessonId: string, file: File) => {
		const updatedModules = [...data.modules];
		const moduleIndex = updatedModules.findIndex(m => m.id === moduleId);
		const lessonIndex = updatedModules[moduleIndex].lessons?.findIndex(l => l.id === lessonId) ?? -1;

		if (moduleIndex === -1 || lessonIndex === -1) return;

		const fileId = `video-${Date.now()}`;

		try {
			const url = await simulateUpload(fileId, file);

			// Update lesson with video URL
			const lesson = updatedModules[moduleIndex].lessons![lessonIndex];
			lesson.videoUrl = url;
			lesson.thumbnail = `${url}_thumbnail.jpg`; // Mock thumbnail

			onUpdate({ modules: updatedModules });
			toast.success('Vídeo enviado com sucesso!');
		} catch (error) {
			toast.error('Erro ao enviar vídeo');
		}
	}, [data.modules, onUpdate, simulateUpload]);

	const handleDeleteFile = useCallback((moduleId: string, lessonId: string, fileId: string) => {
		if (confirm('Tem certeza que deseja excluir este arquivo?')) {
			const updatedModules = [...data.modules];
			const moduleIndex = updatedModules.findIndex(m => m.id === moduleId);
			const lessonIndex = updatedModules[moduleIndex].lessons?.findIndex(l => l.id === lessonId) ?? -1;

			if (moduleIndex !== -1 && lessonIndex !== -1) {
				const lesson = updatedModules[moduleIndex].lessons![lessonIndex];
				lesson.files = lesson.files?.filter(f => f.id !== fileId) || [];
				onUpdate({ modules: updatedModules });
				toast.success('Arquivo removido');
			}
		}
	}, [data.modules, onUpdate]);

	const handleDeleteVideo = useCallback((moduleId: string, lessonId: string) => {
		if (confirm('Tem certeza que deseja remover o vídeo desta aula?')) {
			const updatedModules = [...data.modules];
			const moduleIndex = updatedModules.findIndex(m => m.id === moduleId);
			const lessonIndex = updatedModules[moduleIndex].lessons?.findIndex(l => l.id === lessonId) ?? -1;

			if (moduleIndex !== -1 && lessonIndex !== -1) {
				const lesson = updatedModules[moduleIndex].lessons![lessonIndex];
				lesson.videoUrl = undefined;
				lesson.thumbnail = undefined;
				onUpdate({ modules: updatedModules });
				toast.success('Vídeo removido');
			}
		}
	}, [data.modules, onUpdate]);

	const retryUpload = useCallback(async (moduleId: string, lessonId: string, fileId: string) => {
		const updatedModules = [...data.modules];
		const moduleIndex = updatedModules.findIndex(m => m.id === moduleId);
		const lessonIndex = updatedModules[moduleIndex].lessons?.findIndex(l => l.id === lessonId) ?? -1;

		if (moduleIndex === -1 || lessonIndex === -1) return;

		const lesson = updatedModules[moduleIndex].lessons![lessonIndex];
		const file = lesson.files?.find(f => f.id === fileId);

		if (file) {
			file.status = 'uploading';
			file.uploadProgress = 0;
			onUpdate({ modules: updatedModules });

			// Create a mock file for re-upload
			const mockFile = new File([''], file.name, { type: 'application/octet-stream' });

			try {
				const url = await simulateUpload(fileId, mockFile);
				file.url = url;
				file.status = 'completed';
				file.uploadProgress = 100;
				onUpdate({ modules: updatedModules });
				toast.success(`${file.name} reenviado com sucesso!`);
			} catch (error) {
				file.status = 'error';
				onUpdate({ modules: updatedModules });
				toast.error(`Erro ao reenviar ${file.name}`);
			}
		}
	}, [data.modules, onUpdate, simulateUpload]);

	const getStatusIcon = useCallback((status: CourseLessonFile['status']) => {
		switch (status) {
			case 'uploading':
				return <RefreshCwIcon className="h-4 w-4 animate-spin text-blue-500" />;
			case 'completed':
				return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
			case 'error':
				return <AlertCircleIcon className="h-4 w-4 text-red-500" />;
			default:
				return null;
		}
	}, []);

	const getFileIcon = useCallback((type: CourseLessonFile['type']) => {
		switch (type) {
			case 'video':
				return <VideoIcon className="h-4 w-4" />;
			case 'pdf':
			case 'document':
				return <FileTextIcon className="h-4 w-4" />;
			case 'image':
				return <ImageIcon className="h-4 w-4" />;
			default:
				return <FileTextIcon className="h-4 w-4" />;
		}
	}, []);

	const onDragOver = useCallback((e: React.DragEvent, targetId: string) => {
		e.preventDefault();
		setDraggedOver(targetId);
	}, []);

	const onDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setDraggedOver(null);
	}, []);

	const onDrop = useCallback((e: React.DragEvent, moduleId: string, lessonId: string) => {
		e.preventDefault();
		setDraggedOver(null);

		const files = e.dataTransfer.files;
		if (files.length > 0) {
			handleFileUpload(moduleId, lessonId, files);
		}
	}, [handleFileUpload]);

	const canProceed = data.modules.some(module =>
		module.lessons?.some(lesson =>
			lesson.videoUrl || (lesson.files && lesson.files.length > 0)
		)
	);

	return (
		<div className="max-w-6xl mx-auto space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<UploadIcon className="h-5 w-5 text-primary" />
						Gerenciamento de Conteúdo
					</CardTitle>
					<p className="text-muted-foreground">
						Faça upload de vídeos e materiais complementares para cada aula
					</p>
				</CardHeader>
				<CardContent>
					{data.modules.length === 0 ? (
						<div className="text-center py-12">
							<VideoIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
							<h3 className="text-lg font-semibold text-foreground mb-2">
								Nenhum módulo disponível
							</h3>
							<p className="text-muted-foreground">
								Crie módulos e aulas primeiro para gerenciar o conteúdo
							</p>
						</div>
					) : (
						<div className="space-y-6">
							{data.modules.map((module) => (
								<Card key={module.id} className="border-l-4 border-l-primary">
									<CardHeader className="pb-4">
										<CardTitle className="text-lg">{module.name}</CardTitle>
										{module.description && (
											<p className="text-sm text-muted-foreground">{module.description}</p>
										)}
									</CardHeader>
									<CardContent>
										{!module.lessons || module.lessons.length === 0 ? (
											<div className="text-center py-8 bg-muted/30 rounded-lg">
												<VideoIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
												<p className="text-sm text-muted-foreground">
													Nenhuma aula criada neste módulo
												</p>
											</div>
										) : (
											<div className="space-y-4">
												{module.lessons.map((lesson) => (
													<Card key={lesson.id} className="bg-muted/20">
														<CardContent className="p-6">
															<div className="space-y-4">
																{/* Lesson Header */}
																<div className="flex items-start justify-between">
																	<div>
																		<h4 className="font-semibold text-foreground">{lesson.name}</h4>
																		{lesson.description && (
																			<p className="text-sm text-muted-foreground mt-1">
																				{lesson.description}
																			</p>
																		)}
																		{lesson.duration && (
																			<Badge status="muted" className="mt-2">
																				{lesson.duration}
																			</Badge>
																		)}
																	</div>
																</div>

																{/* Video Section */}
																<div className="space-y-3">
																	<h5 className="font-medium text-foreground flex items-center gap-2">
																		<VideoIcon className="h-4 w-4" />
																		Vídeo da Aula
																	</h5>

																	{lesson.videoUrl ? (
																		<div className="flex items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
																			<div className="flex items-center gap-3 flex-1">
																				{lesson.thumbnail && (
																					<img
																						src={lesson.thumbnail}
																						alt="Thumbnail"
																						className="w-16 h-9 object-cover rounded border"
																					/>
																				)}
																				<div>
																					<p className="font-medium text-green-800">Vídeo carregado</p>
																					<p className="text-sm text-green-600">Pronto para visualização</p>
																				</div>
																			</div>
																			<div className="flex gap-2">
																				<Button
																					variant="outline"
																					size="sm"
																					onClick={() => window.open(lesson.videoUrl, '_blank')}
																				>
																					<PlayIcon className="h-3 w-3 mr-1" />
																					Ver
																				</Button>
																				<Button
																					variant="outline"
																					size="sm"
																					onClick={() => handleDeleteVideo(module.id!, lesson.id!)}
																					className="text-red-600 hover:text-red-700"
																				>
																					<TrashIcon className="h-3 w-3" />
																				</Button>
																			</div>
																		</div>
																	) : (
																		<div className="space-y-2">
																			<div
																				className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
																					draggedOver === `video-${lesson.id}`
																						? 'border-primary bg-primary/5'
																						: 'border-muted-foreground/25 hover:border-muted-foreground/50'
																				}`}
																				onDragOver={(e) => onDragOver(e, `video-${lesson.id}`)}
																				onDragLeave={onDragLeave}
																				onDrop={(e) => {
																					e.preventDefault();
																					setDraggedOver(null);
																					const files = e.dataTransfer.files;
																					if (files.length > 0 && files[0].type.startsWith('video/')) {
																						handleVideoUpload(module.id!, lesson.id!, files[0]);
																					}
																				}}
																				onClick={() => {
																					const input = document.createElement('input');
																					input.type = 'file';
																					input.accept = Object.keys(ACCEPTED_VIDEO_TYPES).join(',');
																					input.onchange = (e) => {
																						const file = (e.target as HTMLInputElement).files?.[0];
																						if (file) {
																							handleVideoUpload(module.id!, lesson.id!, file);
																						}
																					};
																					input.click();
																				}}
																			>
																				<VideoIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
																				<p className="font-medium text-foreground mb-1">
																					Arraste um vídeo ou clique para selecionar
																				</p>
																				<p className="text-sm text-muted-foreground">
																					MP4, MOV, AVI, MKV, WebM (máx. 2GB)
																				</p>
																			</div>
																		</div>
																	)}

																	{/* Upload Progress for Video */}
																	{uploadProgress[`video-${lesson.id}`] && (
																		<div className="space-y-2">
																			<div className="flex items-center justify-between text-sm">
																				<span>Enviando vídeo...</span>
																				<span>{Math.round(uploadProgress[`video-${lesson.id}`].progress)}%</span>
																			</div>
																			<Progress value={uploadProgress[`video-${lesson.id}`].progress} />
																		</div>
																	)}
																</div>

																{/* Files Section */}
																<div className="space-y-3">
																	<div className="flex items-center justify-between">
																		<h5 className="font-medium text-foreground flex items-center gap-2">
																			<FileTextIcon className="h-4 w-4" />
																			Materiais Complementares
																		</h5>
																		<Button
																			variant="outline"
																			size="sm"
																			onClick={() => {
																				const input = document.createElement('input');
																				input.type = 'file';
																				input.multiple = true;
																				input.accept = Object.keys(ACCEPTED_FILE_TYPES).join(',');
																				input.onchange = (e) => {
																					const files = (e.target as HTMLInputElement).files;
																					if (files && files.length > 0) {
																						handleFileUpload(module.id!, lesson.id!, files);
																					}
																				};
																				input.click();
																			}}
																		>
																			<PlusIcon className="h-3 w-3 mr-1" />
																			Adicionar Arquivo
																		</Button>
																	</div>

																	{/* Drag and Drop Area for Files */}
																	<div
																		className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
																			draggedOver === `files-${lesson.id}`
																				? 'border-primary bg-primary/5'
																				: 'border-muted-foreground/25 hover:border-muted-foreground/50'
																		}`}
																		onDragOver={(e) => onDragOver(e, `files-${lesson.id}`)}
																		onDragLeave={onDragLeave}
																		onDrop={(e) => onDrop(e, module.id!, lesson.id!)}
																		onClick={() => {
																			const input = document.createElement('input');
																			input.type = 'file';
																			input.multiple = true;
																			input.accept = Object.keys(ACCEPTED_FILE_TYPES).join(',');
																			input.onchange = (e) => {
																				const files = (e.target as HTMLInputElement).files;
																				if (files && files.length > 0) {
																					handleFileUpload(module.id!, lesson.id!, files);
																				}
																			};
																			input.click();
																		}}
																	>
																		<FileTextIcon className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
																		<p className="text-sm font-medium text-foreground mb-1">
																			Arraste arquivos ou clique para selecionar
																		</p>
																		<p className="text-xs text-muted-foreground">
																			PDF, DOC, PPT, Imagens, TXT (máx. 50MB cada)
																		</p>
																	</div>

																	{/* Files List */}
																	{lesson.files && lesson.files.length > 0 && (
																		<div className="space-y-2">
																			{lesson.files.map((file) => (
																				<div key={file.id} className="flex items-center gap-3 p-3 bg-background border rounded-lg">
																					<div className="flex items-center gap-2 flex-1">
																						{getFileIcon(file.type)}
																						<div className="flex-1 min-w-0">
																							<p className="font-medium text-sm truncate">{file.name}</p>
																							<p className="text-xs text-muted-foreground">
																								{formatFileSize(file.size)} • {file.type}
																							</p>
																						</div>
																					</div>

																					{/* Upload Progress */}
																					{file.status === 'uploading' && file.uploadProgress !== undefined && (
																						<div className="flex items-center gap-2 min-w-0 flex-1">
																							<Progress value={file.uploadProgress} className="flex-1" />
																							<span className="text-xs text-muted-foreground whitespace-nowrap">
																								{Math.round(file.uploadProgress)}%
																							</span>
																						</div>
																					)}

																					{/* Status and Actions */}
																					<div className="flex items-center gap-2">
																						{getStatusIcon(file.status)}

																						{file.status === 'completed' && (
																							<Button
																								variant="ghost"
																								size="sm"
																								onClick={() => window.open(file.url, '_blank')}
																							>
																								<DownloadIcon className="h-3 w-3" />
																							</Button>
																						)}

																						{file.status === 'error' && (
																							<Button
																								variant="ghost"
																								size="sm"
																								onClick={() => retryUpload(module.id!, lesson.id!, file.id!)}
																							>
																								<RefreshCwIcon className="h-3 w-3" />
																							</Button>
																						)}

																						<Button
																							variant="ghost"
																							size="sm"
																							onClick={() => handleDeleteFile(module.id!, lesson.id!, file.id!)}
																							className="text-red-600 hover:text-red-700"
																						>
																							<XIcon className="h-3 w-3" />
																						</Button>
																					</div>
																				</div>
																			))}
																		</div>
																	)}
																</div>
															</div>
														</CardContent>
													</Card>
												))}
											</div>
										)}
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</CardContent>
			</Card>

		</div>
	);
}
