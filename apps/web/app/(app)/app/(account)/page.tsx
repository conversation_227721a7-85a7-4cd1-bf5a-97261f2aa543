import { getSession } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { UnifiedAdminDashboard } from "@saas/start/components/UnifiedAdminDashboard";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";

export default async function AppStartPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	const t = await getTranslations();

	return (
		<div className="">
			<UnifiedAdminDashboard />
		</div>
	);
}
