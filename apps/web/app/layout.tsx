import type { Metadata } from "next";
import type { PropsWithChildren } from "react";
import "./globals.css";
import "cropperjs/dist/cropper.css";
import { config } from "@repo/config";
import { Footer } from "@saas/shared/components/Footer";
import { GeistSans } from "geist/font/sans";
import { ApiClientProvider } from "@shared/components/ApiClientProvider";
import { Provider as JotaiProvider } from "jotai";
import { ThemeProvider } from "next-themes";
import { ConsentProvider } from "@shared/components/ConsentProvider";
import { ConsentBanner } from "@shared/components/ConsentBanner";
import { Toaster } from "@ui/components/toast";
import { AnalyticsScript } from "@analytics";
import NextTopLoader from "nextjs-toploader";
import { NuqsAdapter } from "nuqs/adapters/next/app";

export const metadata: Metadata = {
	title: {
		absolute: config.appName,
		default: config.appName,
		template: `%s | ${config.appName}`,
	},
};

export default function RootLayout({ children }: PropsWithChildren) {
	return (
		<html lang="pt" suppressHydrationWarning className={GeistSans.variable}>
			<body className="min-h-screen flex flex-col">
				<NuqsAdapter>
					<ConsentProvider>
						<NextTopLoader color="var(--color-primary)" />
						<ThemeProvider
							attribute="class"
							disableTransitionOnChange
							enableSystem
							defaultTheme={config.ui.defaultTheme}
							themes={config.ui.enabledThemes}
						>
							<ApiClientProvider>
								<JotaiProvider>{children}</JotaiProvider>
							</ApiClientProvider>
						</ThemeProvider>
						<Toaster position="top-right" />
						<AnalyticsScript />
					</ConsentProvider>
				</NuqsAdapter>

			</body>
		</html>
	);
}
