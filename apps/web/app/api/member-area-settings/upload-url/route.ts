import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { getSignedUploadUrl, uploadFile, getSignedUrl } from "@repo/storage";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const session = await getSession();

        if (!session?.user) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const { fileName, fileType, organizationId, fileData } = await request.json();

        if (!fileName || !fileType || !organizationId) {
            return NextResponse.json({ error: "Dados obrigatórios não fornecidos" }, { status: 400 });
        }

        // Verificar se o usuário é admin da organização
        const membership = await db.member.findFirst({
            where: {
                userId: session.user.id,
                organizationId,
            },
        });

        if (!membership) {
            return NextResponse.json({ error: "Usuário não é membro desta organização" }, { status: 403 });
        }

        if (membership.role === "member") {
            return NextResponse.json({ error: "Apenas administradores podem fazer upload de imagens" }, { status: 403 });
        }

        // Gerar um caminho único para o arquivo
        const fileExtension = fileName.split('.').pop();
        const timestamp = Date.now();
        const uniqueFileName = `${timestamp}_${Math.random().toString(36).substring(7)}.${fileExtension}`;
        const filePath = `member-area-settings/${organizationId}/${fileType}/${uniqueFileName}`;

        // Obter URL de upload assinada do provedor de armazenamento
        const bucket = process.env.S3_BUCKET_NAME;
        if (!bucket) {
            return NextResponse.json({ error: "Configuração de armazenamento ausente" }, { status: 500 });
        }

        // Determinar o tipo de conteúdo baseado na extensão do arquivo
        const getContentType = (fileName: string) => {
            const ext = fileName.split('.').pop()?.toLowerCase();
            switch (ext) {
                case 'jpg':
                case 'jpeg':
                    return 'image/jpeg';
                case 'png':
                    return 'image/png';
                case 'gif':
                    return 'image/gif';
                case 'webp':
                    return 'image/webp';
                case 'svg':
                    return 'image/svg+xml';
                default:
                    return 'image/jpeg';
            }
        };

        const contentType = getContentType(fileName);

                // If fileData is provided, upload directly through the API
        if (fileData) {
            try {
                // Convert base64 to buffer
                const buffer = Buffer.from(fileData.split(',')[1], 'base64');

                await uploadFile(filePath, buffer, {
                    bucket,
                    contentType,
                });

                // Generate a signed URL for accessing the uploaded file
                const accessUrl = await getSignedUrl(filePath, {
                    bucket,
                    expiresIn: 3600 * 24 * 7, // 7 days
                });

                return NextResponse.json({
                    filePath,
                    publicUrl: accessUrl,
                });
            } catch (uploadError) {
                console.error("Erro ao fazer upload do arquivo:", uploadError);
                return NextResponse.json({ error: "Erro ao fazer upload do arquivo" }, { status: 500 });
            }
        }

        // Otherwise, return signed URL for direct upload
        const uploadUrl = await getSignedUploadUrl(filePath, {
            bucket,
            contentType
        });

        // Retornar a URL de upload e o caminho final do arquivo
        return NextResponse.json({
            uploadUrl,
            filePath,
            publicUrl: `${process.env.CDN_BASE_URL || process.env.S3_ENDPOINT}/${bucket}/${filePath}`,
        });
    } catch (error) {
        console.error("Erro ao gerar URL de upload:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}
