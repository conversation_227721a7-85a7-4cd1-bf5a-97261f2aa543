import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";
import type { MetadataRoute } from "next";

const baseUrl = getBaseUrl();
const locales = config.i18n.enabled
	? Object.keys(config.i18n.locales)
	: [config.i18n.defaultLocale];

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	return [
		// App routes
		...locales.map((locale) => ({
			url: new URL(`/${locale}/app`, baseUrl).href,
			lastModified: new Date(),
		})),
		// Auth routes
		...locales.map((locale) => ({
			url: new URL(`/${locale}/auth/login`, baseUrl).href,
			lastModified: new Date(),
		})),
		...locales.map((locale) => ({
			url: new URL(`/${locale}/auth/signup`, baseUrl).href,
			lastModified: new Date(),
		})),
		// Legal pages - removed content-collections dependency
		...locales.map((locale) => ({
			url: new URL(`/${locale}/legal/privacy-policy`, baseUrl).href,
			lastModified: new Date(),
		})),
		...locales.map((locale) => ({
			url: new URL(`/${locale}/legal/terms`, baseUrl).href,
			lastModified: new Date(),
		})),
	];
}
