import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  // Redirecionar para a rota do Better Auth
  const url = new URL(request.url);
  const betterAuthUrl = new URL("/api/auth/oauth2/callback/django-sso", url.origin);

  // Copiar todos os parâmetros de query
  url.searchParams.forEach((value, key) => {
    betterAuthUrl.searchParams.set(key, value);
  });

  return NextResponse.redirect(betterAuthUrl);
}
