#!/usr/bin/env node

// Script para verificar e configurar variáveis de ambiente
const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando configuração de ambiente...');

// Verificar se existe arquivo .env.local
const envPath = path.join(__dirname, '../../.env.local');
const envExists = fs.existsSync(envPath);

if (!envExists) {
  console.log('⚠️  Arquivo .env.local não encontrado');
  console.log('📝 Criando arquivo .env.local com configurações básicas...');

  const defaultEnv = `# Configurações básicas para desenvolvimento
DATABASE_URL="postgresql://cakto:cakto@localhost:5432/cakto"
BETTER_AUTH_SECRET="112c209614f7bb08cf834f53d5e2b1721ba940a106b01a3769a4e9bd29cd723c697ce245a874c1ab3140142c3a4fb780c19bf1c5a302b1ee3e85a4950cc3e6c8"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
PORT=3000
NODE_ENV="development"

# Configurações opcionais
# GOOGLE_CLIENT_ID=""
# GOOGLE_CLIENT_SECRET=""
# GITHUB_CLIENT_ID=""
# GITHUB_CLIENT_SECRET=""
# RESEND_API_KEY=""
`;

  fs.writeFileSync(envPath, defaultEnv);
  console.log('✅ Arquivo .env.local criado com sucesso');
} else {
  console.log('✅ Arquivo .env.local encontrado');
}

// Verificar variáveis essenciais
const requiredVars = [
  'DATABASE_URL',
  'BETTER_AUTH_SECRET',
  'NEXT_PUBLIC_SITE_URL'
];

console.log('\n🔍 Verificando variáveis de ambiente essenciais...');

// Carregar variáveis do .env.local se existir
if (envExists) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = envContent.split('\n')
    .filter(line => line.trim() && !line.startsWith('#'))
    .map(line => line.split('=')[0]);

  requiredVars.forEach(varName => {
    if (envVars.includes(varName)) {
      console.log(`✅ ${varName} configurada`);
    } else {
      console.log(`⚠️  ${varName} não encontrada`);
    }
  });
}

console.log('\n🎯 Configuração de ambiente verificada!');
console.log('💡 Execute "pnpm dev" para iniciar o servidor de desenvolvimento');
