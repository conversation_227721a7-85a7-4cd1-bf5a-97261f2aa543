import type { Messages } from "@repo/i18n";
import type { JSX as Jsx } from "react/jsx-runtime";

// Configuração para tipos MDX
declare global {
	namespace JSX {
		type ElementClass = Jsx.ElementClass;
		type Element = Jsx.Element;
		type IntrinsicElements = Jsx.IntrinsicElements;
	}
}

// Configuração para módulos MDX
declare module "*.mdx" {
	let MDXComponent: (props: any) => JSX.Element;
	export default MDXComponent;
}

declare module "*.md" {
	let MDXComponent: (props: any) => JSX.Element;
	export default MDXComponent;
}

declare global {
	interface IntlMessages extends Messages {}
}
