import { auth } from "@repo/auth";
import type { Session } from "@repo/auth";
import { cookies, headers } from "next/headers";

// For server components use
export const getSession = async (
	headersList: Headers,
	cookieStore: ReturnType<typeof cookies>
): Promise<Session | null> => {
	try {
		// Use better-auth's built-in API for server components
		const session = await auth.api.getSession({
			headers: headersList,
		});

		return session;
	} catch (error) {
		console.error("Error fetching session:", error);
		return null;
	}
};
