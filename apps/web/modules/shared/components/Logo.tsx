import { cn } from "@ui/lib";

export function Logo({
	withLabel = true,
	className,
	size = 30,
	width,
	height,
	color = "gradient",
}: {
	className?: string;
	withLabel?: boolean;
	size?: number;
	width?: number;
	height?: number;
	color?: "gradient" | "white";
}) {
	const svgWidth = width ?? size;
	const svgHeight = height ?? size;

	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>


			<svg
				width={svgWidth}
				height={svgHeight}
				viewBox="0 0 40 40"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<title>Cakto Members</title>
				<path
					fillRule="evenodd"
					clipRule="evenodd"
					d="M5.61034 0.0924226C3.2541 0.455012 1.19681 2.11767 0.395486 4.30701C-0.0341406 5.4808 0.000677348 4.09671 0.000677348 19.9982C0.000677348 35.8996 -0.0341406 34.5156 0.395486 35.6894C1.06718 37.5244 2.65124 39.0293 4.57377 39.6587C5.65564 40.0128 5.04821 39.9994 20.0236 39.9998C35.0367 40.0002 34.3442 40.0156 35.4521 39.6561C37.6668 38.9374 39.3301 37.1252 39.8923 34.8187C39.9937 34.4023 40 33.5375 40 19.9982C40 6.45883 39.9937 5.59407 39.8923 5.17767C39.329 2.86656 37.6675 1.0592 35.4419 0.336944C34.3502 -0.0173773 34.9546 -0.00436288 19.9316 0.00208153C8.50474 0.00694524 6.06564 0.0223244 5.61034 0.0924226ZM19.6417 6.10707C21.3537 6.39798 22.8579 7.30001 23.8814 8.64926C25.1673 10.3447 25.7015 13 25.7015 17.6968V19.5808L25.8387 19.5429C25.9141 19.5221 26.2365 19.379 26.5551 19.2249C27.0494 18.9857 27.215 18.8636 27.6842 18.3922C28.4055 17.6677 28.584 17.3161 28.7516 16.2896C29.2464 13.2584 29.7147 12.222 30.8378 11.6733C32.2848 10.9663 33.7933 11.5213 34.5935 13.0549C35.1801 14.1792 35.3118 14.9628 35.2132 16.7437C35.0895 18.9794 34.9394 19.771 34.4639 20.6973C34.1332 21.3414 33.4169 22.0665 32.7928 22.389C31.8075 22.8982 29.7507 23.2146 26.6771 23.33L25.549 23.3724L25.5349 23.5852C25.4013 25.6057 25.1859 28.2336 25.0318 29.7239C24.9679 30.3417 24.921 30.8518 24.9275 30.8575C24.934 30.8632 25.3349 30.9401 25.8185 31.0283C26.3021 31.1165 26.9469 31.2578 27.2514 31.3425C27.939 31.5334 28.7598 31.9213 28.9282 32.1346C30.1625 33.6992 22.3988 34.9006 15.6407 34.1908C12.4619 33.8569 10.4578 33.1898 10.4578 32.4657C10.4578 32.2669 10.8059 31.9315 11.2089 31.7417C11.8654 31.4326 12.9544 31.1406 14.1468 30.9539L14.4517 30.9061L14.4299 30.5135C14.418 30.2975 14.3906 29.8745 14.369 29.5736L14.3297 29.0264L13.1407 28.9512C11.8377 28.8687 10.6753 28.6919 9.03998 28.3273C7.81573 28.0543 7.44299 27.9263 7.01336 27.6312C6.24758 27.1053 5.62924 25.7782 5.19907 23.7372C5.01663 22.8714 5.00693 22.7575 5.00821 21.4877C5.00949 20.2427 5.02053 20.1097 5.16736 19.5649C5.46162 18.4732 5.89521 17.7936 6.59678 17.3245C7.23202 16.8997 7.54866 16.8064 8.35419 16.8064C8.96638 16.8064 9.05546 16.8213 9.37485 16.9777C10.0078 17.2876 10.4271 17.8155 10.8227 18.8006C11.1035 19.4997 11.2296 20.0553 11.3738 21.2275C11.4448 21.8053 11.5446 22.4274 11.5955 22.6098C11.806 23.3641 12.2539 23.9505 13.0436 24.5053C13.4802 24.812 13.6715 24.879 13.6031 24.7013C13.582 24.6464 13.4941 24.3523 13.4078 24.0477C13.3215 23.7432 13.0875 23.0015 12.8878 22.3996C12.1419 20.152 11.7127 18.1584 11.4658 15.7934C11.1675 12.9361 11.1601 12.3832 11.4066 11.3955C11.8903 9.45737 13.5373 7.50684 15.4018 6.66414C16.6616 6.09472 18.3013 5.87926 19.6417 6.10707Z"
					fill={color === "white" ? "white" : "url(#paint0_linear_9548_3019)"}
				/>
				{color === "gradient" && (
					<defs>
						<linearGradient
							id="paint0_linear_9548_3019"
							x1="32.1031"
							y1="1.85429"
							x2="4.08878"
							y2="52.2027"
							gradientUnits="userSpaceOnUse"
						>
							<stop stopColor="#008264" />
							<stop offset="0.594462" stopColor="#00A067" />
						</linearGradient>
					</defs>
				)}
			</svg>

			{withLabel && (
				<span className="ml-3 hidden text-lg md:block">Cakto Members</span>
			)}
		</span>
	);
}
