'use client'

import * as React from 'react'
import { cn } from '@/modules/ui/lib'
import { Input } from './input'
import { Label } from './label'
import { Calendar } from 'lucide-react'

export interface DatePickerProps {
  value?: Date
  onChange?: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  label?: string
  error?: string
}

const DatePicker = React.forwardRef<HTMLInputElement, DatePickerProps>(
  ({ value, onChange, placeholder, disabled, className, label, error, ...props }, ref) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const dateValue = e.target.value
      if (dateValue) {
        onChange?.(new Date(dateValue))
      } else {
        onChange?.(undefined)
      }
    }

    const formatDateForInput = (date: Date | undefined) => {
      if (!date) return ''
      return date.toISOString().split('T')[0]
    }

    return (
      <div className="space-y-2">
        {label && (
          <Label className={cn(error && 'text-destructive')}>
            {label}
          </Label>
        )}
        <div className="relative">
          <Input
            ref={ref}
            type="date"
            value={formatDateForInput(value)}
            onChange={handleChange}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              'pl-10',
              error && 'border-destructive focus-visible:ring-destructive',
              className
            )}
            {...props}
          />
          <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        </div>
        {error && (
          <p className="text-sm text-destructive">{error}</p>
        )}
      </div>
    )
  }
)

DatePicker.displayName = 'DatePicker'

export { DatePicker }