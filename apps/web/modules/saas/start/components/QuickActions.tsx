"use client";
import { Card } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { useTranslations } from "next-intl";
import { Plus, UserPlus, CreditCard, Palette, BookO<PERSON>, Settings } from "lucide-react";
import Link from "next/link";

interface QuickActionProps {
	title: string;
	description: string;
	icon: React.ElementType;
	href: string;
	variant?: "default" | "secondary";
}

function QuickActionCard({ title, description, icon: Icon, href, variant = "default" }: QuickActionProps) {
	return (
		<Card className="p-6 hover:shadow-md transition-shadow">
			<div className="flex items-start space-x-4">
				<div className={`p-2 rounded-lg ${
					variant === "default" ? "bg-primary/10 text-primary" : "bg-muted text-muted-foreground"
				}`}>
					<Icon className="h-6 w-6" />
				</div>
				<div className="flex-1 space-y-2">
					<h3 className="font-semibold">{title}</h3>
					<p className="text-sm text-muted-foreground">{description}</p>
					<Link href={href}>
						<Button size="sm" variant={variant === "default" ? "primary" : "outline"}>
							Acessar
						</Button>
					</Link>
				</div>
			</div>
		</Card>
	);
}

export function QuickActions() {
	const t = useTranslations();

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-2xl font-semibold">Ações Rápidas</h2>
				<p className="text-muted-foreground">
					Acesse rapidamente as funcionalidades mais importantes
				</p>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				<QuickActionCard
					title="Criar Novo Curso"
					description="Adicione conteúdo novo para seus membros"
					icon={BookOpen}
					href="/app/courses/new"
					variant="default"
				/>
				<QuickActionCard
					title="Convidar Membros"
					description="Envie convites para novos usuários"
					icon={UserPlus}
					href="/app/members/invite"
					variant="default"
				/>
				<QuickActionCard
					title="Configurar Pagamentos"
					description="Gerencie planos e métodos de pagamento"
					icon={CreditCard}
					href="/app/settings/personal"
					variant="secondary"
				/>
				<QuickActionCard
					title="Personalizar Área"
					description="Customize a aparência do seu workspace"
					icon={Palette}
					href="/app/settings/branding"
					variant="secondary"
				/>
				<QuickActionCard
					title="Criar Workspace"
					description="Configure uma nova área de membros"
					icon={Plus}
					href="/app/new-organization"
					variant="secondary"
				/>
				<QuickActionCard
					title="Configurações"
					description="Acesse todas as configurações"
					icon={Settings}
					href="/app/settings"
					variant="secondary"
				/>
			</div>
		</div>
	);
}
