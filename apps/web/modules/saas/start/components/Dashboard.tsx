"use client";
import { Card } from "@ui/components/card";
import { useTranslations } from "next-intl";
import { Users, DollarSign, TrendingUp, Activity } from "lucide-react";

interface MetricCardProps {
	title: string;
	value: string | number;
	icon: React.ElementType;
	change?: string;
	changeType?: "positive" | "negative" | "neutral";
}

function MetricCard({ title, value, icon: Icon, change, changeType = "neutral" }: MetricCardProps) {
	return (
		<Card className="p-6">
			<div className="flex items-center justify-between">
				<div>
					<p className="text-sm font-medium text-muted-foreground">{title}</p>
					<p className="text-2xl font-bold">{value}</p>
					{change && (
						<p className={`text-xs ${
							changeType === "positive" ? "text-green-600" :
							changeType === "negative" ? "text-red-600" :
							"text-muted-foreground"
						}`}>
							{change}
						</p>
					)}
				</div>
				<Icon className="h-8 w-8 text-muted-foreground" />
			</div>
		</Card>
	);
}

export function Dashboard() {
	const t = useTranslations();

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-2xl font-bold tracking-tight">Dashboard</h2>
				<p className="text-muted-foreground">
					Visão geral das suas métricas principais
				</p>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<MetricCard
					title="Total de Membros"
					value="1,234"
					icon={Users}
					change="+12% vs mês anterior"
					changeType="positive"
				/>
				<MetricCard
					title="Receita Mensal"
					value="R$ 45.231"
					icon={DollarSign}
					change="+8% vs mês anterior"
					changeType="positive"
				/>
				<MetricCard
					title="Taxa de Conversão"
					value="3.2%"
					icon={TrendingUp}
					change="-0.5% vs mês anterior"
					changeType="negative"
				/>
				<MetricCard
					title="Membros Ativos"
					value="892"
					icon={Activity}
					change="+5% vs mês anterior"
					changeType="positive"
				/>
			</div>
		</div>
	);
}