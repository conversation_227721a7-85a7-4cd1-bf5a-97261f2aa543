"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  GraduationCapIcon,
  UsersIcon,
  Building2Icon,
  SettingsIcon,
  ShieldIcon,
  TrendingUpIcon,
  CheckCircleIcon
} from "lucide-react";
import Link from "next/link";

export function AdminQuickActions() {
  return (
    <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <ShieldIcon className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-foreground text-lg">Área Administrativa</CardTitle>
              <p className="text-sm text-muted-foreground mt-1"><PERSON><PERSON><PERSON><PERSON> sua plataforma com facilidade</p>
            </div>
          </div>
          <Badge className="bg-primary/10 text-primary border-primary/20">
            ADMIN
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/app/admin/courses" className="group">
            <Card className="h-full transition-all duration-200 hover:shadow-md hover:-translate-y-1 border-border/50 group-hover:border-primary/30">
              <CardContent className="p-4 flex flex-col items-center text-center space-y-3">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                  <GraduationCapIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-sm text-foreground">Gestão de Cursos</h3>
                  <p className="text-xs text-muted-foreground">Criar e gerenciar cursos</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/app/admin/users" className="group">
            <Card className="h-full transition-all duration-200 hover:shadow-md hover:-translate-y-1 border-border/50 group-hover:border-primary/30">
              <CardContent className="p-4 flex flex-col items-center text-center space-y-3">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                  <UsersIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-sm text-foreground">Membros</h3>
                  <p className="text-xs text-muted-foreground">Gerenciar membros</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/app/admin/organizations" className="group">
            <Card className="h-full transition-all duration-200 hover:shadow-md hover:-translate-y-1 border-border/50 group-hover:border-primary/30">
              <CardContent className="p-4 flex flex-col items-center text-center space-y-3">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
                  <Building2Icon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-sm text-foreground">Workspaces</h3>
                  <p className="text-xs text-muted-foreground">Gerenciar organizações</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/app" className="group">
            <Card className="h-full transition-all duration-200 hover:shadow-md hover:-translate-y-1 border-border/50 group-hover:border-primary/30">
              <CardContent className="p-4 flex flex-col items-center text-center space-y-3">
                <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/30 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50 transition-colors">
                  <SettingsIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-sm text-foreground">Configurações</h3>
                  <p className="text-xs text-muted-foreground">Painel administrativo</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4 flex items-start gap-3">
          <div className="p-1 rounded-full bg-primary/10">
            <CheckCircleIcon className="h-4 w-4 text-primary" />
          </div>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-foreground">Dica de Produtividade</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Use "Gestão de Cursos" para criar cursos em qualquer organização rapidamente.
              A interface unificada acelera o processo de criação de conteúdo.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
