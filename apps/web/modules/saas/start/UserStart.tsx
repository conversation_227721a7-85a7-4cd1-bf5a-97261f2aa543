"use client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { EnhancedOrganizationsGrid } from "@saas/start/components/EnhancedOrganizationsGrid";
import { Dashboard } from "@saas/start/components/Dashboard";
import { QuickActions } from "@saas/start/components/QuickActions";
import { AdminQuickActions } from "@saas/start/components/AdminQuickActions";
import { useTranslations } from "next-intl";

export default function UserStart() {
	const t = useTranslations();
	const { user } = useSession();

	const isAdmin = user?.role === "admin";

	return (
		<div className="space-y-8 min-h-screen">
			{/* <Dashboard /> */}
			{config.organizations.enable && <EnhancedOrganizationsGrid />}
		</div>
	);
}
