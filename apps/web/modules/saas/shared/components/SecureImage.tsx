import { useState, useEffect } from "react";
import { useImageAccess } from "../hooks/useImageAccess";

interface SecureImageProps {
	src: string;
	alt: string;
	organizationId: string;
	className?: string;
	fallbackSrc?: string;
	onError?: () => void;
	onLoad?: () => void;
}

export function SecureImage({
	src,
	alt,
	organizationId,
	className,
	fallbackSrc,
	onError,
	onLoad,
}: SecureImageProps) {
	const [imageSrc, setImageSrc] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [hasError, setHasError] = useState(false);

	const { getAccessUrl, isLoading: isGettingUrl } = useImageAccess({ organizationId });

	useEffect(() => {
		const loadImage = async () => {
			if (!src) {
				setIsLoading(false);
				return;
			}

			// If it's already a full URL (like a signed URL), use it directly
			if (src.startsWith('http')) {
				setImageSrc(src);
				setIsLoading(false);
				return;
			}

			// If it's a file path, get a signed URL
			try {
				const accessUrl = await getAccessUrl(src);
				if (accessUrl) {
					setImageSrc(accessUrl);
				} else {
					setHasError(true);
				}
			} catch (error) {
				console.error("Failed to get access URL:", error);
				setHasError(true);
			} finally {
				setIsLoading(false);
			}
		};

		loadImage();
	}, [src, organizationId, getAccessUrl]);

	const handleImageError = () => {
		setHasError(true);
		onError?.();
	};

	const handleImageLoad = () => {
		setIsLoading(false);
		onLoad?.();
	};

	if (isLoading || isGettingUrl) {
		return (
			<div className={`animate-pulse bg-gray-200 ${className}`}>
				<div className="w-full h-full bg-gray-300 rounded" />
			</div>
		);
	}

	if (hasError || !imageSrc) {
		if (fallbackSrc) {
			return (
				<img
					src={fallbackSrc}
					alt={alt}
					className={className}
					onError={onError}
					onLoad={onLoad}
				/>
			);
		}

		return (
			<div className={`bg-gray-100 flex items-center justify-center ${className}`}>
				<span className="text-gray-400 text-sm">Image not available</span>
			</div>
		);
	}

	return (
		<img
			src={imageSrc}
			alt={alt}
			className={className}
			onError={handleImageError}
			onLoad={handleImageLoad}
		/>
	);
}
