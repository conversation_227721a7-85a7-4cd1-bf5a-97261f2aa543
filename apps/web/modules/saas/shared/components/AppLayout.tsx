"use client";

import { useOrganization } from "@saas/organizations/components/OrganizationProvider";
import { Logo } from "@shared/components/Logo";
import { UserNav } from "@shared/components/UserNav";
import Link from "next/link";
import { ReactNode } from "react";

interface AppLayoutProps {
  children: ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const { activeOrganization } = useOrganization();

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="flex items-center space-x-2">
              <Logo />
            </Link>

            {activeOrganization && (
              <div className="hidden md:flex items-center gap-2">
                <span className="text-muted-foreground">/</span>
                <Link
                  href={`/app/${activeOrganization.slug}`}
                  className="text-sm font-medium hover:underline"
                >
                  {activeOrganization.name}
                </Link>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <UserNav />
          </div>
        </div>
      </header>

      <main className="flex-1 container py-6">
        {children}
      </main>

      <footer className="border-t py-4">
        <div className="container flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} Cakto Members. Todos os direitos reservados.
          </p>
        </div>
      </footer>
    </div>
  );
}
