"use client";

export function PageHeader({
	title,
	subtitle,
}: {
	title: string;
	subtitle?: string;
}) {
	return (
		<div className="mb-8">

{/*
<div>
					<h2 className="text-2xl font-semibold">{title}</h2>
					<p className="text-muted-foreground">{subtitle}</p>
				</div> */}


			<h2 className="text-2xl font-semibold">{title}</h2>
			<p className="mt-1 opacity-60">{subtitle}</p>
		</div>
	);
}
