"use client";
import { Logo } from "@shared/components/Logo";
import { OrganzationSelect } from "@saas/organizations/components/OrganizationSelect";
import { UserMenu } from "@saas/shared/components/UserMenu";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import {
	Menu,
	X,
	HomeIcon,
	MessageSquare,
	HelpCircle,
	PlayIcon,
	PlayCircleIcon,
} from "lucide-react";

export function UnifiedNavbar() {
	const pathname = usePathname();
	const [open, setOpen] = useState(false);

 
	const getOrganizationSlug = () => {
		const match = pathname.match(/\/app\/([^\/]+)/);
		return match ? match[1] : null;
	};

	const organizationSlug = getOrganizationSlug();
	const vitrineUrl = organizationSlug ? `/app/${organizationSlug}` : "/app";

	const MENU_ITEMS = [
		{
			id: "vitrine",
			label: "Vitrine",
			url: vitrineUrl,
			icon: PlayIcon,
			isExternal: false,
		},
		{
			id: "comunidade",
			label: "Comunidade",
			url: "https://chat.whatsapp.com",
			icon: MessageSquare,
			isExternal: true,
		},
		{ id: "suporte", label: "Suporte", url: "/suporte", icon: HelpCircle },
	];

	return (
		<header className="sticky top-0 z-50 w-full    backdrop-blur-md">
			<div className="  mx-auto flex items-center h-16 px-4 lg:px-8">
			 
				<div className="hidden md:flex items-center flex-shrink-0 gap-4">
					<Link
						href="/"
						className="hover:opacity-80 transition-opacity"
					>
						<Logo className="h-8 w-auto text-white" />
					</Link>
					
				</div>
 
				<div className="flex md:hidden items-center flex-shrink-0">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => setOpen(true)}
						aria-label="Abrir menu"
						className="hover:bg-white/10 transition-colors"
					>
						<Menu className="w-6 h-6 text-white" />
					</Button>
				</div>

		 
				<div className="flex md:hidden flex-1 justify-center">
					<Link
						href="/"
						className="hover:opacity-80 transition-opacity"
					>
						<Logo className="h-8 w-auto text-white" />
					</Link>
				</div>

 
				<nav className="hidden md:flex flex-1 items-center justify-center gap-1">
					{MENU_ITEMS.map((item) => {
						const isActive = pathname === item.url;
						const Icon = item.icon;
						return (
							<Link
								key={item.id}
								href={item.url}
								className={cn(
									"flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-white/10 hover:text-white focus-visible:ring-2 focus-visible:ring-white/20",
									isActive
										? "text-white bg-white/15 shadow-lg"
										: "text-white/80 hover:text-white",
								)}
								target={item.isExternal ? "_blank" : undefined}
								rel={
									item.isExternal
										? "noopener noreferrer"
										: undefined
								}
							>
								<Icon className="w-4 h-4" />
								<span>{item.label}</span>
							</Link>
						);
					})}
				</nav>

			 
				<div className="flex items-center gap-2 flex-shrink-0">
					<div>
						<OrganzationSelect />
					</div>

					<UserMenu />
				</div>
			</div>

	 
			{open && (
				<div className="fixed inset-0 z-[9999] bg-black/90 backdrop-blur-sm flex md:hidden">
					<nav className="bg-gray-900 w-80 max-w-[85vw] h-screen flex flex-col shadow-2xl border-r border-gray-700 relative z-[10000]">
						{/* Header do menu */}
						<div className="flex items-center justify-between p-6 border-b border-gray-700">
							<div className="flex items-center gap-3">
								<Logo className="h-6 w-auto text-white" />
								<span className="font-semibold text-white text-lg">
									Cakto
								</span>
							</div>
							<Button
								variant="ghost"
								size="icon"
								onClick={() => setOpen(false)}
								aria-label="Fechar menu"
								className="hover:bg-gray-700 transition-colors rounded-full"
							>
								<X className="w-5 h-5 text-white" />
							</Button>
						</div>

						{/* Menu items */}
						<div className="flex-1 p-6 space-y-2">
							{MENU_ITEMS.map((item) => {
								const isActive = pathname === item.url;
								const Icon = item.icon;
								return (
									<Link
										key={item.id}
										href={item.url}
										className={cn(
											"flex items-center gap-4 px-4 py-3 rounded-xl text-base font-medium transition-all duration-200 hover:bg-gray-700 hover:text-white focus-visible:ring-2 focus-visible:ring-gray-500 group",
											isActive
												? "text-white bg-gray-700 shadow-lg border border-gray-600"
												: "text-gray-300 hover:text-white hover:translate-x-1",
										)}
										target={
											item.isExternal
												? "_blank"
												: undefined
										}
										rel={
											item.isExternal
												? "noopener noreferrer"
												: undefined
										}
										onClick={() => setOpen(false)}
									>
										<div
											className={cn(
												"p-2 rounded-lg transition-colors",
												isActive
													? "bg-gray-600"
													: "bg-gray-800 group-hover:bg-gray-600",
											)}
										>
											<Icon className="w-5 h-5" />
										</div>
										<span className="flex-1">
											{item.label}
										</span>
										{item.isExternal && (
											<div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
										)}
									</Link>
								);
							})}
						</div>

						<div className="p-6 border-t border-gray-700">
							<div className="text-xs text-gray-400 text-center">
								© 2025 Cakto Members
							</div>
						</div>
					</nav>
					<div className="flex-1" onClick={() => setOpen(false)} />
				</div>
			)}
		</header>
	);
}
