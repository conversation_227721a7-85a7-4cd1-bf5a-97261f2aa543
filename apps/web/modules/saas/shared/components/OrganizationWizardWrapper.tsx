"use client";

import { Logo } from "@shared/components/Logo";
import { cn } from "@ui/lib";
import Link from "next/link";
import { type PropsWithChildren } from "react";
import { motion } from "framer-motion";

export function OrganizationWizardWrapper({
	children,
	contentClass,
	title,
	subtitle,
}: PropsWithChildren<{
	contentClass?: string;
	title: string;
	subtitle?: string;
}>) {
	return (
		<div className="min-h-screen w-full bg-gradient-to-br from-muted/20 via-background to-muted/40">

			<header className="border-b bg-background/90 backdrop-blur-sm py-4 sticky top-0 z-50 shadow-sm">
				<div className="container mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex items-center justify-between">
						<Link href="/" className="block">
							<Logo />
						</Link>
					</div>
				</div>
			</header>


			<main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					className="w-full"
				>
					{children}
				</motion.div>
			</main>


		</div>
	);
}
