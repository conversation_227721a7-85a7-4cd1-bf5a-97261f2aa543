import { useState } from "react";
import { apiClient } from "@shared/lib/api-client";

interface UseImageAccessOptions {
	organizationId: string;
}

export function useImageAccess({ organizationId }: UseImageAccessOptions) {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const getAccessUrl = async (filePath: string): Promise<string | null> => {
		if (!filePath || !organizationId) {
			setError("File path or organization not provided");
			return null;
		}

		setIsLoading(true);
		setError(null);

		try {
			const response = await apiClient.courses["access-url"].$post({
				json: {
					filePath,
					organizationId,
				},
			});

			if (!response.ok) {
				const errorData = await response.json() as { error?: string };
				throw new Error(errorData.error || "Failed to get access URL");
			}

			const { accessUrl } = await response.json();
			return accessUrl;
		} catch (err) {
			console.error("Access URL error:", err);
			setError(err instanceof Error ? err.message : "Failed to get access URL");
			return null;
		} finally {
			setIsLoading(false);
		}
	};

	const resetError = () => {
		setError(null);
	};

	return {
		getAccessUrl,
		isLoading,
		error,
		resetError,
	};
}
