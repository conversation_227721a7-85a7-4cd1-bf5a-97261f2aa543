import { useState } from "react";

interface UploadResponse {
	uploadUrl: string;
	filePath: string;
	publicUrl: string;
}

interface UseMemberAreaImageUploadOptions {
	organizationId: string;
	fileType: "logo" | "banner" | "favicon";
}

export function useMemberAreaImageUpload({ organizationId, fileType }: UseMemberAreaImageUploadOptions) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [error, setError] = useState<string | null>(null);

	const uploadImage = async (file: File): Promise<string | null> => {
		if (!file || !organizationId) {
			setError("Arquivo ou organização não fornecidos");
			return null;
		}

		setIsUploading(true);
		setError(null);
		setUploadProgress(0);

		try {
			// Step 1: Get signed upload URL from our API
			const uploadUrlResponse = await fetch("/api/member-area-settings/upload-url", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					fileName: file.name,
					fileType,
					organizationId,
				}),
			});

			if (!uploadUrlResponse.ok) {
				const errorData = await uploadUrlResponse.json() as { error?: string };
				throw new Error(errorData.error || "Falha ao obter URL de upload");
			}

			const { uploadUrl, publicUrl }: UploadResponse = await uploadUrlResponse.json();

			// Step 2: Upload file directly to S3 using the signed URL
			const uploadResponse = await fetch(uploadUrl, {
				method: "PUT",
				body: file,
				headers: {
					"Content-Type": file.type,
				},
			});

			if (!uploadResponse.ok) {
				throw new Error("Falha ao fazer upload do arquivo para o armazenamento");
			}

			setUploadProgress(100);
			return publicUrl;
		} catch (err) {
			console.error("Erro no upload:", err);
			setError(err instanceof Error ? err.message : "Upload falhou");
			return null;
		} finally {
			setIsUploading(false);
		}
	};

	const resetUpload = () => {
		setIsUploading(false);
		setUploadProgress(0);
		setError(null);
	};

	return {
		uploadImage,
		isUploading,
		uploadProgress,
		error,
		resetUpload,
	};
}
