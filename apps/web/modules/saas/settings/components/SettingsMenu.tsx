"use client";

import { cn } from "@ui/lib";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type { ReactNode } from "react";
import { ChevronRightIcon } from "lucide-react";

export function SettingsMenu({
	menuItems,
}: {
	menuItems: {
		title: string;
		avatar: ReactNode;
		items: {
			title: string;
			href: string;
			icon?: ReactNode;
		}[];
	}[];
}) {
	const pathname = usePathname();

	const isActiveMenuItem = (href: string) => pathname === href;

	return (
		<div className="space-y-4">
			{/* Header interno com informações da conta */}
			<div className="border rounded-lg p-6 bg-card">
				<div className="flex flex-col items-center text-center space-y-4">
					{menuItems[0]?.avatar}
					<div className="space-y-2">
						<h1 className="text-xl font-bold text-foreground">
							{menuItems[0]?.title || "Minha conta"}
						</h1>
						<p className="text-sm text-muted-foreground leading-relaxed max-w-xs">
							<PERSON><PERSON><PERSON><PERSON> as informações de conta, dados pessoais e assinaturas
						</p>
					</div>
				</div>
			</div>

			{/* Menu de navegação */}
			<div className="border rounded-lg p-4 bg-card">
				{menuItems.map((item, i) => (
					<div key={i} className="space-y-2">
						{/* Lista de itens do menu */}
						<nav className="space-y-1">
							{item.items.map((subitem, k) => {
								const isActive = isActiveMenuItem(subitem.href);

								return (
									<Link
										key={k}
										href={subitem.href}
										className={cn(
											"group flex items-center justify-between px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 border",
											isActive
												? "bg-primary text-primary-foreground border-primary shadow-sm"
												: "text-muted-foreground hover:text-foreground hover:bg-muted/50 border-transparent hover:border-border"
										)}
									>
										<div className="flex items-center gap-3 min-w-0">
											<div className={cn(
												"flex-shrink-0 transition-colors",
												isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
											)}>
												{subitem.icon}
											</div>
											<span className="truncate text-sm">{subitem.title}</span>
										</div>

										<ChevronRightIcon
											className={cn(
												"h-4 w-4 transition-all duration-200 flex-shrink-0",
												isActive
													? "text-primary-foreground opacity-100"
													: "text-muted-foreground opacity-0 group-hover:opacity-100 group-hover:translate-x-0.5"
											)}
										/>
									</Link>
								);
							})}
						</nav>
					</div>
				))}
			</div>
		</div>
	);
}
