"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "@saas/auth/hooks/use-session";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { UserIcon } from "lucide-react";

const personalDataSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	cpf: z.string().min(11, "CPF deve ter 11 dígitos"),
	birthDate: z.string().min(1, "Data de nascimento é obrigatória"),
	phone: z.string().min(1, "Telefone é obrigatório"),
});

type PersonalDataFormValues = z.infer<typeof personalDataSchema>;

export function PersonalDataForm() {
	const { user } = useSession();

	const form = useForm<PersonalDataFormValues>({
		resolver: zodResolver(personalDataSchema),
		defaultValues: {
			name: user?.name || "",
			cpf: "",
			birthDate: "",
			phone: "",
		},
	});

	const onSubmit = form.handleSubmit(async (values) => {
		try {
			// Implementação da atualização dos dados pessoais será adicionada posteriormente
			toast.success("Dados pessoais atualizados com sucesso!");
		} catch (error) {
			toast.error("Erro ao atualizar dados pessoais");
		}
	});

	return (
		<SettingsItem
			title="Dados pessoais"
			description="Gerencie suas informações pessoais como nome, CPF, data de nascimento e telefone."
		>
			<Form {...form}>
				<form onSubmit={onSubmit} className="space-y-4">
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Nome completo</FormLabel>
								<FormControl>
									<Input {...field} placeholder="Seu nome completo" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<FormField
							control={form.control}
							name="cpf"
							render={({ field }) => (
								<FormItem>
									<FormLabel>CPF</FormLabel>
									<div className="flex gap-2">
										<FormControl>
											<Input {...field} placeholder="000.000.000-00" />
										</FormControl>
										<Button type="button" variant="outline">
											Alterar
										</Button>
									</div>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="birthDate"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Data de nascimento</FormLabel>
									<FormControl>
										<Input {...field} placeholder="dd/mm/aaaa" />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					<FormField
						control={form.control}
						name="phone"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Telefone</FormLabel>
								<FormControl>
									<Input {...field} placeholder="(00) 00000-0000" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<div className="flex justify-end pt-4">
						<Button
							type="submit"
							loading={form.formState.isSubmitting}
							disabled={!form.formState.isValid}
						>
							Salvar
						</Button>
					</div>
				</form>
			</Form>
		</SettingsItem>
	);
}
