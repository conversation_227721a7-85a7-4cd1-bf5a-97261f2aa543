"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useOrganizationListQuery } from "@saas/organizations/lib/api";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { Building2Icon, PlusIcon, ArrowRightIcon, SettingsIcon, CheckIcon } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function OrganizationSelector() {
    const { data: organizations, isLoading } = useOrganizationListQuery();
    const { setActiveOrganization, activeOrganization } = useActiveOrganization();
    const router = useRouter();
    const [selectedOrg, setSelectedOrg] = useState<string | null>(null);
    const [isConfiguring, setIsConfiguring] = useState(false);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (!organizations || organizations.length === 0) {
        return (
            <Card className="max-w-md mx-auto">
                <CardHeader className="text-center">
                    <CardTitle className="flex items-center justify-center gap-2">
                        <Building2Icon className="h-6 w-6" />
                        Nenhuma Organização Encontrada
                    </CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                    <p className="text-muted-foreground">
                        Você precisa criar ou participar de uma organização para configurar uma área de membros.
                    </p>
                    <div className="flex flex-col gap-2">
                        <Button asChild>
                            <Link href="/app/new-organization">
                                <PlusIcon className="h-4 w-4 mr-2" />
                                Criar Nova Organização
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/app">
                                Voltar ao Dashboard
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    const handleOrganizationSelect = (organizationId: string) => {
        setSelectedOrg(organizationId);
    };

    const handleConfigureOrganization = async () => {
        if (!selectedOrg) return;

        const organization = organizations.find(org => org.id === selectedOrg);
        if (!organization) return;

        setIsConfiguring(true);

        try {
            await setActiveOrganization(organization.slug);
                                    router.push(`/app/${organization.slug}/settings/general`);
        } catch (error) {
            setIsConfiguring(false);
        }
    };

    return (
        <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center gap-2">
                    <SettingsIcon className="h-6 w-6" />
                    Configurar Área de Membros
                </CardTitle>
                <p className="text-muted-foreground">
                    Escolha uma organização para configurar sua área de membros personalizada
                </p>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid gap-3">
                    {organizations.map((organization) => (
                        <div
                            key={organization.id}
                            className={`flex items-center justify-between p-4 border rounded-lg transition-colors cursor-pointer group ${
                                selectedOrg === organization.id
                                    ? "bg-primary/10 border-primary"
                                    : "hover:bg-muted/50"
                            }`}
                            onClick={() => handleOrganizationSelect(organization.id)}
                        >
                            <div className="flex items-center gap-3">
                                <OrganizationLogo
                                    name={organization.name}
                                    logoUrl={organization.logo}
                                    className="h-10 w-10"
                                />
                                <div>
                                    <h3 className="font-medium">{organization.name}</h3>
                                    <p className="text-sm text-muted-foreground">
                                        {organization.slug}.cakto.com.br
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center gap-2">
                                {selectedOrg === organization.id ? (
                                    <CheckIcon className="h-5 w-5 text-primary" />
                                ) : (
                                    <ArrowRightIcon className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                                )}
                            </div>
                        </div>
                    ))}
                </div>

                <div className="pt-4 border-t flex flex-col gap-3">
                    <Button
                        onClick={handleConfigureOrganization}
                        disabled={!selectedOrg || isConfiguring}
                        loading={isConfiguring}
                        className="w-full"
                    >
                        <SettingsIcon className="h-4 w-4 mr-2" />
                        Configurar Organização Selecionada
                    </Button>

                    <Button asChild variant="outline" className="w-full">
                        <Link href="/app/new-organization">
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Criar Nova Organização
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
