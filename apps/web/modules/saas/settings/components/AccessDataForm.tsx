"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { MailIcon, LockIcon } from "lucide-react";

export function AccessDataForm() {
	const { user } = useSession();

	return (
		<>
			{/* Email */}
			<SettingsItem
				title="Seu email"
				description="Para alterar seu email, digite o novo email e clique em salvar. Você terá que confirmar o novo email antes que ele se torne ativo."
			>
				<div className="flex items-center gap-3">
					<div className="flex-1 relative">
						<MailIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<Input
							value={user?.email || "<EMAIL>"}
							readOnly
							className="pl-10 bg-muted/50"
						/>
					</div>
					<Button variant="outline">
						Alterar
					</Button>
				</div>
			</SettingsItem>

			{/* Senha */}
			<SettingsItem
				title="Sua senha"
				description="Para alterar sua senha, clique no botão alterar e siga as instruções."
			>
				<div className="flex items-center gap-3">
					<div className="flex-1 relative">
						<LockIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<Input
							value="••••••••"
							readOnly
							type="password"
							className="pl-10 bg-muted/50"
						/>
					</div>
					<Button variant="outline">
						Alterar
					</Button>
				</div>
			</SettingsItem>

			{/* Conta vinculada */}
			<SettingsItem
				title="Conta vinculada"
				description="Gerencie suas contas vinculadas e métodos de autenticação."
			>
				<div className="flex items-center gap-3">
					<div className="flex-1 relative">
						<div className="flex items-center gap-2 pl-3 pr-4 py-2 bg-muted/50 rounded-md border border-input">
							<div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
								<span className="text-white text-xs font-bold">G</span>
							</div>
							<span className="text-sm text-foreground">
								{user?.email || "<EMAIL>"}
							</span>
						</div>
					</div>
					<Button variant="outline" className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300">
						Desvincular
					</Button>
				</div>
			</SettingsItem>
		</>
	);
}
