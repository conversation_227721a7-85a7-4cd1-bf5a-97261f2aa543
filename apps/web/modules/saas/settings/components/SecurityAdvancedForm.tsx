"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Switch } from "@ui/components/switch";
import { ShieldIcon, SmartphoneIcon, KeyIcon, AlertTriangleIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export function SecurityAdvancedForm() {
	const [securitySettings, setSecuritySettings] = useState({
		twoFactorAuth: false,
		loginNotifications: true,
		sessionTimeout: 30,
		passwordExpiry: 90,
		deviceManagement: true,
	});

	const handleToggle = (key: keyof typeof securitySettings) => {
		setSecuritySettings(prev => ({
			...prev,
			[key]: !prev[key]
		}));
	};

	const handleSave = () => {
		toast.success("Configurações de segurança atualizadas!");
	};

	return (
		<div className="space-y-4">
			<div>
				<h2 className="text-xl font-semibold text-foreground">Segurança</h2>
				<p className="text-muted-foreground mt-1 text-sm">
					Configure as opções avançadas de segurança da sua conta
				</p>
			</div>

			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2">
						<ShieldIcon className="h-5 w-5" />
						Autenticação
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<SmartphoneIcon className="h-5 w-5 text-muted-foreground" />
							<div>
								<p className="font-medium">Autenticação de dois fatores</p>
								<p className="text-sm text-muted-foreground">
									Adicione uma camada extra de segurança à sua conta
								</p>
							</div>
						</div>
						<Switch
							checked={securitySettings.twoFactorAuth}
							onCheckedChange={() => handleToggle('twoFactorAuth')}
						/>
					</div>

					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<AlertTriangleIcon className="h-5 w-5 text-muted-foreground" />
							<div>
								<p className="font-medium">Notificações de login</p>
								<p className="text-sm text-muted-foreground">
									Receba alertas quando sua conta for acessada
								</p>
							</div>
						</div>
						<Switch
							checked={securitySettings.loginNotifications}
							onCheckedChange={() => handleToggle('loginNotifications')}
						/>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2">
						<KeyIcon className="h-5 w-5" />
						Sessões e dispositivos
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3">
					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Gerenciamento de dispositivos</p>
							<p className="text-sm text-muted-foreground">
								Visualize e gerencie dispositivos conectados
							</p>
						</div>
						<Button variant="outline" size="sm">
							Gerenciar
						</Button>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Tempo limite de sessão</p>
							<p className="text-sm text-muted-foreground">
								{securitySettings.sessionTimeout} minutos de inatividade
							</p>
						</div>
						<Button variant="outline" size="sm">
							Configurar
						</Button>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Expiração de senha</p>
							<p className="text-sm text-muted-foreground">
								A cada {securitySettings.passwordExpiry} dias
							</p>
						</div>
						<Button variant="outline" size="sm">
							Configurar
						</Button>
					</div>
				</CardContent>
			</Card>

			<div className="flex justify-end">
				<Button onClick={handleSave}>
					Salvar configurações
				</Button>
			</div>
		</div>
	);
}
