"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Switch } from "@ui/components/switch";
import { BellIcon, MailIcon, SmartphoneIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export function NotificationPreferencesForm() {
	const [preferences, setPreferences] = useState({
		emailNotifications: true,
		pushNotifications: false,
		smsNotifications: false,
		marketingEmails: true,
		courseUpdates: true,
		securityAlerts: true,
		newsletter: false,
	});

	const handleToggle = (key: keyof typeof preferences) => {
		setPreferences(prev => ({
			...prev,
			[key]: !prev[key]
		}));
	};

	const handleSave = () => {
		toast.success("Preferências de notificação atualizadas!");
	};

	return (
		<div className="space-y-4">
			<div>
				<h2 className="text-xl font-semibold text-foreground">Preferências de notificação</h2>
				<p className="text-muted-foreground mt-1 text-sm">
					Configure como você gostaria de receber notificações
				</p>
			</div>

			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2">
						<BellIcon className="h-5 w-5" />
						Canais de notificação
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Canais */}
					<div className="space-y-3">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-3">
								<MailIcon className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="font-medium">Notificações por e-mail</p>
									<p className="text-sm text-muted-foreground">
										Receba notificações importantes por e-mail
									</p>
								</div>
							</div>
							<Switch
								checked={preferences.emailNotifications}
								onCheckedChange={() => handleToggle('emailNotifications')}
							/>
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center gap-3">
								<BellIcon className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="font-medium">Notificações push</p>
									<p className="text-sm text-muted-foreground">
										Receba notificações no navegador
									</p>
								</div>
							</div>
							<Switch
								checked={preferences.pushNotifications}
								onCheckedChange={() => handleToggle('pushNotifications')}
							/>
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center gap-3">
								<SmartphoneIcon className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="font-medium">Notificações por SMS</p>
									<p className="text-sm text-muted-foreground">
										Receba notificações por mensagem de texto
									</p>
								</div>
							</div>
							<Switch
								checked={preferences.smsNotifications}
								onCheckedChange={() => handleToggle('smsNotifications')}
							/>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader className="pb-4">
					<CardTitle>Tipos de notificação</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3">
					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Atualizações de cursos</p>
							<p className="text-sm text-muted-foreground">
								Novos conteúdos, lições e materiais
							</p>
						</div>
						<Switch
							checked={preferences.courseUpdates}
							onCheckedChange={() => handleToggle('courseUpdates')}
						/>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Alertas de segurança</p>
							<p className="text-sm text-muted-foreground">
								Login em novos dispositivos e alterações de conta
							</p>
						</div>
						<Switch
							checked={preferences.securityAlerts}
							onCheckedChange={() => handleToggle('securityAlerts')}
						/>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">E-mails de marketing</p>
							<p className="text-sm text-muted-foreground">
								Ofertas especiais e novidades da plataforma
							</p>
						</div>
						<Switch
							checked={preferences.marketingEmails}
							onCheckedChange={() => handleToggle('marketingEmails')}
						/>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<p className="font-medium">Newsletter</p>
							<p className="text-sm text-muted-foreground">
								Receba dicas e insights sobre educação
							</p>
						</div>
						<Switch
							checked={preferences.newsletter}
							onCheckedChange={() => handleToggle('newsletter')}
						/>
					</div>
				</CardContent>
			</Card>

			<div className="flex justify-end">
				<Button onClick={handleSave}>
					Salvar preferências
				</Button>
			</div>
		</div>
	);
}
