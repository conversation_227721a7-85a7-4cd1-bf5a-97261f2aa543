"use client";

import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { FormField, FormItem, FormLabel, FormControl } from "@ui/components/form";
import { Trash2, Plus } from "lucide-react";
import { useFieldArray } from "react-hook-form";

export function FooterEditor({ form }: { form: any }) {
    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "footer.links",
    });

    const addFooterLink = () => {
        append({
            title: "",
            url: "",
        });
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Rodapé</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <FormField
                    control={form.control}
                    name="footer.text"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Texto do Rodapé</FormLabel>
                            <FormControl>
                                <Textarea
                                    {...field}
                                    placeholder="© 2024 Minha Empresa. Todos os direitos reservados."
                                    rows={3}
                                />
                            </FormControl>
                        </FormItem>
                    )}
                />

                <div className="space-y-2">
                    <FormLabel>Links do Rodapé</FormLabel>
                    {fields.map((field, index) => (
                        <div key={field.id} className="flex items-end gap-2">
                            <div className="flex-1 grid grid-cols-2 gap-2">
                                <FormField
                                    control={form.control}
                                    name={`footer.links.${index}.title`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Título</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Política de Privacidade" />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name={`footer.links.${index}.url`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>URL</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="/privacy" />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => remove(index)}
                            >
                                <Trash2 className="size-4" />
                            </Button>
                        </div>
                    ))}

                    <Button
                        type="button"
                        variant="outline"
                        onClick={addFooterLink}
                        className="w-full"
                    >
                        <Plus className="size-4 mr-2" />
                        Adicionar Link do Rodapé
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
