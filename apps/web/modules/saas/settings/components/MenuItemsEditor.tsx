"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { FormField, FormItem, FormLabel, FormControl } from "@ui/components/form";
import { Trash2, Plus, GripVertical } from "lucide-react";
import { useFieldArray } from "react-hook-form";

export function MenuItemsEditor({ form }: { form: any }) {
    const { fields, append, remove, move } = useFieldArray({
        control: form.control,
        name: "menuItems",
    });

    const addMenuItem = () => {
        append({
            title: "",
            url: "",
            icon: "",
            position: fields.length + 1,
        });
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Itens do Menu</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {fields.map((field, index) => (
                    <div key={field.id} className="flex items-center gap-2 p-4 border rounded-lg">
                        <GripVertical className="size-4 text-muted-foreground cursor-move" />

                        <div className="flex-1 grid grid-cols-3 gap-2">
                            <FormField
                                control={form.control}
                                name={`menuItems.${index}.title`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Título</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="Dashboard" />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name={`menuItems.${index}.url`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>URL</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="/dashboard" />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name={`menuItems.${index}.icon`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Ícone</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="dashboard" />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => remove(index)}
                        >
                            <Trash2 className="size-4" />
                        </Button>
                    </div>
                ))}

                <Button
                    type="button"
                    variant="outline"
                    onClick={addMenuItem}
                    className="w-full"
                >
                    <Plus className="size-4 mr-2" />
                    Adicionar Item do Menu
                </Button>
            </CardContent>
        </Card>
    );
}
