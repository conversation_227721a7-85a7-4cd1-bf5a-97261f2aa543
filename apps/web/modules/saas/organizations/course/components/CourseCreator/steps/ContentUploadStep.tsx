'use client'

import { useState, useCallback, useEffect } from 'react'
import { CourseCreationData, VideoUploadData, MaterialUploadData } from '../../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Progress } from '@/modules/ui/components/progress'
import { Badge } from '@/modules/ui/components/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/modules/ui/components/tabs'
import { Alert, AlertDescription } from '@/modules/ui/components/alert'
import {
  Upload,
  Video,
  FileText,
  Image,
  Download,
  X,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react'
import { useDropzone } from 'react-dropzone'
import { toast } from 'sonner'


interface ContentUploadStepProps {
  data: CourseCreationData
  onUpdate: (data: Partial<CourseCreationData>) => void
  onNext: () => void
  onPrevious: () => void
  organizationSlug: string
}

type UploadStatus = 'pending' | 'uploading' | 'completed' | 'error'

interface UploadProgress {
  [key: string]: {
    progress: number
    status: UploadStatus
    error?: string
  }
}

export function ContentUploadStep({ data, onUpdate, onNext, onPrevious }: ContentUploadStepProps) {
  const [videos, setVideos] = useState<VideoUploadData[]>(data.content.videos)
  const [materials, setMaterials] = useState<MaterialUploadData[]>(data.content.materials)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({})
  const [activeTab, setActiveTab] = useState('videos')

  // Video upload dropzone
  const onVideosDrop = useCallback((acceptedFiles: File[]) => {
    const newVideos: VideoUploadData[] = acceptedFiles.map(file => ({
      id: `video-${Date.now()}-${Math.random()}`,
      file,
      name: file.name.replace(/\.[^/.]+$/, ''),
      uploadProgress: 0,
      status: 'pending'
    }))

    setVideos(prev => [...prev, ...newVideos])

    // Start upload simulation for each video
    newVideos.forEach(video => {
      simulateUpload(video.id, 'video')
    })
  }, [])

  // Materials upload dropzone
  const onMaterialsDrop = useCallback((acceptedFiles: File[]) => {
    const newMaterials: MaterialUploadData[] = acceptedFiles.map(file => ({
      id: `material-${Date.now()}-${Math.random()}`,
      file,
      name: file.name.replace(/\.[^/.]+$/, ''),
      type: getFileType(file),
      size: file.size,
      uploadProgress: 0,
      status: 'pending'
    }))

    setMaterials(prev => [...prev, ...newMaterials])

    // Start upload simulation for each material
    newMaterials.forEach(material => {
      simulateUpload(material.id, 'material')
    })
  }, [])

  const videoDropzone = useDropzone({
    onDrop: onVideosDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm']
    },
    maxSize: 2 * 1024 * 1024 * 1024, // 2GB
    multiple: true
  })

  const materialDropzone = useDropzone({
    onDrop: onMaterialsDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'text/*': ['.txt', '.md'],
      'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.svg']
    },
    maxSize: 100 * 1024 * 1024, // 100MB
    multiple: true
  })

  const getFileType = (file: File): string => {
    if (file.type.startsWith('image/')) return 'image'
    if (file.type === 'application/pdf') return 'pdf'
    if (file.type.includes('word') || file.type.includes('document')) return 'document'
    if (file.type.includes('presentation') || file.type.includes('powerpoint')) return 'presentation'
    return 'other'
  }

  const simulateUpload = (id: string, type: 'video' | 'material') => {
    setUploadProgress(prev => ({
      ...prev,
      [id]: { progress: 0, status: 'uploading' }
    }))

    // Update the item status
    if (type === 'video') {
      setVideos(prev => prev.map(v =>
        v.id === id ? { ...v, status: 'uploading' } : v
      ))
    } else {
      setMaterials(prev => prev.map(m =>
        m.id === id ? { ...m, status: 'uploading' } : m
      ))
    }

    // Simulate progress
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        const current = prev[id]?.progress || 0
        const newProgress = Math.min(current + Math.random() * 15, 100)

        if (newProgress >= 100) {
          clearInterval(interval)

          // Simulate occasional errors (10% chance)
          const hasError = Math.random() < 0.1

          if (hasError) {
            // Update item status to error
            if (type === 'video') {
              setVideos(prev => prev.map(v =>
                v.id === id ? { ...v, status: 'error' } : v
              ))
            } else {
              setMaterials(prev => prev.map(m =>
                m.id === id ? { ...m, status: 'error' } : m
              ))
            }

            return {
              ...prev,
              [id]: {
                progress: 100,
                status: 'error',
                error: 'Erro no upload. Tente novamente.'
              }
            }
          } else {
            // Update item status to completed
            if (type === 'video') {
              setVideos(prev => prev.map(v =>
                v.id === id ? { ...v, status: 'completed' } : v
              ))
            } else {
              setMaterials(prev => prev.map(m =>
                m.id === id ? { ...m, status: 'completed' } : m
              ))
            }

            return {
              ...prev,
              [id]: { progress: 100, status: 'completed' }
            }
          }
        }

        return {
          ...prev,
          [id]: { progress: newProgress, status: 'uploading' }
        }
      })
    }, 200)
  }

  const retryUpload = (id: string, type: 'video' | 'material') => {
    simulateUpload(id, type)
  }

  const removeVideo = (id: string) => {
    setVideos(prev => prev.filter(v => v.id !== id))
    setUploadProgress(prev => {
      const { [id]: removed, ...rest } = prev
      return rest
    })
  }

  const removeMaterial = (id: string) => {
    setMaterials(prev => prev.filter(m => m.id !== id))
    setUploadProgress(prev => {
      const { [id]: removed, ...rest } = prev
      return rest
    })
  }

  const updateVideoData = (id: string, updates: Partial<VideoUploadData>) => {
    setVideos(prev => prev.map(v =>
      v.id === id ? { ...v, ...updates } : v
    ))
  }

  const updateMaterialData = (id: string, updates: Partial<MaterialUploadData>) => {
    setMaterials(prev => prev.map(m =>
      m.id === id ? { ...m, ...updates } : m
    ))
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: UploadStatus) => {
    switch (status) {
      case 'uploading':
        return <RotateCcw className="h-4 w-4 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getModuleOptions = () => {
    return data.structure.modules.map(module => ({
      value: module.id!,
      label: module.name
    }))
  }

  const getLessonOptions = (moduleId: string) => {
    const module = data.structure.modules.find(m => m.id === moduleId)
    return module?.lessons.map(lesson => ({
      value: lesson.id!,
      label: lesson.name
    })) || []
  }

  // Update parent data when videos or materials change
  useEffect(() => {
    onUpdate({
      content: {
        videos,
        materials
      }
    })
  }, [videos, materials, onUpdate])

  const totalUploads = videos.length + materials.length
  const completedUploads = videos.filter(v => v.status === 'completed').length +
                          materials.filter(m => m.status === 'completed').length
  const hasErrors = videos.some(v => v.status === 'error') ||
                   materials.some(m => m.status === 'error')

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Upload de Conteúdo
        </h2>
        <p className="text-gray-600">
          Faça upload dos vídeos e materiais do seu curso
        </p>
      </div>

      {/* Upload Progress Summary */}
      {totalUploads > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progresso do Upload</span>
              <span className="text-sm text-gray-500">
                {completedUploads}/{totalUploads} concluídos
              </span>
            </div>
            <Progress value={(completedUploads / totalUploads) * 100} className="mb-2" />
            {hasErrors && (
              <Alert className="mt-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Alguns arquivos falharam no upload. Clique em "Tentar Novamente" para reenviar.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="videos" className="flex items-center gap-2">
            <Video className="h-4 w-4" />
            Vídeos ({videos.length})
          </TabsTrigger>
          <TabsTrigger value="materials" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Materiais ({materials.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="videos" className="space-y-4">
          {/* Video Upload Area */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Video className="h-5 w-5" />
                Upload de Vídeos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                {...videoDropzone.getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  videoDropzone.isDragActive
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...videoDropzone.getInputProps()} />
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  {videoDropzone.isDragActive
                    ? 'Solte os vídeos aqui...'
                    : 'Arraste vídeos ou clique para selecionar'
                  }
                </p>
                <p className="text-sm text-gray-500">
                  Formatos suportados: MP4, MOV, AVI, MKV, WebM (máx. 2GB cada)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Video List */}
          {videos.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Vídeos Enviados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {videos.map((video) => (
                    <div key={video.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-100 rounded">
                            <Video className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium">{video.name}</h4>
                            <p className="text-sm text-gray-500">
                              {formatFileSize(video.file.size)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(video.status)}
                          <Badge
                            status={video.status === 'completed' ? 'success' :
                                   video.status === 'error' ? 'error' : 'info'}
                          >
                            {video.status === 'pending' && 'Aguardando'}
                            {video.status === 'uploading' && 'Enviando'}
                            {video.status === 'completed' && 'Concluído'}
                            {video.status === 'error' && 'Erro'}
                          </Badge>
                          {video.status === 'error' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => retryUpload(video.id, 'video')}
                            >
                              Tentar Novamente
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeVideo(video.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {uploadProgress[video.id] && video.status === 'uploading' && (
                        <div className="mb-3">
                          <Progress value={uploadProgress[video.id].progress} />
                          <p className="text-xs text-gray-500 mt-1">
                            {Math.round(uploadProgress[video.id].progress)}% concluído
                          </p>
                        </div>
                      )}

                      {video.status === 'error' && uploadProgress[video.id]?.error && (
                        <Alert className="mb-3">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            {uploadProgress[video.id].error}
                          </AlertDescription>
                        </Alert>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`video-name-${video.id}`}>Nome do Vídeo</Label>
                          <Input
                            id={`video-name-${video.id}`}
                            value={video.name}
                            onChange={(e) => updateVideoData(video.id, { name: e.target.value })}
                            placeholder="Nome do vídeo"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`video-module-${video.id}`}>Módulo</Label>
                          <select
                            id={`video-module-${video.id}`}
                            value={video.moduleId}
                            onChange={(e) => updateVideoData(video.id, { moduleId: e.target.value, lessonId: '' })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Selecionar módulo</option>
                            {getModuleOptions().map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`video-lesson-${video.id}`}>Aula</Label>
                          <select
                            id={`video-lesson-${video.id}`}
                            value={video.lessonId}
                            onChange={(e) => updateVideoData(video.id, { lessonId: e.target.value })}
                            disabled={!video.moduleId}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                          >
                            <option value="">Selecionar aula</option>
                            {video.moduleId && getLessonOptions(video.moduleId).map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="materials" className="space-y-4">
          {/* Materials Upload Area */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Upload de Materiais
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                {...materialDropzone.getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  materialDropzone.isDragActive
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...materialDropzone.getInputProps()} />
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  {materialDropzone.isDragActive
                    ? 'Solte os materiais aqui...'
                    : 'Arraste materiais ou clique para selecionar'
                  }
                </p>
                <p className="text-sm text-gray-500">
                  Formatos suportados: PDF, DOC, DOCX, PPT, PPTX, TXT, MD, imagens (máx. 100MB cada)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Materials List */}
          {materials.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Materiais Enviados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {materials.map((material) => (
                    <div key={material.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-green-100 rounded">
                            {material.type === 'image' && <Image className="h-5 w-5 text-green-600" />}
                            {material.type === 'pdf' && <FileText className="h-5 w-5 text-green-600" />}
                            {(material.type === 'document' || material.type === 'presentation') && <FileText className="h-5 w-5 text-green-600" />}
                            {material.type === 'other' && <FileText className="h-5 w-5 text-green-600" />}
                          </div>
                          <div>
                            <h4 className="font-medium">{material.name}</h4>
                            <p className="text-sm text-gray-500">
                              {formatFileSize(material.size)} • {material.type}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(material.status)}
                          <Badge
                            status={material.status === 'completed' ? 'success' :
                                   material.status === 'error' ? 'error' : 'info'}
                          >
                            {material.status === 'pending' && 'Aguardando'}
                            {material.status === 'uploading' && 'Enviando'}
                            {material.status === 'completed' && 'Concluído'}
                            {material.status === 'error' && 'Erro'}
                          </Badge>
                          {material.status === 'error' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => retryUpload(material.id, 'material')}
                            >
                              Tentar Novamente
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeMaterial(material.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {uploadProgress[material.id] && material.status === 'uploading' && (
                        <div className="mb-3">
                          <Progress value={uploadProgress[material.id].progress} />
                          <p className="text-xs text-gray-500 mt-1">
                            {Math.round(uploadProgress[material.id].progress)}% concluído
                          </p>
                        </div>
                      )}

                      {material.status === 'error' && uploadProgress[material.id]?.error && (
                        <Alert className="mb-3">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            {uploadProgress[material.id].error}
                          </AlertDescription>
                        </Alert>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`material-name-${material.id}`}>Nome do Material</Label>
                          <Input
                            id={`material-name-${material.id}`}
                            value={material.name}
                            onChange={(e) => updateMaterialData(material.id, { name: e.target.value })}
                            placeholder="Nome do material"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`material-module-${material.id}`}>Módulo</Label>
                          <select
                            id={`material-module-${material.id}`}
                            value={material.moduleId}
                            onChange={(e) => updateMaterialData(material.id, { moduleId: e.target.value, lessonId: '' })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Selecionar módulo</option>
                            {getModuleOptions().map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`material-lesson-${material.id}`}>Aula</Label>
                          <select
                            id={`material-lesson-${material.id}`}
                            value={material.lessonId}
                            onChange={(e) => updateMaterialData(material.id, { lessonId: e.target.value })}
                            disabled={!material.moduleId}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                          >
                            <option value="">Selecionar aula</option>
                            {material.moduleId && getLessonOptions(material.moduleId).map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          Anterior
        </Button>
        <Button onClick={onNext}>
          Próximo
        </Button>
      </div>
    </div>
  )
}
