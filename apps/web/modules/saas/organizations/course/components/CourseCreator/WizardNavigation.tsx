'use client'

import { WizardS<PERSON> } from '../../types'
import { cn } from '@/modules/ui/lib'
import { Check } from 'lucide-react'

interface WizardNavigationProps {
  steps: WizardStep[]
  currentStep: number
  onStepChange: (stepIndex: number) => void
}

export function WizardNavigation({ steps, currentStep, onStepChange }: WizardNavigationProps) {
  return (
    <nav className="flex items-center space-x-4">
      {steps.map((step, index) => {
        const isActive = index === currentStep
        const isCompleted = step.isCompleted
        const isPast = index < currentStep
        const isClickable = isPast || isActive

        return (
          <div key={step.id} className="flex items-center">
            <button
              onClick={() => isClickable && onStepChange(index)}
              disabled={!isClickable}
              className={cn(
                'flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200',
                {
                  'bg-primary border-primary text-primary-foreground': isActive,
                  'bg-success border-success text-success-foreground': isCompleted,
                  'border-border text-muted-foreground': !isActive && !isCompleted && !isPast,
                  'border-muted-foreground text-foreground hover:border-foreground': isPast && !isCompleted,
                  'cursor-pointer': isClickable,
                  'cursor-not-allowed': !isClickable,
                }
              )}
            >
              {isCompleted ? (
                <Check className="h-4 w-4" />
              ) : (
                <span className="text-sm font-medium">{index + 1}</span>
              )}
            </button>

            <div className="ml-3 hidden sm:block">
              <p className={cn(
                'text-sm font-medium transition-colors',
                {
                  'text-primary': isActive,
                  'text-success': isCompleted,
                  'text-foreground': isPast && !isCompleted,
                  'text-muted-foreground': !isActive && !isCompleted && !isPast,
                }
              )}>
                {step.title}
                {step.isOptional && (
                  <span className="ml-1 text-xs text-muted-foreground">(opcional)</span>
                )}
              </p>
              <p className="text-xs text-muted-foreground">{step.description}</p>
            </div>

            {index < steps.length - 1 && (
              <div className={cn(
                'ml-4 w-8 h-0.5 transition-colors',
                {
                  'bg-success': isCompleted,
                  'bg-primary': isActive,
                  'bg-border': !isActive && !isCompleted,
                }
              )} />
            )}
          </div>
        )
      })}
    </nav>
  )
}
