'use client'

import { useState, useEffect } from 'react'
import { CourseCreationData } from '../../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Textarea } from '@/modules/ui/components/textarea'

import { Badge } from '@/modules/ui/components/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Upload, X, Sparkles } from 'lucide-react'
import { useSmartSuggestions } from '../../../hooks/useSmartSuggestions'

interface BasicInfoStepProps {
  data: CourseCreationData
  onUpdate: (data: Partial<CourseCreationData>) => void
  onNext: () => void
  onPrevious: () => void
  organizationSlug: string
}

const COURSE_CATEGORIES = [
  'Programação',
  'Design',
  'Marketing',
  'Negócios',
  'Fotografia',
  'Música',
  'Idiomas',
  'Saúde e Fitness',
  'Culinária',
  'Desenvolvimento Pessoal',
]

const DIFFICULTY_LEVELS = [
  { value: 'beginner', label: 'Iniciante' },
  { value: 'intermediate', label: 'Intermediário' },
  { value: 'advanced', label: 'Avançado' },
] as const

export function BasicInfoStep({ data, onUpdate, onNext }: BasicInfoStepProps) {
  const [formData, setFormData] = useState(data.basicInfo)
  const [newTag, setNewTag] = useState('')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const { getSuggestions, isLoading: isSuggestionsLoading } = useSmartSuggestions()

  useEffect(() => {
    onUpdate({ basicInfo: formData })
  }, [formData, onUpdate])

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setLogoFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setLogoPreview(result)
        setFormData(prev => ({ ...prev, logo: result }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleGenerateDescription = async () => {
    if (!formData.name || !formData.category) return

    try {
      const suggestions = await getSuggestions({
        type: 'description',
        context: {
          name: formData.name,
          category: formData.category,
          difficulty: formData.difficulty
        }
      })

      if (suggestions.length > 0) {
        setFormData(prev => ({ ...prev, description: suggestions[0].content }))
      }
    } catch (error) {
      console.error('Erro ao gerar sugestão:', error)
    }
  }

  const isFormValid = formData.name.trim() && formData.description.trim() && formData.category

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">
          Informações Básicas do Curso
        </h2>
        <p className="opacity-60">
          Defina as informações principais que identificarão seu curso
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="course-name">Nome do Curso *</Label>
            <Input
              id="course-name"
              placeholder="Ex: Curso Completo de React"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="text-lg"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="course-description">Descrição *</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleGenerateDescription}
                disabled={!formData.name || !formData.category || isSuggestionsLoading}
                className="flex items-center gap-2"
              >
                <Sparkles className="h-4 w-4" />
                {isSuggestionsLoading ? 'Gerando...' : 'Gerar com IA'}
              </Button>
            </div>
            <Textarea
              id="course-description"
              placeholder="Descreva o que os alunos aprenderão neste curso..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="course-short-description">Descrição Curta</Label>
            <Input
              id="course-short-description"
              placeholder="Resumo em uma linha"
              value={formData.shortDescription}
              onChange={(e) => handleInputChange('shortDescription', e.target.value)}
              maxLength={120}
            />
            <p className="text-xs text-muted-foreground">
              {formData.shortDescription.length}/120 caracteres
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Categoria *</Label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="">Selecione uma categoria</option>
                {COURSE_CATEGORIES.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label>Nível de Dificuldade</Label>
              <select
                value={formData.difficulty}
                onChange={(e) => handleInputChange('difficulty', e.target.value as 'beginner' | 'intermediate' | 'advanced')}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                {DIFFICULTY_LEVELS.map((level) => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar tag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              />
              <Button type="button" onClick={handleAddTag} disabled={!newTag.trim()}>
                Adicionar
              </Button>
            </div>
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.tags.map((tag) => (
                  <Badge key={tag} className="flex items-center gap-1 bg-muted text-muted-foreground">
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Logo do Curso</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {logoPreview ? (
                  <div className="relative">
                    <img
                      src={logoPreview}
                      alt="Preview do logo"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setLogoPreview(null)
                        setLogoFile(null)
                        setFormData(prev => ({ ...prev, logo: undefined }))
                      }}
                      className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-border border-dashed rounded-lg cursor-pointer bg-muted hover:bg-accent">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
                      <p className="mb-2 text-sm text-muted-foreground">
                        <span className="font-semibold">Clique para fazer upload</span>
                      </p>
                      <p className="text-xs text-muted-foreground">PNG, JPG ou JPEG (MAX. 2MB)</p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleLogoUpload}
                    />
                  </label>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={onNext}
          disabled={!isFormValid}
          className="min-w-[120px]"
        >
          Próximo
        </Button>
      </div>
    </div>
  )
}
