'use client'

import { useState, useCallback } from 'react'
import { CourseCreationData, WizardStep } from '../../types'
import { WizardNavigation } from './WizardNavigation'
import { BasicInfoStep } from './steps/BasicInfoStep'
import { ContentStructureStep } from './steps/ContentStructureStep'
import { ContentUploadStep } from './steps/ContentUploadStep'
import { SettingsStep } from './steps/SettingsStep'
import { useCourseCreation } from '../../hooks/useCourseCreation'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface CourseCreationWizardProps {
  organizationSlug: string
}

const WIZARD_STEPS: WizardStep[] = [
  {
    id: 'basic-info',
    title: 'Informações Básicas',
    description: 'Nome, descrição e categoria',
    component: BasicInfoStep,
    isCompleted: false
  },
  {
    id: 'structure',
    title: 'Estrutura',
    description: 'Módulos e aulas',
    component: ContentStructureStep,
    isCompleted: false
  },
  {
    id: 'content',
    title: 'Conteú<PERSON>',
    description: 'Vídeos e materiais',
    component: ContentUploadStep,
    isCompleted: false
  },
  {
    id: 'settings',
    title: 'Configurações',
    description: 'Preço e publicação',
    component: SettingsStep,
    isCompleted: false
  }
]

export function CourseCreationWizard({ organizationSlug }: CourseCreationWizardProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [courseData, setCourseData] = useState<CourseCreationData>({
    basicInfo: {
      name: '',
      description: '',
      shortDescription: '',
      category: '',
      difficulty: 'beginner',
      tags: [],
      logo: ''
    },
    structure: {
      modules: [],
      estimatedDuration: ''
    },
    content: {
      videos: [],
      materials: []
    },
    settings: {
      isPublic: true,
      allowComments: true,
      certificateEnabled: false,
      pricing: {
        type: 'free',
        price: 0,
        currency: 'BRL'
      }
    }
  })

  const { createCourse, isCreating } = useCourseCreation()
  const router = useRouter()

  const handleStepChange = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  const handleNext = () => {
    if (currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleFinish()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleDataUpdate = useCallback((data: Partial<CourseCreationData>) => {
    setCourseData(prev => ({ ...prev, ...data }))
  }, [])

  const handleFinish = async () => {
    try {
      const courseId = await createCourse(courseData, organizationSlug)
      toast.success('Curso criado com sucesso!')
      handleComplete(courseId)
    } catch (error) {
      console.error('Erro ao criar curso:', error)
      toast.error('Erro ao criar curso. Tente novamente.')
    }
  }

  const handleComplete = (courseId: string) => {
    router.push(`/${organizationSlug}/course/${courseId}`)
  }

  const handleCancel = () => {
    router.push(`/${organizationSlug}`)
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <BasicInfoStep
            data={courseData}
            onUpdate={handleDataUpdate}
            onNext={handleNext}
            onPrevious={handlePrevious}
            organizationSlug={organizationSlug}
          />
        )
      case 1:
        return (
          <ContentStructureStep
            data={courseData}
            onUpdate={handleDataUpdate}
            onNext={handleNext}
            onPrevious={handlePrevious}
            organizationSlug={organizationSlug}
          />
        )
      case 2:
        return (
          <ContentUploadStep
            data={courseData}
            onUpdate={handleDataUpdate}
            onNext={handleNext}
            onPrevious={handlePrevious}
            organizationSlug={organizationSlug}
          />
        )
      case 3:
        return (
          <SettingsStep
            data={courseData}
            onUpdate={handleDataUpdate}
            onNext={handleFinish}
            onPrevious={handlePrevious}
            organizationSlug={organizationSlug}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <WizardNavigation
        steps={WIZARD_STEPS}
        currentStep={currentStep}
        onStepChange={handleStepChange}
      />

      <div className="mt-8">
        {renderCurrentStep()}
      </div>
    </div>
  )
}
