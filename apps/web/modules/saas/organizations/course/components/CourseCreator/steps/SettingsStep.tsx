'use client'

import { useState, useC<PERSON>back, useEffect } from 'react'
import { CourseCreationData, PricingData } from '../../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Textarea } from '@/modules/ui/components/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Switch } from '@/modules/ui/components/switch'
import { Badge } from '@/modules/ui/components/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/modules/ui/components/tabs'
import { Alert, AlertDescription } from '@/modules/ui/components/alert'
import {
  DollarSign,
  Users,
  Calendar,
  Globe,
  Lock,
  Eye,
  EyeOff,
  Info,
  CheckCircle,
  AlertCircle,
  Settings,
  Zap
} from 'lucide-react'
import { DatePicker } from '@/modules/ui/components/date-picker'
import { toast } from 'sonner'

interface SettingsStepProps {
  data: CourseCreationData
  onUpdate: (data: Partial<CourseCreationData>) => void
  onNext: () => void
  onPrevious: () => void
  organizationSlug: string
}

type AccessType = 'free' | 'paid' | 'subscription' | 'private'
type PublishStatus = 'draft' | 'scheduled' | 'published'

export function SettingsStep({ data, onUpdate, onNext, onPrevious }: SettingsStepProps) {
  const [pricing, setPricing] = useState<PricingData>(data.settings?.pricing || {
    type: 'free',
    price: 0,
    currency: 'BRL'
  })
  const [accessType, setAccessType] = useState<AccessType>('free')
  const [publishStatus, setPublishStatus] = useState<PublishStatus>('draft')
  const [publishDate, setPublishDate] = useState<Date | undefined>(undefined)
  const [isPublic, setIsPublic] = useState(true)
  const [allowComments, setAllowComments] = useState(true)
  const [allowDownloads, setAllowDownloads] = useState(true)
  const [certificateEnabled, setCertificateEnabled] = useState(false)
  const [maxStudents, setMaxStudents] = useState(0)
  const [seoTitle, setSeoTitle] = useState('')
  const [seoDescription, setSeoDescription] = useState('')
  const [seoKeywords, setSeoKeywords] = useState('')
  const [activeTab, setActiveTab] = useState('pricing')

  const updateSettings = useCallback(() => {
    onUpdate({
      settings: {
        pricing,
        isPublic,
        allowComments,
        certificateEnabled
      }
    })
  }, [pricing, isPublic, allowComments, certificateEnabled, onUpdate])

  const handlePricingChange = useCallback((field: keyof PricingData, value: any) => {
    setPricing(prev => ({ ...prev, [field]: value }))
  }, [])

  const handleAccessTypeChange = useCallback((type: AccessType) => {
    setAccessType(type)

    if (type === 'free') {
      setPricing({
        type: 'free',
        price: 0,
        currency: 'BRL'
      })
    } else if (type === 'paid') {
      setPricing(prev => ({ ...prev, type: 'paid' }))
    } else if (type === 'subscription') {
      setPricing(prev => ({ ...prev, type: 'subscription' }))
    }
  }, [])

  const formatCurrency = useCallback((value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: pricing.currency || 'BRL'
    }).format(value)
  }, [pricing.currency])

  const getAccessTypeIcon = useCallback((type: AccessType) => {
    switch (type) {
      case 'free':
        return <Globe className="h-5 w-5 text-green-500" />
      case 'paid':
        return <DollarSign className="h-5 w-5 text-blue-500" />
      case 'subscription':
        return <Zap className="h-5 w-5 text-purple-500" />
      case 'private':
        return <Lock className="h-5 w-5 text-gray-500" />
      default:
        return null
    }
  }, [])

  const getPublishStatusBadge = useCallback((status: PublishStatus) => {
    switch (status) {
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800">Rascunho</Badge>
      case 'scheduled':
        return <Badge className="border border-gray-300 text-gray-700">Agendado</Badge>
      case 'published':
        return <Badge className="bg-green-100 text-green-800">Publicado</Badge>
      default:
        return null
    }
  }, [])

  const isFormValid = useCallback(() => {
    if (accessType === 'paid' || accessType === 'subscription') {
      return (pricing.price || 0) > 0
    }
    if (publishStatus === 'scheduled') {
      return publishDate && publishDate > new Date()
    }
    return true
  }, [accessType, pricing.price, publishStatus, publishDate])

  const getEstimatedRevenue = useCallback((): string => {
    if (accessType !== 'paid' && accessType !== 'subscription') return ''

    const studentsEstimate = maxStudents || 100
    const conversionRate = 0.05
    const expectedStudents = Math.floor(studentsEstimate * conversionRate)
    const revenue = expectedStudents * (pricing.price || 0)

    return formatCurrency(revenue)
  }, [accessType, maxStudents, pricing.price, formatCurrency])

  // Update settings when dependencies change
  useEffect(() => {
    updateSettings()
  }, [updateSettings])

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">
          Configurações e Publicação
        </h2>
        <p className="opacity-60">
          Configure o preço, acesso e outras configurações do seu curso
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pricing" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Preço
          </TabsTrigger>
          <TabsTrigger value="access" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Acesso
          </TabsTrigger>
          <TabsTrigger value="publish" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Publicação
          </TabsTrigger>
          <TabsTrigger value="seo" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            SEO
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pricing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Tipo de Acesso</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { type: 'free' as AccessType, label: 'Gratuito', description: 'Curso totalmente gratuito' },
                  { type: 'paid' as AccessType, label: 'Pago', description: 'Pagamento único' },
                  { type: 'subscription' as AccessType, label: 'Assinatura', description: 'Pagamento recorrente' },
                  { type: 'private' as AccessType, label: 'Privado', description: 'Acesso restrito' }
                ].map((option) => (
                  <div
                    key={option.type}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      accessType === option.type
                        ? 'border-primary bg-primary/10'
                        : 'border-border hover:border-foreground'
                    }`}
                    onClick={() => handleAccessTypeChange(option.type)}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {getAccessTypeIcon(option.type)}
                      <span className="font-medium">{option.label}</span>
                    </div>
                    <p className="text-sm text-gray-500">{option.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {(accessType === 'paid' || accessType === 'subscription') && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Configuração de Preço</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Preço *</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="price"
                        type="number"
                        min="0"
                        step="0.01"
                        value={pricing.price || ''}
                        onChange={(e) => handlePricingChange('price', parseFloat(e.target.value) || 0)}
                        className="pl-10"
                        placeholder="0,00"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Moeda</Label>
                    <select
                      id="currency"
                      value={pricing.currency}
                      onChange={(e) => handlePricingChange('currency', e.target.value)}
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                    >
                      <option value="BRL">Real (BRL)</option>
                      <option value="USD">Dólar (USD)</option>
                      <option value="EUR">Euro (EUR)</option>
                    </select>
                  </div>
                </div>

                {getEstimatedRevenue() && (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      Receita estimada (5% conversão): <strong>{getEstimatedRevenue()}</strong>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="access" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Configurações de Acesso</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Permitir Comentários</Label>
                    <p className="text-sm text-muted-foreground">Estudantes podem comentar nas aulas</p>
                  </div>
                  <Switch
                    checked={allowComments}
                    onCheckedChange={setAllowComments}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Permitir Downloads</Label>
                    <p className="text-sm text-muted-foreground">Estudantes podem baixar materiais</p>
                  </div>
                  <Switch
                    checked={allowDownloads}
                    onCheckedChange={setAllowDownloads}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Certificado de Conclusão</Label>
                    <p className="text-sm text-muted-foreground">Emitir certificado ao finalizar</p>
                  </div>
                  <Switch
                    checked={certificateEnabled}
                    onCheckedChange={setCertificateEnabled}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Limite de Alunos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="max-students">Número Máximo de Alunos</Label>
                  <Input
                    id="max-students"
                    type="number"
                    min="0"
                    value={maxStudents || ''}
                    onChange={(e) => setMaxStudents(parseInt(e.target.value) || 0)}
                    placeholder="0 = ilimitado"
                  />
                  <p className="text-sm text-muted-foreground">
                    Deixe 0 para permitir número ilimitado de alunos
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="publish" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status de Publicação</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    { status: 'draft' as PublishStatus, label: 'Rascunho', description: 'Curso não publicado' },
                    { status: 'scheduled' as PublishStatus, label: 'Agendado', description: 'Publicar em data específica' },
                    { status: 'published' as PublishStatus, label: 'Publicado', description: 'Curso disponível agora' }
                  ].map((option) => (
                    <div
                      key={option.status}
                                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      publishStatus === option.status
                        ? 'border-primary bg-primary/10'
                        : 'border-border hover:border-foreground'
                    }`}
                      onClick={() => setPublishStatus(option.status)}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        {getPublishStatusBadge(option.status)}
                      </div>
                      <h4 className="font-medium">{option.label}</h4>
                      <p className="text-sm text-gray-500">{option.description}</p>
                    </div>
                  ))}
                </div>

                {publishStatus === 'scheduled' && (
                  <div className="space-y-2">
                    <Label htmlFor="publish-date">Data de Publicação *</Label>
                    <DatePicker
                      value={publishDate}
                      onChange={setPublishDate}
                      placeholder="Selecionar data e hora"
                    />
                    {publishDate && publishDate <= new Date() && (
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          A data de publicação deve ser no futuro
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Visibilidade</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="is-public">Curso Público</Label>
                    <p className="text-sm text-muted-foreground">
                      {isPublic ? (
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          Visível em buscas e catálogo
                        </span>
                      ) : (
                        <span className="flex items-center gap-1">
                          <EyeOff className="h-3 w-3" />
                          Apenas via link direto
                        </span>
                      )}
                    </p>
                  </div>
                  <Switch
                    id="is-public"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Otimização para Buscadores (SEO)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="seo-title">Título SEO</Label>
                <Input
                  id="seo-title"
                  value={seoTitle}
                  onChange={(e) => setSeoTitle(e.target.value)}
                  placeholder={data.basicInfo.name || 'Título do curso para buscadores'}
                  maxLength={60}
                />
                <p className="text-xs text-muted-foreground">
                  {seoTitle.length}/60 caracteres (recomendado: 50-60)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seo-description">Descrição SEO</Label>
                <Textarea
                  id="seo-description"
                  value={seoDescription}
                  onChange={(e) => setSeoDescription(e.target.value)}
                  placeholder={data.basicInfo.shortDescription || 'Descrição do curso para buscadores'}
                  maxLength={160}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {seoDescription.length}/160 caracteres (recomendado: 150-160)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seo-keywords">Palavras-chave</Label>
                <Input
                  id="seo-keywords"
                  value={seoKeywords}
                  onChange={(e) => setSeoKeywords(e.target.value)}
                  placeholder="react, javascript, programação, curso online"
                />
                <p className="text-xs text-muted-foreground">
                  Separe as palavras-chave com vírgulas
                </p>
              </div>

              <div className="border rounded-lg p-4 bg-muted">
                <h4 className="font-medium mb-2">Preview nos Buscadores</h4>
                <div className="space-y-1">
                  <div className="text-blue-600 text-lg font-medium line-clamp-1">
                    {seoTitle || data.basicInfo.name || 'Título do Curso'}
                  </div>
                  <div className="text-green-600 text-sm">
                    https://cakto.com.br/curso/{data.basicInfo.name?.toLowerCase().replace(/\s+/g, '-') || 'nome-do-curso'}
                  </div>
                  <div className="text-gray-600 text-sm line-clamp-2">
                    {seoDescription || data.basicInfo.shortDescription || 'Descrição do curso que aparecerá nos resultados de busca.'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-success" />
            Resumo do Curso
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold">
                {data.structure.modules.length}
              </div>
              <div className="text-sm text-muted-foreground">Módulos</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold">
                {data.structure.modules.reduce((acc, m) => acc + m.lessons.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Aulas</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold">
                {data.content.videos.length}
              </div>
              <div className="text-sm text-muted-foreground">Vídeos</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold">
                {accessType === 'free' ? 'Gratuito' : formatCurrency(pricing.price || 0)}
              </div>
              <div className="text-sm text-muted-foreground">Preço</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          Anterior
        </Button>
        <Button
          onClick={onNext}
          disabled={!isFormValid()}
          className="min-w-[120px]"
        >
          Criar Curso
        </Button>
      </div>
    </div>
  )
}
