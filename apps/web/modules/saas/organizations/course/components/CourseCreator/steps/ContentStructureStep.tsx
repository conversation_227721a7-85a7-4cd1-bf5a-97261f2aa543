'use client'

import { useState, useEffect } from 'react'
import { CourseCreationData, ModuleCreationData, LessonCreationData } from '../../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Textarea } from '@/modules/ui/components/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/modules/ui/components/dialog'
import { Badge } from '@/modules/ui/components/badge'
import { Plus, GripVertical, Edit, Trash2, BookOpen, Play, Sparkles } from 'lucide-react'
import { useCourseTemplates } from '../../../hooks/useCourseCreation'
import { useSmartSuggestions } from '../../../hooks/useSmartSuggestions'
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd'

interface ContentStructureStepProps {
  data: CourseCreationData
  onUpdate: (data: Partial<CourseCreationData>) => void
  onNext: () => void
  onPrevious: () => void
  organizationSlug: string
}

export function ContentStructureStep({ data, onUpdate, onNext }: ContentStructureStepProps) {
  const [modules, setModules] = useState<ModuleCreationData[]>(data.structure.modules)
  const [isModuleDialogOpen, setIsModuleDialogOpen] = useState(false)
  const [isLessonDialogOpen, setIsLessonDialogOpen] = useState(false)
  const [editingModule, setEditingModule] = useState<ModuleCreationData | null>(null)
  const [editingLesson, setEditingLesson] = useState<{ lesson: LessonCreationData; moduleId: string } | null>(null)
  const [selectedModuleId, setSelectedModuleId] = useState<string | null>(null)

  const { templates } = useCourseTemplates()
  const { getSuggestions, isLoading: isSuggestionsLoading } = useSmartSuggestions()

  useEffect(() => {
    const estimatedDuration = calculateEstimatedDuration(modules)
    onUpdate({
      structure: {
        modules,
        estimatedDuration
      }
    })
  }, [modules, onUpdate])

  const calculateEstimatedDuration = (modules: ModuleCreationData[]): string => {
    const totalLessons = modules.reduce((acc, module) => acc + module.lessons.length, 0)
    const estimatedMinutes = totalLessons * 15 // 15 min per lesson average
    const hours = Math.floor(estimatedMinutes / 60)
    const minutes = estimatedMinutes % 60
    return `${hours}h ${minutes}min`
  }

  const handleAddModule = () => {
    setEditingModule(null)
    setIsModuleDialogOpen(true)
  }

  const handleEditModule = (module: ModuleCreationData) => {
    setEditingModule(module)
    setIsModuleDialogOpen(true)
  }

  const handleSaveModule = (moduleData: Omit<ModuleCreationData, 'id' | 'position'>) => {
    if (editingModule) {
      setModules(prev => prev.map(m =>
        m.id === editingModule.id
          ? { ...m, ...moduleData }
          : m
      ))
    } else {
      const newModule: ModuleCreationData = {
        id: `module-${Date.now()}`,
        ...moduleData,
        position: modules.length + 1,
        lessons: []
      }
      setModules(prev => [...prev, newModule])
    }
    setIsModuleDialogOpen(false)
    setEditingModule(null)
  }

  const handleDeleteModule = (moduleId: string) => {
    setModules(prev => prev.filter(m => m.id !== moduleId))
  }

  const handleAddLesson = (moduleId: string) => {
    setSelectedModuleId(moduleId)
    setEditingLesson(null)
    setIsLessonDialogOpen(true)
  }

  const handleEditLesson = (lesson: LessonCreationData, moduleId: string) => {
    setSelectedModuleId(moduleId)
    setEditingLesson({ lesson, moduleId })
    setIsLessonDialogOpen(true)
  }

  const handleSaveLesson = (lessonData: Omit<LessonCreationData, 'id' | 'position'>) => {
    if (!selectedModuleId) return

    setModules(prev => prev.map(module => {
      if (module.id === selectedModuleId) {
        if (editingLesson) {
          return {
            ...module,
            lessons: module.lessons.map(l =>
              l.id === editingLesson.lesson.id
                ? { ...l, ...lessonData }
                : l
            )
          }
        } else {
          const newLesson: LessonCreationData = {
            id: `lesson-${Date.now()}`,
            ...lessonData,
            position: module.lessons.length + 1
          }
          return {
            ...module,
            lessons: [...module.lessons, newLesson]
          }
        }
      }
      return module
    }))

    setIsLessonDialogOpen(false)
    setEditingLesson(null)
    setSelectedModuleId(null)
  }

  const handleDeleteLesson = (moduleId: string, lessonId: string) => {
    setModules(prev => prev.map(module =>
      module.id === moduleId
        ? { ...module, lessons: module.lessons.filter(l => l.id !== lessonId) }
        : module
    ))
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const { source, destination, type } = result

    if (type === 'module') {
      const newModules = Array.from(modules)
      const [reorderedModule] = newModules.splice(source.index, 1)
      newModules.splice(destination.index, 0, reorderedModule)

      const updatedModules = newModules.map((module, index) => ({
        ...module,
        position: index + 1
      }))

      setModules(updatedModules)
    }
  }

  const handleUseTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (!template) return

    const templateModules: ModuleCreationData[] = template.modules.map((module, index) => ({
      id: `module-${Date.now()}-${index}`,
      name: module.name,
      description: module.description,
      position: index + 1,
      lessons: module.lessons.map((lesson, lessonIndex) => ({
        id: `lesson-${Date.now()}-${index}-${lessonIndex}`,
        name: lesson.name,
        description: lesson.description,
        position: lessonIndex + 1
      }))
    }))

    setModules(templateModules)
  }

  const generateModuleSuggestions = async () => {
    try {
      const suggestions = await getSuggestions({
        type: 'module',
        context: {
          name: data.basicInfo.name,
          category: data.basicInfo.category,
          difficulty: data.basicInfo.difficulty,
          existingModules: modules.map(m => m.name)
        }
      })

      const suggestedModules: ModuleCreationData[] = suggestions.slice(0, 5).map((suggestion, index) => ({
        id: `module-${Date.now()}-${index}`,
        name: suggestion.content,
        description: '',
        position: modules.length + index + 1,
        lessons: []
      }))

      setModules(prev => [...prev, ...suggestedModules])
    } catch (error) {
      console.error('Erro ao gerar sugestões de módulos:', error)
    }
  }

  const isFormValid = modules.length > 0 && modules.every(m => m.name.trim())

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Estrutura do Conteúdo
        </h2>
        <p className="text-gray-600">
          Organize seu curso em módulos e aulas para uma melhor experiência de aprendizado
        </p>
      </div>

      {/* Templates and AI Suggestions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Templates Prontos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {templates.map(template => (
                <div key={template.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{template.name}</p>
                    <p className="text-sm text-gray-500">{template.description}</p>
                    <Badge className="mt-1 bg-gray-100 text-gray-700 border border-gray-300">{template.category}</Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUseTemplate(template.id)}
                  >
                    Usar
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Sugestões Inteligentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Crie módulos rapidamente com nossa estrutura otimizada
              </p>
              <Button
                onClick={generateModuleSuggestions}
                disabled={!data.basicInfo.name || !data.basicInfo.category || isSuggestionsLoading}
                className="w-full"
              >
                {isSuggestionsLoading ? 'Criando...' : 'Criar Módulos Rápido'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Course Structure */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Estrutura do Curso</CardTitle>
            <div className="flex items-center gap-2">
              <Badge className="bg-gray-100 text-gray-700 border border-gray-300">
                {modules.length} módulos
              </Badge>
              <Badge className="bg-gray-100 text-gray-700 border border-gray-300">
                {modules.reduce((acc, m) => acc + m.lessons.length, 0)} aulas
              </Badge>
              <Button onClick={handleAddModule}>
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Módulo
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {modules.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">Nenhum módulo criado ainda</p>
              <Button onClick={handleAddModule}>
                Criar Primeiro Módulo
              </Button>
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="modules" type="module">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                    {modules.map((module, index) => {
                      const moduleId = module.id || `module-${Date.now()}-${index}`;
                      return (
                        <Draggable key={moduleId} draggableId={moduleId} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="border rounded-lg p-4 bg-white shadow-sm"
                          >
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <div {...provided.dragHandleProps}>
                                  <GripVertical className="h-5 w-5 text-gray-500" />
                                </div>
                                <div>
                                  <h3 className="font-medium text-gray-900">{module.name}</h3>
                                  {module.description && (
                                    <p className="text-sm text-gray-600">{module.description}</p>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge className="bg-gray-100 text-gray-700 border border-gray-300">
                                  {module.lessons.length} aulas
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditModule(module)}
                                  className="text-gray-600 hover:text-gray-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteModule(module.id || '')}
                                  className="text-gray-600 hover:text-red-600"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>

                            {/* Lessons */}
                            <div className="ml-8 space-y-2">
                              {module.lessons.map((lesson) => (
                                <div key={lesson.id} className="flex items-center justify-between p-2 bg-gray-100 rounded border">
                                  <div className="flex items-center gap-2">
                                    <Play className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm text-gray-700">{lesson.name}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleEditLesson(lesson, module.id!)}
                                      className="text-gray-500 hover:text-gray-700"
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleDeleteLesson(module.id!, lesson.id!)}
                                      className="text-gray-500 hover:text-red-600"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleAddLesson(module.id!)}
                                className="w-full mt-2 text-gray-700 border-gray-300 hover:bg-gray-50"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Adicionar Aula
                              </Button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    );
                    })}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </CardContent>
      </Card>

      {/* Module Dialog */}
      <ModuleDialog
        isOpen={isModuleDialogOpen}
        onClose={() => setIsModuleDialogOpen(false)}
        onSave={handleSaveModule}
        module={editingModule}
      />

      {/* Lesson Dialog */}
      <LessonDialog
        isOpen={isLessonDialogOpen}
        onClose={() => setIsLessonDialogOpen(false)}
        onSave={handleSaveLesson}
        lesson={editingLesson?.lesson}
      />

      <div className="flex justify-end">
        <Button
          onClick={onNext}
          disabled={!isFormValid}
          className="min-w-[120px]"
        >
          Próximo
        </Button>
      </div>
    </div>
  )
}

// Module Dialog Component
interface ModuleDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Omit<ModuleCreationData, 'id' | 'position'>) => void
  module?: ModuleCreationData | null
}

function ModuleDialog({ isOpen, onClose, onSave, module }: ModuleDialogProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')

  useEffect(() => {
    if (module) {
      setName(module.name)
      setDescription(module.description || '')
    } else {
      setName('')
      setDescription('')
    }
  }, [module, isOpen])

  const handleSave = () => {
    if (!name.trim()) return

    onSave({
      name: name.trim(),
      description: description.trim(),
      lessons: module?.lessons || []
    })

    setName('')
    setDescription('')
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {module ? 'Editar Módulo' : 'Novo Módulo'}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="module-name">Nome do Módulo *</Label>
            <Input
              id="module-name"
              placeholder="Ex: Introdução ao React"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="module-description">Descrição</Label>
            <Textarea
              id="module-description"
              placeholder="Descreva o que será abordado neste módulo..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button onClick={handleSave} disabled={!name.trim()}>
              {module ? 'Salvar' : 'Criar'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Lesson Dialog Component
interface LessonDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Omit<LessonCreationData, 'id' | 'position'>) => void
  lesson?: LessonCreationData | null
}

function LessonDialog({ isOpen, onClose, onSave, lesson }: LessonDialogProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [videoUrl, setVideoUrl] = useState('')
  const [externalLink, setExternalLink] = useState('')

  useEffect(() => {
    if (lesson) {
      setName(lesson.name)
      setDescription(lesson.description || '')
      setVideoUrl(lesson.videoUrl || '')
      setExternalLink(lesson.externalLink || '')
    } else {
      setName('')
      setDescription('')
      setVideoUrl('')
      setExternalLink('')
    }
  }, [lesson, isOpen])

  const handleSave = () => {
    if (!name.trim()) return

    onSave({
      name: name.trim(),
      description: description.trim(),
      videoUrl: videoUrl.trim() || undefined,
      externalLink: externalLink.trim() || undefined
    })

    setName('')
    setDescription('')
    setVideoUrl('')
    setExternalLink('')
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {lesson ? 'Editar Aula' : 'Nova Aula'}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="lesson-name">Nome da Aula *</Label>
            <Input
              id="lesson-name"
              placeholder="Ex: Criando seu primeiro componente"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lesson-description">Descrição</Label>
            <Textarea
              id="lesson-description"
              placeholder="Descreva o conteúdo desta aula..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lesson-video">URL do Vídeo</Label>
            <Input
              id="lesson-video"
              placeholder="https://..."
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lesson-link">Link Externo</Label>
            <Input
              id="lesson-link"
              placeholder="https://..."
              value={externalLink}
              onChange={(e) => setExternalLink(e.target.value)}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button onClick={handleSave} disabled={!name.trim()}>
              {lesson ? 'Salvar' : 'Criar'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
