'use client'

import { useState } from 'react'
import { LessonListProps, Lesson } from '../../types'
import { LessonCard } from './LessonCard'
import { LessonForm } from './LessonForm'
import { Button } from '@/modules/ui/components/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/modules/ui/components/dialog'
import { Plus } from 'lucide-react'

export function LessonList({ 
  lessons, 
  moduleId, 
  onAddLesson, 
  onEditLesson, 
  onDeleteLesson, 
  onReorderLessons 
}: LessonListProps) {
  const [showLessonForm, setShowLessonForm] = useState(false)
  const [editingLesson, setEditingLesson] = useState<Lesson | null>(null)
  const [draggedLesson, setDraggedLesson] = useState<string | null>(null)

  const handleAddLesson = () => {
    setEditingLesson(null)
    setShowLessonForm(true)
  }

  const handleEditLesson = (lesson: Lesson) => {
    setEditingLesson(lesson)
    setShowLessonForm(true)
  }

  const handleSaveLesson = (lessonData: any) => {
    if (editingLesson) {
      onEditLesson({ ...editingLesson, ...lessonData })
    } else {
      onAddLesson(moduleId)
    }
    setShowLessonForm(false)
    setEditingLesson(null)
  }

  const handleDragStart = (lessonId: string) => {
    setDraggedLesson(lessonId)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, targetLessonId: string) => {
    e.preventDefault()
    
    if (!draggedLesson || draggedLesson === targetLessonId) {
      setDraggedLesson(null)
      return
    }

    const draggedIndex = lessons.findIndex(l => l.id === draggedLesson)
    const targetIndex = lessons.findIndex(l => l.id === targetLessonId)
    
    if (draggedIndex === -1 || targetIndex === -1) return

    const newLessons = [...lessons]
    const [draggedItem] = newLessons.splice(draggedIndex, 1)
    newLessons.splice(targetIndex, 0, draggedItem)
    
    const reorderedLessons = newLessons.map((lesson, index) => ({
      ...lesson,
      position: index + 1
    }))
    
    onReorderLessons(reorderedLessons)
    setDraggedLesson(null)
  }

  const sortedLessons = [...lessons].sort((a, b) => a.position - b.position)

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">
          Aulas ({lessons.length})
        </span>
        <Button
          size="sm"
          onClick={handleAddLesson}
          className="bg-[#36B37E] hover:bg-[#2a8f66] h-7 px-2 text-xs"
        >
          <Plus className="h-3 w-3 mr-1" />
          Aula
        </Button>
      </div>
      
      {sortedLessons.length === 0 ? (
        <div className="text-center py-6 bg-gray-50 rounded border border-dashed border-gray-200">
          <p className="text-gray-500 text-sm mb-2">Nenhuma aula criada</p>
          <Button size="sm" onClick={handleAddLesson} className="bg-[#36B37E] hover:bg-[#2a8f66]">
            <Plus className="h-3 w-3 mr-1" />
            Primeira Aula
          </Button>
        </div>
      ) : (
        <div className="space-y-2">
          {sortedLessons.map((lesson) => (
            <div
              key={lesson.id}
              draggable
              onDragStart={() => handleDragStart(lesson.id)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, lesson.id)}
              className={`transition-opacity ${
                draggedLesson === lesson.id ? 'opacity-50' : 'opacity-100'
              }`}
            >
              <LessonCard
                lesson={lesson}
                onEdit={() => handleEditLesson(lesson)}
                onDelete={() => onDeleteLesson(lesson.id)}
              />
            </div>
          ))}
        </div>
      )}
      
      <Dialog open={showLessonForm} onOpenChange={setShowLessonForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingLesson ? 'Editar Aula' : 'Nova Aula'}
            </DialogTitle>
          </DialogHeader>
          
          <LessonForm
            lesson={editingLesson}
            onSave={handleSaveLesson}
            onCancel={() => {
              setShowLessonForm(false)
              setEditingLesson(null)
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}