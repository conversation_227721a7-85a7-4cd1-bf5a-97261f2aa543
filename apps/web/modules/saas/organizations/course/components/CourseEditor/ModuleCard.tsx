'use client'

import { useState } from 'react'
import { Module } from '../../types'
import { LessonList } from './LessonList'
import { Card, CardContent, CardHeader } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@ui/components/dropdown-menu'
import { ChevronDown, ChevronRight, GripVertical, MoreVertical, Plus, Edit, Trash2 } from 'lucide-react'
import { cn } from '@/modules/ui/lib'

interface ModuleCardProps {
  module: Module & { lessons?: any[] }
  onEdit: () => void
  onDelete: () => void
}

export function ModuleCard({ module, onEdit, onDelete }: ModuleCardProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const lessons = module.lessons || []
  const lessonCount = lessons.length

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          <div className="cursor-grab text-gray-400 hover:text-gray-600">
            <GripVertical className="h-5 w-5" />
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-0 h-auto"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>

          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h3 className="font-semibold text-gray-900">{module.name}</h3>
              <Badge status="info" className="text-xs">
                {lessonCount} {lessonCount === 1 ? 'conteúdo' : 'conteúdos'}
              </Badge>
            </div>
            {module.description && (
              <p className="text-sm text-gray-600 mt-1">{module.description}</p>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="bg-[#36B37E] hover:bg-[#2a8f66] h-8 px-3"
            >
              <Plus className="h-3 w-3 mr-1" />
              Adicionar
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDelete} className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {lessons.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-200">
              <p className="text-gray-500 text-sm mb-3">Nenhuma aula criada ainda</p>
              <Button size="sm" className="bg-[#36B37E] hover:bg-[#2a8f66]">
                <Plus className="h-3 w-3 mr-1" />
                Adicionar Primeira Aula
              </Button>
            </div>
          ) : (
            <LessonList
              lessons={lessons}
              moduleId={module.id}
              onAddLesson={() => {}}
              onEditLesson={() => {}}
              onDeleteLesson={() => {}}
              onReorderLessons={() => {}}
            />
          )}
        </CardContent>
      )}
    </Card>
  )
}
