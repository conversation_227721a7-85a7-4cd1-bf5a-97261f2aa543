'use client'

import { useState, useEffect } from 'react'
import { Module, ModuleFormData } from '../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Textarea } from '@/modules/ui/components/textarea'
import { Label } from '@/modules/ui/components/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Upload, X, Image as ImageIcon } from 'lucide-react'
import { cn } from '@/modules/ui/lib'

interface ModuleFormProps {
  module?: Module | null
  onSave: (data: ModuleFormData) => void
  onCancel: () => void
}

export function ModuleForm({ module, onSave, onCancel }: ModuleFormProps) {
  const [formData, setFormData] = useState<ModuleFormData>({
    name: '',
    description: '',
    coverImage: undefined
  })
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (module) {
      setFormData({
        name: module.name || '',
        description: module.description || '',
        coverImage: undefined
      })
      setCoverImagePreview(module.coverImage || null)
    }
  }, [module])

  const handleInputChange = (field: keyof ModuleFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, coverImage: 'Por favor, selecione uma imagem válida' }))
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, coverImage: 'A imagem deve ter no máximo 5MB' }))
      return
    }

    setFormData(prev => ({ ...prev, coverImage: file }))

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setCoverImagePreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    if (errors.coverImage) {
      setErrors(prev => ({ ...prev, coverImage: '' }))
    }
  }

  const handleRemoveImage = () => {
    setFormData(prev => ({ ...prev, coverImage: undefined }))
    setCoverImagePreview(null)
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Nome do módulo é obrigatório'
    }

    if (formData.name.trim().length < 3) {
      newErrors.name = 'Nome deve ter pelo menos 3 caracteres'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Descrição deve ter no máximo 500 caracteres'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving module:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Module Name */}
      <div className="space-y-2">
        <Label htmlFor="module-name">Nome do Módulo *</Label>
        <Input
          id="module-name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          placeholder="Ex: Introdução ao curso"
          className={cn(errors.name && "border-red-500")}
        />
        {errors.name && (
          <p className="text-sm text-red-600">{errors.name}</p>
        )}
      </div>

      {/* Module Description */}
      <div className="space-y-2">
        <Label htmlFor="module-description">Descrição</Label>
        <Textarea
          id="module-description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Descreva o conteúdo deste módulo..."
          rows={3}
          className={cn(errors.description && "border-red-500")}
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>Opcional</span>
          <span>{formData.description?.length || 0}/500</span>
        </div>
        {errors.description && (
          <p className="text-sm text-red-600">{errors.description}</p>
        )}
      </div>

      {/* Cover Image */}
      <div className="space-y-2">
        <Label>Imagem de Capa</Label>
        <div className="space-y-3">
          {coverImagePreview ? (
            <div className="relative">
              <img
                src={coverImagePreview}
                alt="Preview da capa"
                className="w-full h-32 object-cover rounded-lg border"
              />
              <Button
                type="button"
                variant="error"
                size="sm"
                onClick={handleRemoveImage}
                className="absolute top-2 right-2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                id="cover-upload"
              />
              <label htmlFor="cover-upload" className="cursor-pointer">
                <div className="flex flex-col items-center gap-2">
                  <div className="p-2 bg-gray-100 rounded-full">
                    <ImageIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Clique para fazer upload</p>
                    <p className="text-xs text-gray-500">PNG, JPG até 5MB (400x300px recomendado)</p>
                  </div>
                </div>
              </label>
            </div>
          )}
        </div>
        {errors.coverImage && (
          <p className="text-sm text-red-600">{errors.coverImage}</p>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#36B37E] hover:bg-[#2a8f66]"
        >
          {isLoading ? 'Salvando...' : module ? 'Atualizar' : 'Criar Módulo'}
        </Button>
      </div>
    </form>
  )
}
