'use client'

import { useState } from 'react'
import { ModuleListProps, Module } from '../../types'
import { ModuleCard } from './ModuleCard'
import { ModuleForm } from './ModuleForm'
import { Button } from '@/modules/ui/components/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/modules/ui/components/dialog'
import { Plus } from 'lucide-react'

export function ModuleList({ 
  modules, 
  onAddModule, 
  onEditModule, 
  onDeleteModule, 
  onReorderModules 
}: ModuleListProps) {
  const [showModuleForm, setShowModuleForm] = useState(false)
  const [editingModule, setEditingModule] = useState<Module | null>(null)
  const [draggedModule, setDraggedModule] = useState<string | null>(null)

  const handleAddModule = () => {
    setEditingModule(null)
    setShowModuleForm(true)
  }

  const handleEditModule = (module: Module) => {
    setEditingModule(module)
    setShowModuleForm(true)
  }

  const handleSaveModule = (moduleData: any) => {
    if (editingModule) {
      onEditModule({ ...editingModule, ...moduleData })
    } else {
      onAddModule()
    }
    setShowModuleForm(false)
    setEditingModule(null)
  }

  const handleDragStart = (moduleId: string) => {
    setDraggedModule(moduleId)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, targetModuleId: string) => {
    e.preventDefault()
    
    if (!draggedModule || draggedModule === targetModuleId) {
      setDraggedModule(null)
      return
    }

    const draggedIndex = modules.findIndex(m => m.id === draggedModule)
    const targetIndex = modules.findIndex(m => m.id === targetModuleId)
    
    if (draggedIndex === -1 || targetIndex === -1) return

    const newModules = [...modules]
    const [draggedItem] = newModules.splice(draggedIndex, 1)
    newModules.splice(targetIndex, 0, draggedItem)
    
    const reorderedModules = newModules.map((module, index) => ({
      ...module,
      position: index + 1
    }))
    
    onReorderModules(reorderedModules)
    setDraggedModule(null)
  }

  const sortedModules = [...modules].sort((a, b) => a.position - b.position)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Módulos do Curso</h2>
        <Button
          onClick={handleAddModule}
          className="bg-[#36B37E] hover:bg-[#2a8f66] flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Adicionar Módulo
        </Button>
      </div>
      
      {sortedModules.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-dashed border-gray-300">
          <p className="text-gray-500 mb-4">Nenhum módulo criado ainda</p>
          <Button onClick={handleAddModule} className="bg-[#36B37E] hover:bg-[#2a8f66]">
            <Plus className="h-4 w-4 mr-2" />
            Criar Primeiro Módulo
          </Button>
        </div>
      ) : (
        <div className="space-y-3">
          {sortedModules.map((module) => (
            <div
              key={module.id}
              draggable
              onDragStart={() => handleDragStart(module.id)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, module.id)}
              className={`transition-opacity ${
                draggedModule === module.id ? 'opacity-50' : 'opacity-100'
              }`}
            >
              <ModuleCard
                module={module}
                onEdit={() => handleEditModule(module)}
                onDelete={() => onDeleteModule(module.id)}
              />
            </div>
          ))}
        </div>
      )}
      
      <Dialog open={showModuleForm} onOpenChange={setShowModuleForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingModule ? 'Editar Módulo' : 'Novo Módulo'}
            </DialogTitle>
          </DialogHeader>
          
          <ModuleForm
            module={editingModule}
            onSave={handleSaveModule}
            onCancel={() => {
              setShowModuleForm(false)
              setEditingModule(null)
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}