import { Skeleton } from '@/modules/ui/components/skeleton'
import { <PERSON>, CardContent, CardHeader } from '@/modules/ui/components/card'

export function CoursePageSkeleton() {
  return (
    <div className="flex h-screen bg-background">
      {/* Main Content Skeleton */}
      <div className="flex-1 flex flex-col">
        {/* Header Skeleton */}
        <div className="flex-shrink-0 border-b p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-6 w-48" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-8" />
            </div>
          </div>
        </div>

        {/* Video Player Skeleton */}
        <div className="p-4 md:p-6 pb-0">
          <Skeleton className="aspect-video w-full rounded-lg" />
        </div>

        {/* Content Area Skeleton */}
        <div className="flex-1 overflow-y-auto p-4 md:p-6 pt-4">
          <div className="space-y-6">
            {/* Lesson Info Skeleton */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-8 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Tabs Skeleton */}
            <div className="space-y-4">
              <div className="flex space-x-1 rounded-lg bg-muted p-1">
                <Skeleton className="h-9 flex-1" />
                <Skeleton className="h-9 flex-1" />
              </div>

              {/* Content Skeleton */}
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-3 w-16" />
                          </div>
                          <Skeleton className="h-16 w-full" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar Skeleton - Right Side */}
      <div className="w-80 border-l bg-card">
        <div className="p-4 border-b">
          <Skeleton className="h-6 w-3/4 mb-3" />
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-3 w-8" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
        </div>

        <div className="p-2 space-y-2">
          {/* Module Skeletons */}
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-12 w-full" />
              <div className="ml-6 space-y-1">
                {Array.from({ length: 3 }).map((_, j) => (
                  <Skeleton key={j} className="h-10 w-full" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export function CourseErrorState({
  title = "Curso não encontrado",
  description = "O curso que você está procurando não existe ou você não tem acesso a ele.",
  onRetry
}: {
  title?: string
  description?: string
  onRetry?: () => void
} = {}) {
  return (
    <div className="flex items-center justify-center h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
            <div className="w-6 h-6 rounded-full bg-destructive/20" />
          </div>
          <h2 className="text-2xl font-bold mb-2">{title}</h2>
          <p className="text-muted-foreground">{description}</p>
        </CardHeader>
        {onRetry && (
          <CardContent className="text-center">
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Tentar novamente
            </button>
          </CardContent>
        )}
      </Card>
    </div>
  )
}

export function VideoPlayerSkeleton() {
  return (
    <div className="aspect-video w-full bg-black rounded-lg overflow-hidden">
      <div className="w-full h-full bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-600 border-t-white rounded-full animate-spin mx-auto mb-4" />
          <p className="text-white text-sm">Carregando vídeo...</p>
        </div>
      </div>
    </div>
  )
}

export function SidebarSkeleton() {
  return (
    <div className="w-80 border-r bg-card h-full">
      <div className="p-4 border-b">
        <Skeleton className="h-6 w-3/4 mb-3" />
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-8" />
          </div>
          <Skeleton className="h-2 w-full" />
        </div>
      </div>

      <div className="p-2 space-y-2 overflow-y-auto">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-12 w-full" />
            <div className="ml-6 space-y-1">
              {Array.from({ length: Math.floor(Math.random() * 4) + 2 }).map((_, j) => (
                <Skeleton key={j} className="h-10 w-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function LessonContentSkeleton() {
  return (
    <div className="space-y-6">
      {/* Lesson Info Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2 flex-1">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="flex gap-2 ml-4">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-12" />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Tabs Skeleton */}
      <div className="space-y-4">
        <div className="flex space-x-1 rounded-lg bg-muted p-1">
          <Skeleton className="h-9 flex-1" />
          <Skeleton className="h-9 flex-1" />
        </div>

        {/* Comments/Files Skeleton */}
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                    <Skeleton className="h-16 w-full" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
