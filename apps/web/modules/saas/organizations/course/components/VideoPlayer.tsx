'use client'

import { useRef, useEffect, useState } from 'react'
import { Button } from '@/modules/ui/components/button'
import {
  Play,
  Loader2,
  AlertCircle,
  ExternalLink
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { detectVideoProvider, VideoProvider } from '../lib/video-utils'
import { useWatchLesson } from '../hooks/useCourses'

interface VideoPlayerProps {
  videoUrl?: string
  lessonId?: string
  lessonTitle?: string
  onNext?: () => void
  onPrevious?: () => void
  className?: string
}

export function VideoPlayer({
  videoUrl,
  lessonId,
  lessonTitle,
  onNext,
  onPrevious,
  className
}: VideoPlayerProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const watchLessonMutation = useWatchLesson()

  // Detecta o tipo de provedor de vídeo
  const videoProvider: VideoProvider = videoUrl ? detectVideoProvider(videoUrl) : { type: 'unknown', url: '' }

  // Gera URL otimizada baseada no provedor
  const optimizedVideoUrl = videoProvider.embedUrl || videoUrl

  // Handle iframe load events
  useEffect(() => {
    const iframe = iframeRef.current
    if (!iframe) return

    const handleLoad = () => {
      setIsLoading(false)
      setHasError(false)
    }

    const handleError = () => {
      setIsLoading(false)
      setHasError(true)
    }

    iframe.addEventListener('load', handleLoad)
    iframe.addEventListener('error', handleError)

    // Timeout para casos onde o load event não dispara
    const loadTimeout = setTimeout(() => {
      setIsLoading(false)
    }, 5000)

    return () => {
      iframe.removeEventListener('load', handleLoad)
      iframe.removeEventListener('error', handleError)
      clearTimeout(loadTimeout)
    }
  }, [optimizedVideoUrl])

  // Track lesson progress (simplified for Bunny.net)
  useEffect(() => {
    if (lessonId && !isLoading && !hasError) {
      // Mark lesson as accessed
      const timer = setTimeout(() => {
        watchLessonMutation.mutate({
          lessonId,
          data: {
            lessonId,
            currentTime: '0',
            isCompleted: false,
          }
        })
      }, 5000) // After 5 seconds of viewing

      return () => clearTimeout(timer)
    }
  }, [lessonId, isLoading, hasError, watchLessonMutation])

  // Handle navigation shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle if not focused on an input
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      switch (e.code) {
        case 'ArrowLeft':
          if (onPrevious) {
            e.preventDefault()
            onPrevious()
          }
          break
        case 'ArrowRight':
          if (onNext) {
            e.preventDefault()
            onNext()
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onNext, onPrevious])

  // No video URL provided
  if (!videoUrl) {
    return (
      <div className={cn(
        "relative bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg overflow-hidden w-full h-full flex items-center justify-center",
        className
      )}>
        <div className="text-center text-gray-400 p-8">
          <Play className="h-16 w-16 mx-auto mb-4 opacity-50" />
          <h3 className="text-xl font-medium mb-2">Selecione uma aula para começar</h3>
          <p className="text-sm opacity-75">
            Escolha uma aula na barra lateral para iniciar seus estudos
          </p>
        </div>
      </div>
    )
  }

  // Error state
  if (hasError) {
    return (
      <div className={cn(
        "relative bg-gradient-to-br from-red-900/20 to-gray-900 rounded-lg overflow-hidden w-full h-full flex items-center justify-center",
        className
      )}>
        <div className="text-center text-gray-300 p-8">
          <AlertCircle className="h-16 w-16 mx-auto mb-4 text-red-400" />
          <h3 className="text-xl font-medium mb-2">Erro ao carregar o vídeo</h3>
          <p className="text-sm opacity-75 mb-4">
            Não foi possível carregar o vídeo. Verifique sua conexão com a internet.
          </p>
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setHasError(false)
                setIsLoading(true)
                window.location.reload()
              }}
            >
              Tentar novamente
            </Button>
            {videoProvider.type === 'bunny' && videoProvider.config && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const directUrl = `https://iframe.mediadelivery.net/embed/${videoProvider.config.libraryId}/${videoProvider.config.videoId}`
                  window.open(directUrl, '_blank')
                }}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Abrir em nova aba
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative bg-black rounded-lg overflow-hidden w-full h-full",
        className
      )}
    >
      {/* Multi-Provider Video Player - Suporta Bunny.net, YouTube, Vimeo */}
      <iframe
        ref={iframeRef}
        src={optimizedVideoUrl}
        className="w-full h-full"
        frameBorder="0"
        loading="lazy"
        allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture; web-share"
        allowFullScreen
        title={lessonTitle || 'Vídeo da aula'}
        style={{
          border: 'none',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%'
        }}
      />

      {/* Loading Overlay - Apenas enquanto carrega */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-black/80 z-10"
          >
            <div className="text-center text-white">
              <Loader2 className="h-12 w-12 mx-auto mb-4 animate-spin" />
              <p className="text-lg font-medium">Carregando vídeo...</p>
              <p className="text-sm opacity-75 mt-1">
                {videoProvider.type === 'bunny' ? 'Conectando com Bunny.net' :
                 videoProvider.type === 'youtube' ? 'Conectando com YouTube' :
                 videoProvider.type === 'vimeo' ? 'Conectando com Vimeo' : 'Preparando reprodução'}
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
