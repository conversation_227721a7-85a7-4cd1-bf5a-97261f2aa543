'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { LessonComment, LessonFile, WatchLessonRequest } from '../types'
import { api } from '../lib/api'

// Query keys for course-related data
export const courseKeys = {
  all: ['courses'] as const,
  course: (courseId: string, organizationSlug?: string) => organizationSlug
    ? [...courseKeys.all, courseId, organizationSlug] as const
    : [...courseKeys.all, courseId] as const,
  modules: (courseId: string) => [...courseKeys.all, courseId, 'modules'] as const,
  lessons: (courseId: string) => [...courseKeys.all, courseId, 'lessons'] as const,
  lessonComments: (lessonId: string) => ['lesson-comments', lessonId] as const,
  lessonFiles: (lessonId: string) => ['lesson-files', lessonId] as const,
}

// Hook for lesson comments
export function useLessonComments(lessonId: string) {
  return useQuery({
    queryKey: courseKeys.lessonComments(lessonId),
    queryFn: () => api.getLessonComments(lessonId),
    enabled: !!lessonId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Hook for lesson files
export function useLessonFiles(lessonId: string) {
  return useQuery({
    queryKey: courseKeys.lessonFiles(lessonId),
    queryFn: () => api.getLessonFiles(lessonId),
    enabled: !!lessonId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Hook for marking lesson as watched
export function useWatchLesson() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: WatchLessonRequest) => api.watchLesson(request),
    onSuccess: (data, variables) => {
      // Invalidate and refetch lesson data
      queryClient.invalidateQueries({
        queryKey: courseKeys.lessons(variables.data.lessonId)
      })
      // Also invalidate course data to update progress
      queryClient.invalidateQueries({
        queryKey: courseKeys.all
      })
    },
    onError: (error) => {
      console.error('Erro ao marcar aula como assistida:', error)
    }
  })
}

// Hook for toggling lesson completion
export function useToggleLessonCompletion() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ lessonId, isCompleted }: { lessonId: string; isCompleted: boolean }) =>
      api.toggleLessonCompletion(lessonId, isCompleted),
    onSuccess: (data, variables) => {
      // Invalidate course data to update lesson completion status
      queryClient.invalidateQueries({
        queryKey: courseKeys.all
      })
    },
    onError: (error) => {
      console.error('Erro ao alterar status da aula:', error)
    }
  })
}

// Hook for rating lesson
export function useRateLesson() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ lessonId, rating }: { lessonId: string; rating: number }) =>
      api.rateLesson(lessonId, rating),
    onSuccess: (data, variables) => {
      // Invalidate course data to update lesson rating
      queryClient.invalidateQueries({
        queryKey: courseKeys.all
      })
    },
    onError: (error) => {
      console.error('Erro ao avaliar aula:', error)
    }
  })
}

// Hook for adding lesson comment
export function useAddLessonComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ lessonId, comment }: { lessonId: string; comment: string }) =>
      api.addLessonComment(lessonId, comment),
    onSuccess: (data, variables) => {
      // Invalidate comments for this lesson
      queryClient.invalidateQueries({
        queryKey: courseKeys.lessonComments(variables.lessonId)
      })
    },
    onError: (error) => {
      console.error('Erro ao adicionar comentário:', error)
    }
  })
}

// Hook for replying to lesson comment
export function useReplyLessonComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      lessonId,
      commentId,
      reply
    }: {
      lessonId: string
      commentId: number
      reply: string
    }) => api.replyLessonComment(lessonId, commentId, reply),
    onSuccess: (data, variables) => {
      // Invalidate comments for this lesson
      queryClient.invalidateQueries({
        queryKey: courseKeys.lessonComments(variables.lessonId)
      })
    },
    onError: (error) => {
      console.error('Erro ao responder comentário:', error)
    }
  })
}

// Hook for downloading lesson file
export function useDownloadLessonFile() {
  return useMutation({
    mutationFn: ({ lessonId, fileId }: { lessonId: string; fileId: number }) =>
      api.downloadLessonFile(lessonId, fileId),
    onError: (error) => {
      console.error('Erro ao baixar arquivo:', error)
    }
  })
}
