'use client'

import { useState } from 'react'
import { SmartSuggestion } from '../types'

interface SuggestionRequest {
  type: 'module' | 'lesson' | 'description' | 'tags'
  context: {
    name?: string
    category?: string
    difficulty?: string
    existingModules?: string[]
    existingLessons?: string[]
  }
}

export function useSmartSuggestions() {
  const [isLoading, setIsLoading] = useState(false)

  const getSuggestions = async (request: SuggestionRequest): Promise<SmartSuggestion[]> => {
    setIsLoading(true)

    try {
      // Simulate API call - replace with actual AI service
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Sugestões baseadas no tipo e contexto - implementação da IA será adicionada posteriormente
      const suggestions = generateMockSuggestions(request)

      return suggestions
    } catch (error) {
      console.error('Erro ao obter sugestões:', error)
      return []
    } finally {
      setIsLoading(false)
    }
  }

  return {
    getSuggestions,
    isLoading,
  }
}

function generateMockSuggestions(request: SuggestionRequest): SmartSuggestion[] {
  const { type, context } = request

  switch (type) {
    case 'description':
      return generateDescriptionSuggestions(context)
    case 'module':
      return generateModuleSuggestions(context)
    case 'lesson':
      return generateLessonSuggestions(context)
    case 'tags':
      return generateTagSuggestions(context)
    default:
      return []
  }
}

function generateDescriptionSuggestions(context: any): SmartSuggestion[] {
  const { name, category, difficulty } = context

  const templates = {
    'Programação': {
      beginner: `Aprenda ${name} do zero! Este curso foi desenvolvido para iniciantes que desejam dominar os fundamentos da programação. Com aulas práticas e projetos reais, você sairá preparado para criar suas próprias aplicações.`,
      intermediate: `Aprofunde seus conhecimentos em ${name}. Este curso intermediário aborda conceitos avançados e melhores práticas, preparando você para desafios mais complexos no desenvolvimento.`,
      advanced: `Domine ${name} como um expert. Este curso avançado explora técnicas sofisticadas, padrões de arquitetura e otimizações que farão você se destacar no mercado.`
    },
    'Design': {
      beginner: `Descubra o mundo do design com ${name}. Um curso completo para iniciantes que querem aprender os princípios fundamentais e criar designs incríveis desde o primeiro dia.`,
      intermediate: `Eleve suas habilidades de design com ${name}. Explore técnicas avançadas, tendências atuais e desenvolva um portfólio profissional que impressiona.`,
      advanced: `Torne-se um designer expert em ${name}. Domine as técnicas mais sofisticadas e aprenda a criar designs que não apenas impressionam, mas também convertem.`
    }
  }

  const template = templates[category as keyof typeof templates]?.[difficulty as keyof typeof templates['Programação']] ||
    `Aprenda ${name} de forma prática e eficiente. Este curso foi cuidadosamente estruturado para proporcionar uma experiência de aprendizado completa e envolvente.`

  return [
    {
      type: 'description',
      content: template,
      confidence: 0.9
    }
  ]
}

function generateModuleSuggestions(context: any): SmartSuggestion[] {
  const { category, difficulty } = context

  const moduleTemplates = {
    'Programação': {
      beginner: [
        'Introdução e Configuração',
        'Fundamentos da Linguagem',
        'Estruturas de Dados',
        'Programação Orientada a Objetos',
        'Projeto Prático'
      ],
      intermediate: [
        'Conceitos Avançados',
        'Padrões de Design',
        'Testes e Debugging',
        'Performance e Otimização',
        'Projeto Complexo'
      ],
      advanced: [
        'Arquitetura de Software',
        'Microserviços',
        'DevOps e Deploy',
        'Segurança',
        'Projeto Enterprise'
      ]
    },
    'Design': {
      beginner: [
        'Fundamentos do Design',
        'Teoria das Cores',
        'Tipografia',
        'Layout e Composição',
        'Projeto Final'
      ],
      intermediate: [
        'Design Systems',
        'UX/UI Avançado',
        'Prototipagem',
        'Design Responsivo',
        'Portfolio Profissional'
      ],
      advanced: [
        'Design Estratégico',
        'Research e Testes',
        'Design Thinking',
        'Liderança em Design',
        'Consultoria em Design'
      ]
    }
  }

  const modules = moduleTemplates[category as keyof typeof moduleTemplates]?.[difficulty as keyof typeof moduleTemplates['Programação']] ||
    ['Introdução', 'Conceitos Básicos', 'Prática', 'Projeto', 'Conclusão']

  return modules.map((module, index) => ({
    type: 'module' as const,
    content: module,
    confidence: 0.8 - (index * 0.1)
  }))
}

function generateLessonSuggestions(context: any): SmartSuggestion[] {
  // Sugestões de aulas baseadas no contexto do módulo - implementação da IA será adicionada posteriormente
  const lessons = [
    'Introdução ao tópico',
    'Conceitos fundamentais',
    'Exemplos práticos',
    'Exercícios guiados',
    'Projeto hands-on',
    'Revisão e próximos passos'
  ]

  return lessons.map((lesson, index) => ({
    type: 'lesson' as const,
    content: lesson,
    confidence: 0.7 - (index * 0.05)
  }))
}

function generateTagSuggestions(context: any): SmartSuggestion[] {
  const { category } = context

  const tagTemplates = {
    'Programação': ['javascript', 'react', 'nodejs', 'frontend', 'backend', 'fullstack', 'api', 'database'],
    'Design': ['ui', 'ux', 'figma', 'photoshop', 'branding', 'logo', 'web-design', 'mobile'],
    'Marketing': ['digital', 'seo', 'social-media', 'ads', 'analytics', 'conversion', 'email-marketing'],
    'Negócios': ['empreendedorismo', 'gestão', 'liderança', 'vendas', 'estratégia', 'inovação']
  }

  const tags = tagTemplates[category as keyof typeof tagTemplates] || ['curso', 'online', 'prático']

  return tags.map((tag, index) => ({
    type: 'tags' as const,
    content: tag,
    confidence: 0.8 - (index * 0.05)
  }))
}
