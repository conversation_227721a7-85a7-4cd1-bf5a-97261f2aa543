export interface BunnyVideoConfig {
  libraryId: string
  videoId: string
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  preload?: boolean
  startTime?: string | number
  captions?: string
  showHeatmap?: boolean
  showSpeed?: boolean
  rememberPosition?: boolean
  responsive?: boolean
}

export function parseBunnyUrl(url: string): { libraryId: string; videoId: string } | null {
  const embedMatch = url.match(/iframe\.mediadelivery\.net\/embed\/([^\/]+)\/([^\/\?]+)/)

  if (embedMatch) {
    return {
      libraryId: embedMatch[1],
      videoId: embedMatch[2]
    }
  }

  const directMatch = url.match(/bunnycdn\.com.*\/([^\/]+)\/([^\/\?]+)/)

  if (directMatch) {
    return {
      libraryId: directMatch[1],
      videoId: directMatch[2]
    }
  }

  return null
}

export function generateBunnyEmbedUrl(config: BunnyVideoConfig): string {
  const { libraryId, videoId, ...params } = config

  const baseUrl = `https://iframe.mediadelivery.net/embed/${libraryId}/${videoId}`
  const searchParams = new URLSearchParams()

  if (params.autoplay !== undefined) {
    searchParams.set('autoplay', params.autoplay.toString())
  }

  if (params.loop !== undefined) {
    searchParams.set('loop', params.loop.toString())
  }

  if (params.muted !== undefined) {
    searchParams.set('muted', params.muted.toString())
  }

  if (params.preload !== undefined) {
    searchParams.set('preload', params.preload.toString())
  }

  if (params.startTime !== undefined) {
    const timeParam = typeof params.startTime === 'number'
      ? `${params.startTime}s`
      : params.startTime
    searchParams.set('t', timeParam)
  }

  if (params.captions) {
    searchParams.set('captions', params.captions)
  }

  if (params.showHeatmap !== undefined) {
    searchParams.set('showHeatmap', params.showHeatmap.toString())
  }

  if (params.showSpeed !== undefined) {
    searchParams.set('showSpeed', params.showSpeed.toString())
  }

  if (params.rememberPosition !== undefined) {
    searchParams.set('rememberPosition', params.rememberPosition.toString())
  }

  if (params.responsive !== undefined) {
    searchParams.set('responsive', params.responsive.toString())
  }

  const queryString = searchParams.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

export function isBunnyUrl(url: string): boolean {
  return url.includes('iframe.mediadelivery.net/embed') ||
         url.includes('bunnycdn.com') ||
         url.includes('b-cdn.net')
}

export function generateBunnyEmbedCode(config: BunnyVideoConfig): string {
  const embedUrl = generateBunnyEmbedUrl(config)

  return `<div style="position: relative; padding-top: 56.25%;">
  <iframe
    src="${embedUrl}"
    loading="lazy"
    style="border: none; position: absolute; top: 0; height: 100%; width: 100%;"
    allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
    allowfullscreen="true">
  </iframe>
</div>`
}

export function formatTimeForBunny(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}h${minutes}m${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m${secs}s`
  } else {
    return `${secs}s`
  }
}
