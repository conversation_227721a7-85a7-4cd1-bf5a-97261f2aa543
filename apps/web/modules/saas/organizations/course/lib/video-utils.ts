export interface VideoProvider {
  type: 'bunny' | 'youtube' | 'vimeo' | 'unknown'
  url: string
  embedUrl?: string
  thumbnailUrl?: string
  config?: any
}

export interface BunnyVideoConfig {
  libraryId: string
  videoId: string
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  preload?: boolean
  startTime?: string | number
  captions?: string
  showHeatmap?: boolean
  showSpeed?: boolean
  rememberPosition?: boolean
  responsive?: boolean
}

export interface YouTubeVideoConfig {
  videoId: string
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  startTime?: number
  showControls?: boolean
  modestbranding?: boolean
  rel?: boolean
}

export interface VimeoVideoConfig {
  videoId: string
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  startTime?: number
  showTitle?: boolean
  showPortrait?: boolean
  showByline?: boolean
}

/**
 * Detecta o tipo de provedor de vídeo baseado na URL
 */
export function detectVideoProvider(url: string): VideoProvider {
  if (!url) {
    return { type: 'unknown', url }
  }

  // Bunny.net
  if (isBunnyUrl(url)) {
    const config = parseBunnyUrl(url)
    return {
      type: 'bunny',
      url,
      embedUrl: config ? generateBunnyEmbedUrl(config) : url,
      config
    }
  }

  // YouTube
  if (isYouTubeUrl(url)) {
    const config = parseYouTubeUrl(url)
    return {
      type: 'youtube',
      url,
      embedUrl: config ? generateYouTubeEmbedUrl(config) : url,
      thumbnailUrl: config ? `https://img.youtube.com/vi/${config.videoId}/maxresdefault.jpg` : undefined,
      config
    }
  }

  // Vimeo
  if (isVimeoUrl(url)) {
    const config = parseVimeoUrl(url)
    return {
      type: 'vimeo',
      url,
      embedUrl: config ? generateVimeoEmbedUrl(config) : url,
      config
    }
  }

  return { type: 'unknown', url }
}

/**
 * Bunny.net Functions
 */
export function isBunnyUrl(url: string): boolean {
  return url.includes('iframe.mediadelivery.net/embed') ||
         url.includes('bunnycdn.com') ||
         url.includes('b-cdn.net')
}

export function parseBunnyUrl(url: string): BunnyVideoConfig | null {
  const embedMatch = url.match(/iframe\.mediadelivery\.net\/embed\/([^\/]+)\/([^\/\?]+)/)

  if (embedMatch) {
    return {
      libraryId: embedMatch[1],
      videoId: embedMatch[2]
    }
  }

  const directMatch = url.match(/bunnycdn\.com.*\/([^\/]+)\/([^\/\?]+)/)

  if (directMatch) {
    return {
      libraryId: directMatch[1],
      videoId: directMatch[2]
    }
  }

  return null
}

export function generateBunnyEmbedUrl(config: BunnyVideoConfig): string {
  const { libraryId, videoId, ...params } = config

  const baseUrl = `https://iframe.mediadelivery.net/embed/${libraryId}/${videoId}`
  const searchParams = new URLSearchParams()

  if (params.autoplay !== undefined) {
    searchParams.set('autoplay', params.autoplay.toString())
  }

  if (params.loop !== undefined) {
    searchParams.set('loop', params.loop.toString())
  }

  if (params.muted !== undefined) {
    searchParams.set('muted', params.muted.toString())
  }

  if (params.preload !== undefined) {
    searchParams.set('preload', params.preload.toString())
  }

  if (params.startTime !== undefined) {
    const timeParam = typeof params.startTime === 'number'
      ? `${params.startTime}s`
      : params.startTime
    searchParams.set('t', timeParam)
  }

  if (params.captions) {
    searchParams.set('captions', params.captions)
  }

  if (params.showHeatmap !== undefined) {
    searchParams.set('showHeatmap', params.showHeatmap.toString())
  }

  if (params.showSpeed !== undefined) {
    searchParams.set('showSpeed', params.showSpeed.toString())
  }

  if (params.rememberPosition !== undefined) {
    searchParams.set('rememberPosition', params.rememberPosition.toString())
  }

  if (params.responsive !== undefined) {
    searchParams.set('responsive', params.responsive.toString())
  }

  const queryString = searchParams.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

/**
 * YouTube Functions
 */
export function isYouTubeUrl(url: string): boolean {
  return url.includes('youtube.com') ||
         url.includes('youtu.be') ||
         url.includes('youtube-nocookie.com')
}

export function parseYouTubeUrl(url: string): YouTubeVideoConfig | null {
  // youtube.com/watch?v=VIDEO_ID
  const watchMatch = url.match(/youtube\.com\/watch\?v=([^&]+)/)
  if (watchMatch) {
    return { videoId: watchMatch[1] }
  }

  // youtu.be/VIDEO_ID
  const shortMatch = url.match(/youtu\.be\/([^?]+)/)
  if (shortMatch) {
    return { videoId: shortMatch[1] }
  }

  // youtube.com/embed/VIDEO_ID
  const embedMatch = url.match(/youtube\.com\/embed\/([^?]+)/)
  if (embedMatch) {
    return { videoId: embedMatch[1] }
  }

  return null
}

export function generateYouTubeEmbedUrl(config: YouTubeVideoConfig): string {
  const { videoId, ...params } = config
  const baseUrl = `https://www.youtube.com/embed/${videoId}`
  const searchParams = new URLSearchParams()

  if (params.autoplay !== undefined) {
    searchParams.set('autoplay', params.autoplay ? '1' : '0')
  }

  if (params.loop !== undefined) {
    searchParams.set('loop', params.loop ? '1' : '0')
  }

  if (params.muted !== undefined) {
    searchParams.set('muted', params.muted ? '1' : '0')
  }

  if (params.startTime !== undefined) {
    searchParams.set('start', params.startTime.toString())
  }

  if (params.showControls !== undefined) {
    searchParams.set('controls', params.showControls ? '1' : '0')
  }

  if (params.modestbranding !== undefined) {
    searchParams.set('modestbranding', params.modestbranding ? '1' : '0')
  }

  if (params.rel !== undefined) {
    searchParams.set('rel', params.rel ? '1' : '0')
  }

  const queryString = searchParams.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

/**
 * Vimeo Functions
 */
export function isVimeoUrl(url: string): boolean {
  return url.includes('vimeo.com') || url.includes('player.vimeo.com')
}

export function parseVimeoUrl(url: string): VimeoVideoConfig | null {
  // vimeo.com/VIDEO_ID
  const vimeoMatch = url.match(/vimeo\.com\/(\d+)/)
  if (vimeoMatch) {
    return { videoId: vimeoMatch[1] }
  }

  // player.vimeo.com/video/VIDEO_ID
  const playerMatch = url.match(/player\.vimeo\.com\/video\/(\d+)/)
  if (playerMatch) {
    return { videoId: playerMatch[1] }
  }

  return null
}

export function generateVimeoEmbedUrl(config: VimeoVideoConfig): string {
  const { videoId, ...params } = config
  const baseUrl = `https://player.vimeo.com/video/${videoId}`
  const searchParams = new URLSearchParams()

  if (params.autoplay !== undefined) {
    searchParams.set('autoplay', params.autoplay ? '1' : '0')
  }

  if (params.loop !== undefined) {
    searchParams.set('loop', params.loop ? '1' : '0')
  }

  if (params.muted !== undefined) {
    searchParams.set('muted', params.muted ? '1' : '0')
  }

  if (params.startTime !== undefined) {
    searchParams.set('t', `${params.startTime}s`)
  }

  if (params.showTitle !== undefined) {
    searchParams.set('title', params.showTitle ? '1' : '0')
  }

  if (params.showPortrait !== undefined) {
    searchParams.set('portrait', params.showPortrait ? '1' : '0')
  }

  if (params.showByline !== undefined) {
    searchParams.set('byline', params.showByline ? '1' : '0')
  }

  const queryString = searchParams.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

/**
 * Utility Functions
 */
export function formatTimeForVideo(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

export function generateVideoEmbedCode(provider: VideoProvider): string {
  if (!provider.embedUrl) {
    return ''
  }

  const baseEmbed = `<div style="position: relative; padding-top: 56.25%;">
  <iframe
    src="${provider.embedUrl}"
    loading="lazy"
    style="border: none; position: absolute; top: 0; height: 100%; width: 100%;"
    allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
    allowfullscreen="true">
  </iframe>
</div>`

  return baseEmbed
}
