import { Course, Module, Lesson, LessonComment, LessonFile, WatchLessonRequest, ApiResponse } from '../types'

export class CourseAPI {
  private baseUrl: string
  private headers: HeadersInit

  constructor() {
    const backendUrl = process.env.NEXT_PUBLIC_CAKTO_BACKEND_URL || 'https://api.cakto.com.br'
    // If running locally, append /api to the base URL
    this.baseUrl = backendUrl.includes('localhost') ? `${backendUrl}/api` : backendUrl
    this.headers = {
      'Content-Type': 'application/json',
      // Add authentication headers here
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    const response = await fetch(url, {
      ...options,
      credentials: 'include', // Include cookies for authentication
      headers: {
        ...this.headers,
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }


  async getCourse(courseId: string, organizationSlug: string): Promise<Course & { modules: (Module & { lessons: Lesson[] })[] }> {
    const response = await this.request<{ course: Course & { modules: (Module & { lessons: Lesson[] })[] } }>(`/courses/course/${courseId}?organizationSlug=${organizationSlug}`)
    return response.course
  }


  async getCourseModules(courseId: string): Promise<Module[]> {
    const response = await this.request<ApiResponse<Module[]>>(`/courses/${courseId}/modules`)
    return response.data || []
  }


  async getCourseLessons(courseId: string): Promise<Lesson[]> {
    const response = await this.request<ApiResponse<Lesson[]>>(`/courses/${courseId}/lessons`)
    return response.data || []
  }


  async getModuleLessons(moduleId: string): Promise<Lesson[]> {
    const response = await this.request<ApiResponse<Lesson[]>>(`/modules/${moduleId}/lessons`)
    return response.data || []
  }


  async getLessonComments(lessonId: string): Promise<LessonComment[]> {
    const response = await this.request<ApiResponse<LessonComment[]>>(`/lessons/${lessonId}/comments`)
    return response.data || []
  }


  async getLessonFiles(lessonId: string): Promise<LessonFile[]> {
    const response = await this.request<ApiResponse<LessonFile[]>>(`/lessons/${lessonId}/files`)
    return response.data || []
  }


  async watchLesson(request: WatchLessonRequest): Promise<void> {
    await this.request(`/lessons/${request.lessonId}/watch`, {
      method: 'POST',
      body: JSON.stringify(request.data),
    })
  }

  async toggleLessonCompletion(lessonId: string, isCompleted: boolean): Promise<void> {
    await this.request(`/lessons/${lessonId}/completion`, {
      method: 'POST',
      body: JSON.stringify({ isCompleted }),
    })
  }

  async rateLesson(lessonId: string, rating: number): Promise<void> {
    await this.request(`/lessons/${lessonId}/rating`, {
      method: 'POST',
      body: JSON.stringify({ rating }),
    })
  }


  async addLessonComment(lessonId: string, comment: string): Promise<LessonComment> {
    const response = await this.request<ApiResponse<LessonComment>>(`/lessons/${lessonId}/comments`, {
      method: 'POST',
      body: JSON.stringify({ comment }),
    })
    return response.data!
  }


  async replyLessonComment(lessonId: string, commentId: number, reply: string): Promise<void> {
    await this.request(`/lessons/${lessonId}/comments/${commentId}/replies`, {
      method: 'POST',
      body: JSON.stringify({ reply }),
    })
  }


  async downloadLessonFile(lessonId: string, fileId: number): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/lessons/${lessonId}/files/${fileId}/download`, {
      method: 'GET',
      credentials: 'include', // Include cookies for authentication
    })

    if (!response.ok) {
      throw new Error(`Download Error: ${response.status} ${response.statusText}`)
    }

    return response.blob()
  }


  async updateCourse(courseId: string, data: Partial<Course>): Promise<Course> {
    return this.request<Course>(`/members/v2/${courseId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async createCourse(data: Partial<Course>): Promise<Course> {
    return this.request<Course>('/members/v2/', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async deleteCourse(courseId: string): Promise<void> {
    return this.request<void>(`/members/v2/${courseId}/`, {
      method: 'DELETE',
    })
  }


  async createModule(courseId: string, data: Partial<Module>): Promise<Module> {
    return this.request<Module>(`/members/v2/${courseId}/modules/`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateModule(moduleId: string, data: Partial<Module>): Promise<Module> {
    return this.request<Module>(`/members/v2/modules/${moduleId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteModule(moduleId: string): Promise<void> {
    return this.request<void>(`/members/v2/modules/${moduleId}/`, {
      method: 'DELETE',
    })
  }

  async reorderModules(courseId: string, modules: Module[]): Promise<void> {
    const reorderData = modules.map((module, index) => ({
      id: module.id,
      position: index + 1,
    }))

    return this.request<void>(`/members/v2/${courseId}/modules/reorder/`, {
      method: 'POST',
      body: JSON.stringify({ modules: reorderData }),
    })
  }


  async createLesson(moduleId: string, data: Partial<Lesson>): Promise<Lesson> {
    return this.request<Lesson>(`/members/v2/modules/${moduleId}/lessons/`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateLesson(lessonId: string, data: Partial<Lesson>): Promise<Lesson> {
    return this.request<Lesson>(`/members/v2/lessons/${lessonId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteLesson(lessonId: string): Promise<void> {
    return this.request<void>(`/members/v2/lessons/${lessonId}/`, {
      method: 'DELETE',
    })
  }

  async reorderLessons(lessons: Lesson[]): Promise<void> {
    const reorderData = lessons.map((lesson, index) => ({
      id: lesson.id,
      position: index + 1,
    }))

    return this.request<void>('/members/v2/lessons/reorder/', {
      method: 'POST',
      body: JSON.stringify({ lessons: reorderData }),
    })
  }


  async uploadFile(file: File, type: 'banner' | 'logo' | 'cover' | 'thumbnail'): Promise<{ url: string }> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    const response = await fetch(`${this.baseUrl}/upload/`, {
      method: 'POST',
      credentials: 'include', // Include cookies for authentication
      headers: {

        ...Object.fromEntries(
          Object.entries(this.headers).filter(([key]) => key.toLowerCase() !== 'content-type')
        ),
      },
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Upload failed' }))
      throw new Error(error.message || `Upload failed: ${response.status}`)
    }

    return response.json()
  }
}

export const api = new CourseAPI()
