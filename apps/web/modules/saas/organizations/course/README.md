# Módulo de Curso

Este módulo implementa a funcionalidade completa de visualização e interação com cursos na plataforma Cakto Members.

## Estrutura

### Componentes

- **CoursePage**: Componente principal da página do curso
- **VideoPlayer**: Player de vídeo com suporte ao Bunny.net
- **CourseSidebar**: Barra lateral com módulos e aulas

### Hooks

- **useCoursePage**: Gerencia estado da página do curso
- **useCourses**: Hooks para comentários, arquivos e progresso

### API

- **api.ts**: Cliente para comunicação com backend
- Endpoints para módulos, aulas, comentários e arquivos

### Utilitários

- **bunny-utils.ts**: Funções para URLs do Bunny.net

## Uso

```tsx
import { CoursePage } from '@/modules/saas/organizations/course'

<CoursePage 
  courseId="course-id" 
  organizationSlug="org-slug" 
/>
```

## Rotas da API

- `GET /courses/:courseId/modules` - Módulos do curso
- `GET /courses/:courseId/lessons` - Aulas do curso
- `GET /modules/:moduleId/lessons` - Aulas do módulo
- `GET /lessons/:lessonId/comments` - Comentários da aula
- `GET /lessons/:lessonId/files` - Arquivos da aula
- `POST /lessons/:lessonId/watch` - Marcar progresso
- `POST /lessons/:lessonId/comments` - Adicionar comentário
- `POST /lessons/:lessonId/comments/:commentId/replies` - Responder comentário
- `GET /lessons/:lessonId/files/:fileId/download` - Download de arquivo

## Funcionalidades

- ✅ Visualização de vídeos com player customizado
- ✅ Navegação entre módulos e aulas
- ✅ Rastreamento de progresso
- ✅ Sistema de comentários
- ✅ Download de materiais
- ✅ Suporte ao Bunny.net
- ✅ Interface responsiva
- ✅ Estados de loading e erro