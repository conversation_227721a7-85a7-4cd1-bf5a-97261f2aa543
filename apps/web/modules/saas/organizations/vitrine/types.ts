export interface ShowcaseItem {
  id: string;
  title: string;
  image: string;
  description: string;
  category: string;
  progress: number;
  duration: string;
  studentsCount: number;
  isLocked?: boolean;
  price?: number;
  originalPrice?: number;
  discount?: number;
  level?: 'INICIANTE' | 'INTERMEDIARIO' | 'AVANCADO';
  instructor?: string;
  rating?: number;
  totalRatings?: number;
  tags?: string[];
  sectionLocked?: boolean;
  isContinueWatching?: boolean;
  sectionId?: string;
  progressPercentage?: number;
}

export interface ShowcaseSection {
  title: string;
  subtitle: string;
  items: ShowcaseItem[];
}

export interface VitrineData {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    backgroundImage: string;
    ctaText: string;
    ctaLink: string;
  };
  sections: ShowcaseSection[];
}

export interface VitrineSection {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  position: number;
  items: ShowcaseItem[];
  isLocked: boolean;
  requiresPurchase: boolean;
  checkoutUrl?: string;
  webhookUrl?: string;
  price?: number;
  originalPrice?: number;
  accessType: 'free' | 'paid' | 'member_only';
  visibility: 'public' | 'private';
}

export interface VitrineConfig {
  id: string;
  title: string;
  description?: string;
  visibility: 'public' | 'private';
  bannerImage?: string;
  sections: VitrineSection[];
  createdAt: string;
  updatedAt: string;
  stats: {
    views: number;
    enrollments: number;
    revenue: number;
  };
}
