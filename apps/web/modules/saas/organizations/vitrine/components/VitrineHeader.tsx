"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";
import { Bell } from "lucide-react";
import { Button } from "@ui/components/button";
import { UserMenu } from "@saas/shared/components/UserMenu";
import { OrganzationSelect } from "@saas/organizations/components/OrganizationSelect";
import { VitrineNavigation } from "./VitrineNavigation";

interface MenuItem {
  id: string;
  label: string;
  url: string;
  icon?: string;
  isExternal?: boolean;
}

interface VitrineHeaderProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  menuItems?: MenuItem[];
}

export function VitrineHeader({ user, menuItems }: VitrineHeaderProps) {
  const pathname = usePathname();

  return (
    <header className="header-gradient sticky top-0 z-50 w-full backdrop-blur-md">
      <div className="container mx-auto flex items-center gap-2 md:gap-4 h-16 px-4">
        <div className="flex items-center flex-shrink-0 gap-4">
          <Link href="/" className="hover:opacity-80 transition-opacity">
            <div className="h-8 w-auto text-white font-bold text-lg">
              Cakto Members
            </div>
          </Link>
          <OrganzationSelect />
        </div>

        {/* Navegação central */}
        <div className="flex-1 flex items-center justify-center">
          <VitrineNavigation menuItems={menuItems} className="ml-4" />
        </div>

        {/* Área direita */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 relative"
          >
            <Bell className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs notification-badge"></span>
            <span className="sr-only">Notificações</span>
          </Button>

          <UserMenu  />
        </div>
      </div>
    </header>
  );
}
