"use client";

import { But<PERSON> } from "@ui/components/button";
import { Play, Info, Star, Users, Clock } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@ui/lib";

interface HeroBannerProps {
  title: string;
  subtitle: string;
  description: string;
  backgroundImage: string;
  rating?: number;
  duration?: string;
  studentsCount?: number;
  onPlay?: () => void;
  onInfo?: () => void;
  className?: string;
}

export function HeroBanner({
  title,
  subtitle,
  description,
  backgroundImage,
  rating,
  duration,
  studentsCount,
  onPlay,
  onInfo,
  className,
}: HeroBannerProps) {
  return (
    <section
      className={cn(
        "relative min-h-[60vh] md:min-h-[70vh] flex items-center overflow-hidden bg-transparent",
        className
      )}
    >
      {/* Remover Background Image e overlays, deixar só o conteúdo */}
      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 md:px-8">
        <div className="max-w-2xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-4 md:space-y-6"
          >
            {/* Title */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight"
            >
              {title}
            </motion.h1>

            {/* Stats */}
            {(rating || duration || studentsCount) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.6 }}
                className="flex flex-wrap items-center gap-3 md:gap-6 text-white/80"
              >
                {rating && (
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 md:w-5 md:h-5 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium text-sm md:text-base">
                      {rating.toFixed(1)}
                    </span>
                  </div>
                )}
                {duration && (
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 md:w-5 md:h-5" />
                    <span className="text-sm md:text-base">{duration}</span>
                  </div>
                )}
                {studentsCount && (
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 md:w-5 md:h-5" />
                    <span className="text-sm md:text-base">
                      {studentsCount.toLocaleString()} alunos
                    </span>
                  </div>
                )}
              </motion.div>
            )}

            {/* Description */}
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="text-base sm:text-lg md:text-xl text-gray-200 leading-relaxed max-w-xl"
            >
              {description}
            </motion.p>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-3 md:gap-4 pt-2 md:pt-4"
            >
              {onPlay && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full sm:w-auto"
                >
                  <Button
                    size="lg"
                    onClick={onPlay}
                    className="w-full sm:w-auto bg-white text-black hover:bg-gray-200 font-semibold px-6 md:px-8 py-3 md:py-3 text-base md:text-lg h-12 md:h-auto"
                  >
                    <Play className="mr-2 md:mr-3 h-4 w-4 md:h-5 md:w-5 fill-black" />
                    Assistir Agora
                  </Button>
                </motion.div>
              )}

              {onInfo && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full sm:w-auto"
                >
                  <Button
                    size="lg"
                    variant="outline"
                    onClick={onInfo}
                    className="w-full sm:w-auto border-white/30 text-white hover:bg-white/10 hover:border-white/50 font-semibold px-6 md:px-8 py-3 md:py-3 text-base md:text-lg h-12 md:h-auto backdrop-blur-sm"
                  >
                    <Info className="mr-2 md:mr-3 h-4 w-4 md:h-5 md:w-5" />
                    Mais Informações
                  </Button>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Floating Elements - Hidden on mobile */}
      <div className="absolute top-1/4 right-10 hidden lg:block">
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="w-20 h-20 bg-purple-500/20 rounded-full backdrop-blur-sm border border-purple-500/30"
        />
      </div>

      <div className="absolute bottom-1/3 right-1/4 hidden lg:block">
        <motion.div
          animate={{
            y: [0, 15, 0],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
          className="w-12 h-12 bg-blue-500/20 rounded-full backdrop-blur-sm border border-blue-500/30"
        />
      </div>

      {/* Scroll Indicator - Hidden on mobile */}
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 hidden md:block"
      >
        <div className="flex flex-col items-center gap-2 drop-shadow-[0_2px_8px_rgba(255,255,255,0.7)]">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2" />
          </div>
          <span className="text-sm font-medium">Role para baixo</span>
        </div>
      </motion.div>

      {/* Background Image and Overlays */}
      <div className="absolute inset-0">
        <img
          src={backgroundImage}
          alt="Hero background"
          className="w-full h-full object-cover"
        />
        {/* Overlays superiores para legibilidade */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        {/* Gradiente inferior sutil, escurecendo só o final */}
        <div className="absolute bottom-0 left-0 right-0 h-[40vh]" style={{background: 'linear-gradient(to top, rgba(17,17,20,0.95) 90%, rgba(17,17,20,0.0) 60%)'}} />
      </div>
    </section>
  );
}
