"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";
import { HomeIcon, MessageSquareIcon, HelpCircleIcon } from "lucide-react";
import React from "react";

interface MenuItem {
  id: string;
  label: string;
  url: string;
  icon?: string;
  isExternal?: boolean;
}

interface VitrineNavigationProps {
  menuItems?: MenuItem[];
  className?: string;
}

export function VitrineNavigation({ menuItems = [], className }: VitrineNavigationProps) {
  const pathname = usePathname();


  const defaultMenuItems: MenuItem[] = [
    { id: "vitrine", label: "Vitrine", url: "/vitrine", icon: "Home" },
    { id: "comunidade", label: "Comunidade", url: "https://chat.whatsapp.com/example", icon: "MessageSquare", isExternal: true },
    { id: "suporte", label: "Suporte", url: "/suporte", icon: "HelpCircle" },
  ];

  const items = menuItems.length > 0 ? menuItems : defaultMenuItems;


  const getIconComponent = (iconName?: string) => {
    if (!iconName) return null;

    switch (iconName) {
      case "Home":
        return <HomeIcon className="w-4 h-4" />;
      case "MessageSquare":
        return <MessageSquareIcon className="w-4 h-4" />;
      case "HelpCircle":
        return <HelpCircleIcon className="w-4 h-4" />;
      default:
        return null;
    }
  };

  return (
    <nav className={cn("hidden md:flex gap-1", className)}>
      {items.map((item) => {
        const isActive = pathname === item.url;
        const IconComponent = getIconComponent(item.icon);

        return (
          <Link
            key={item.id}
            href={item.url}
            className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-white/10 hover:text-white focus-visible:ring-2 focus-visible:ring-white/20",
              isActive
                ? "text-white bg-white/15 shadow-lg"
                : "text-white/80 hover:text-white"
            )}
            target={item.isExternal ? "_blank" : undefined}
            rel={item.isExternal ? "noopener noreferrer" : undefined}
          >
            {IconComponent}
            <span>{item.label}</span>
          </Link>
        );
      })}
    </nav>
  );
}
