/* Vitrine Styles - Based on Vite project */

/* Head<PERSON> */
.header-gradient {
    background: linear-gradient(135deg, #30004f 0%, #4a0066 25%, #5a007a 50%, #4a0066 75%, #30004f 100%);
    color: white;
    position: relative;
}

.header-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(114, 9, 183, 0.1) 0%, rgba(74, 0, 102, 0.1) 50%, rgba(114, 9, 183, 0.1) 100%);
    pointer-events: none;
    opacity: 0.1;
}

/* Notification badge pulse */
@keyframes notification-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.notification-badge {
    animation: notification-pulse 2s infinite;
}

/* Netflix Carousel Styles */
.netflix-carousel-container {
    position: relative;
    overflow: hidden;
}

.netflix-carousel-item {
    transition: transform 0.3s ease;
}

/* Showcase Card Styles */
.showcase-card-wrapper {
    position: relative;
    overflow: hidden;
}

.showcase-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.75rem;
    background: linear-gradient(to bottom right, #1f2937, #111827, #1f2937);
    border: 1px solid rgba(107, 114, 128, 0.5);
    transition: all 0.3s ease-out;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.showcase-card:hover {
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 15px rgba(139, 92, 246, 0.2);
    transform: translateY(-8px);
}

/* Line clamp utilities */
.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.7);
}

/* Loading animation */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Focus styles */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
}

/* Button animations */
.btn-scale {
    transition: transform 0.2s ease;
}

.btn-scale:hover {
    transform: scale(1.05);
}

.btn-scale:active {
    transform: scale(0.95);
}

/* Glass morphism effect */
.glass-morphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Shimmer effect for loading states */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* --- ShowcaseCard Styles: Copiado do frontend para visual idêntico --- */
.showcase-card-wrapper {
    isolation: isolate;
    contain: layout style;
    position: relative;
    padding: 4px 8px 12px 8px;
}

.showcase-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border-radius: 0.5rem;
    padding: 0 !important;
    margin: 0;
    width: 100%;
    height: 100%;
}

.showcase-card-image-container {
    position: relative;
    overflow: hidden;
    background-color: hsl(var(--muted));
    border-radius: 0.5rem;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease-out;
}

.showcase-card-wrapper:hover .showcase-card-image-container {
    transform: scale(1.03);
}

.showcase-card-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    z-index: 30;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);
    opacity: 1;
    transition: all 0.3s ease-out;
}

.showcase-card-button {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    z-index: 40;
    opacity: 0;
    transform: translateY(0.5rem);
    transition: all 0.3s ease-out;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.5) 80%, transparent 100%);
}

.showcase-card-wrapper:hover .showcase-card-button {
    opacity: 1;
    transform: translateY(0);
}

.showcase-card-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease-out;
    z-index: 20;
}

.showcase-card-wrapper:hover .showcase-card-overlay {
    opacity: 1;
}

.showcase-card-interaction {
    position: absolute;
    inset: 0;
    background-color: rgba(147, 51, 234, 0.05);
    opacity: 0;
    transition: opacity 0.3s ease-out;
    pointer-events: none;
    z-index: 10;
}

.showcase-card-wrapper:hover .showcase-card-interaction {
    opacity: 1;
}

.showcase-card-wrapper:not(:hover) .showcase-card-overlay,
.showcase-card-wrapper:not(:hover) .showcase-card-button,
.showcase-card-wrapper:not(:hover) .showcase-card-interaction {
    opacity: 0;
}

.showcase-card-wrapper:not(:hover) .showcase-card-image-container {
    transform: scale(1);
}

.showcase-card-wrapper:not(:hover) .showcase-card-title {
    opacity: 1;
}

.netflix-carousel-container {
    padding: 12px 0;
    overflow: visible;
}

.netflix-carousel-item {
    padding: 8px 0;
}

/* --- Fim do CSS copiado do frontend --- */

/* Responsive adjustments */
@media (max-width: 640px) {
    .showcase-card-title {
        padding: 0.75rem;
    }

    .showcase-card-button {
        padding: 0.75rem;
    }
}

@media (max-width: 768px) {
    .header-gradient {
        background: linear-gradient(135deg, #30004f 0%, #4a0066 50%, #30004f 100%);
    }
}
