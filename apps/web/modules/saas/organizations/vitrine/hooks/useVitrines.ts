"use client";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@shared/lib/api-client";


const vitrineKeys = {
	all: ["vitrines"] as const,
	organization: (organizationId: string) => [...vitrineKeys.all, "organization", organizationId] as const,
	public: (organizationSlug: string) => [...vitrineKeys.all, "public", organizationSlug] as const,
	organizationVitrine: (organizationSlug: string) => [...vitrineKeys.all, "organizationVitrine", organizationSlug] as const,
};

interface UseVitrinesOptions {
	organizationId: string;
	status?: "DRAFT" | "PUBLISHED" | "ARCHIVED";
	visibility?: "PUBLIC" | "PRIVATE";
	enabled?: boolean;
}

export function useVitrines({
	organizationId,
	status,
	visibility,
	enabled = true,
}: UseVitrinesOptions) {
	return useQuery({
		queryKey: vitrineKeys.organization(organizationId),
		queryFn: async () => {
			const response = await apiClient.vitrines.organization.$get({
				query: {
					organizationId,
					...(status && { status }),
					...(visibility && { visibility }),
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch vitrines");
			}

			return response.json();
		},
		enabled,
	});
}

export function usePublicVitrines(organizationSlug: string) {
	return useQuery({
		queryKey: vitrineKeys.public(organizationSlug),
		queryFn: async () => {
			const response = await fetch(`/api/vitrines/public?organizationSlug=${organizationSlug}`);

			if (!response.ok) {
				throw new Error("Failed to fetch public vitrines");
			}

			return response.json();
		},
	});
}

export function useOrganizationVitrine(organizationSlug: string) {
	return useQuery({
		queryKey: vitrineKeys.organizationVitrine(organizationSlug),
		queryFn: async () => {
			const response = await fetch(`/api/vitrines/organization/vitrine?organizationSlug=${organizationSlug}`);

			if (!response.ok) {
				throw new Error("Failed to fetch organization vitrine");
			}

			return response.json();
		},
		enabled: !!organizationSlug,
	});
}
