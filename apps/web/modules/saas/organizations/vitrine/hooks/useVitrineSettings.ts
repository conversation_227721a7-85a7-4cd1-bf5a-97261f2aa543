"use client";

import { useState, useEffect } from "react";

export interface MenuItem {
  id: string;
  label: string;
  url: string;
  isExternal: boolean;
  order: number;
  icon?: string;
}

export interface VitrineSettings {
  // Personalização
  memberAreaName: string;
  primaryColor: string;
  logo?: string;

  // Menu de navegação
  menuItems: MenuItem[];

  // Comentários
  commentsEnabled: boolean;

  // Suporte
  supportEmail: string;
}

const defaultSettings: VitrineSettings = {
  memberAreaName: "Área de Membros",
  primaryColor: "#6366f1",
  menuItems: [
    {
      id: "1",
      label: "Vitrine",
      url: "/vitrine",
      isExternal: false,
      order: 1,
      icon: "Home"
    },
    {
      id: "2",
      label: "Comunidade",
      url: "https://chat.whatsapp.com/example",
      isExternal: true,
      order: 2,
      icon: "MessageSquare"
    },
    {
      id: "3",
      label: "Suporte",
      url: "/suporte",
      isExternal: false,
      order: 3,
      icon: "HelpCircle"
    }
  ],
  commentsEnabled: true,
  supportEmail: "<EMAIL>"
};

export function useVitrineSettings() {
  const [settings, setSettings] = useState<VitrineSettings>(defaultSettings);
  const [loading, setLoading] = useState(false);

  const loadSettings = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      setSettings(defaultSettings);
      setSettings(defaultSettings);
    } catch (error) {
      console.error("Erro ao carregar configurações:", error);
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    loadSettings();
  }, []);

  return {
    settings,
    loading,
    loadSettings
  };
}
