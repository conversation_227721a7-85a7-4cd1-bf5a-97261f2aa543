"use client";

import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormDescription,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@ui/components/select";
import {
    PlusIcon,
    TrashIcon,
    GripVerticalIcon,
    LinkIcon,
    HashIcon,
    ListIcon
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useFieldArray, type UseFormReturn } from "react-hook-form";
import { cn } from "@ui/lib";

const iconOptions = [
    { value: "dashboard", label: "Dashboard", icon: "📊" },
    { value: "book", label: "Courses", icon: "📚" },
    { value: "users", label: "Community", icon: "👥" },
    { value: "help", label: "Support", icon: "❓" },
    { value: "star", label: "Favorites", icon: "⭐" },
    { value: "crown", label: "Premium", icon: "👑" },
    { value: "headphones", label: "Support", icon: "🎧" },
    { value: "megaphone", label: "Announcements", icon: "📢" },
    { value: "chart", label: "Analytics", icon: "📈" },
    { value: "folder", label: "Resources", icon: "📁" },
];

interface MenuItemsEditorProps {
    form: UseFormReturn<any>;
}

export function MenuItemsEditor({ form }: MenuItemsEditorProps) {
    const t = useTranslations();
    const { fields, append, remove, move } = useFieldArray({
        control: form.control,
        name: "menuItems",
    });

    const addMenuItem = () => {
        append({
            title: "",
            url: "",
            icon: "dashboard",
            position: fields.length + 1,
        });
    };

    const getIconDisplay = (iconValue: string) => {
        const icon = iconOptions.find(opt => opt.value === iconValue);
        return icon ? { emoji: icon.icon, label: icon.label } : { emoji: "📊", label: "Dashboard" };
    };

    return (
        <SettingsItem
            title={t("organizations.settings.memberArea.menu.title")}
            description={t("organizations.settings.memberArea.menu.description")}
        >
            <div className="space-y-4">
                {fields.length === 0 ? (
                    <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                        <ListIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-muted-foreground mb-2">
                            Nenhum item de menu ainda
                        </h3>
                        <p className="text-sm text-muted-foreground mb-4">
                            Adicione itens de menu para criar navegação para sua área de membros
                        </p>
                        <div className="flex justify-center">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={addMenuItem}
                            >
                                <PlusIcon className="h-4 w-4 mr-2" />
                                + Adicionar Primeiro
                            </Button>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {fields.map((field, index) => {
                            const iconDisplay = getIconDisplay((field as any).icon);

                            return (
                                <div
                                    key={field.id}
                                    className="group relative p-4 border rounded-lg bg-card hover:bg-muted/50 transition-colors"
                                >
                                    <div className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <GripVerticalIcon className="h-4 w-4 text-muted-foreground cursor-move" />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-12 gap-3 ml-6">
                                        <div className="md:col-span-2">
                                            <FormField
                                                control={form.control}
                                                name={`menuItems.${index}.icon`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="sr-only">
                                                            Icon
                                                        </FormLabel>
                                                        <Select
                                                            onValueChange={field.onChange}
                                                            defaultValue={field.value}
                                                        >
                                                            <FormControl>
                                                                <SelectTrigger className="h-10">
                                                                    <SelectValue>
                                                                        <div className="flex items-center gap-2">
                                                                            <span className="text-lg">
                                                                                {iconDisplay.emoji}
                                                                            </span>
                                                                            <span className="text-xs text-muted-foreground">
                                                                                {iconDisplay.label}
                                                                            </span>
                                                                        </div>
                                                                    </SelectValue>
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                {iconOptions.map((option) => (
                                                                    <SelectItem key={option.value} value={option.value}>
                                                                        <div className="flex items-center gap-2">
                                                                            <span className="text-lg">
                                                                                {option.icon}
                                                                            </span>
                                                                            <span>{option.label}</span>
                                                                        </div>
                                                                    </SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        {/* Title */}
                                        <div className="md:col-span-4">
                                            <FormField
                                                control={form.control}
                                                name={`menuItems.${index}.title`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="sr-only">
                                                            Title
                                                        </FormLabel>
                                                        <FormControl>
                                                                                                                            <Input
                                                                    {...field}
                                                                    placeholder={t("organizations.settings.memberArea.menu.titlePlaceholder")}
                                                                    className="h-10"
                                                                />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        {/* URL */}
                                        <div className="md:col-span-3">
                                            <FormField
                                                control={form.control}
                                                name={`menuItems.${index}.url`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="sr-only">
                                                            URL
                                                        </FormLabel>
                                                        <FormControl>
                                                            <div className="relative">
                                                                <LinkIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                                                <Input
                                                                    {...field}
                                                                    placeholder={t("organizations.settings.memberArea.menu.urlPlaceholder")}
                                                                    className="h-10 pl-9"
                                                                />
                                                            </div>
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        {/* Position */}
                                        <div className="md:col-span-2">
                                            <FormField
                                                control={form.control}
                                                name={`menuItems.${index}.position`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="sr-only">
                                                            Position
                                                        </FormLabel>
                                                        <FormControl>
                                                            <div className="relative">
                                                                <HashIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                                                <Input
                                                                    {...field}
                                                                    type="number"
                                                                    min="1"
                                                                    className="h-10 pl-9"
                                                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                                                />
                                                            </div>
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        {/* Delete Button */}
                                        <div className="md:col-span-1 flex justify-end">
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => remove(index)}
                                                className="h-10 w-10 p-0 text-muted-foreground hover:text-destructive"
                                            >
                                                <TrashIcon className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>

                                    {/* Preview */}
                                    <div className="mt-3 pt-3 border-t border-muted-foreground/25">
                                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                            <span>Visualização:</span>
                                            <div className="flex items-center gap-1 px-2 py-1 bg-muted rounded">
                                                <span className="text-sm">{iconDisplay.emoji}</span>
                                                <span className="font-medium">
                                                    {(field as any).title || "Item do Menu"}
                                                </span>
                                                <span className="text-muted-foreground">
                                                    {(field as any).url || "/path"}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}

                {fields.length > 0 && (
                    <Button
                        type="button"
                        variant="outline"
                        onClick={addMenuItem}
                        className="w-full"
                    >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        {t("organizations.settings.memberArea.menu.addItem")}
                    </Button>
                )}

                {fields.length > 0 && (
                    <div className="text-xs text-muted-foreground text-center">
                        <p>Arraste os itens para reordenar • Os itens de menu aparecerão na navegação da sua área de membros</p>
                    </div>
                )}
            </div>
        </SettingsItem>
    );
}
