"use client";

import { useTranslations } from "next-intl";
import type { UseFormReturn } from "react-hook-form";
import { CheckCircle2Icon, RocketIcon, UsersIcon, BookOpenIcon, PartyPopperIcon } from "lucide-react";
import { Card, CardContent } from "@ui/components/card";

interface WorkspaceCompletionStepProps {
  form: UseFormReturn<{
    name: string;
    description?: string;
    logo?: string;
  }>;
}

export function WorkspaceCompletionStep({ form }: WorkspaceCompletionStepProps) {
  const t = useTranslations();
  const values = form.getValues();

  return (
    <div className="space-y-12">
      {/* Header de Conclusão */}
      <div className="text-center">
        <div className="bg-gradient-to-br from-success/20 to-success/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center shadow-lg border-2 border-success/30">
          <CheckCircle2Icon className="h-8 w-8 text-success" />
        </div>
        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-success to-success/80 bg-clip-text text-transparent">
          Tudo pronto!
        </h2>
        <p className="text-foreground/80 text-lg max-w-2xl mx-auto">
          Seu workspace <span className="font-semibold text-foreground">{values.name}</span> está configurado e pronto para ser criado.
        </p>
      </div>

      {/* Resumo Final */}
      <Card className="border-2 border-success/30 bg-gradient-to-br from-success/5 to-success/10 shadow-xl">
        <CardContent className="p-8">
          <h3 className="font-bold text-2xl mb-6 flex items-center gap-3 text-success">
            <RocketIcon className="w-6 h-6" />
            Resumo da configuração
          </h3>

          <div className="space-y-4">
            <div className="flex justify-between items-center py-3 border-b border-success/20">
              <span className="text-foreground/80 font-medium text-lg">
                Nome do workspace
              </span>
              <span className="font-bold text-xl text-foreground">{values.name}</span>
            </div>

            {values.description && (
              <div className="flex justify-between items-start py-3 border-b border-success/20">
                <span className="text-foreground/80 font-medium text-lg">
                  Descrição
                </span>
                <span className="font-medium text-right max-w-[60%] text-lg text-foreground">{values.description}</span>
              </div>
            )}

            <div className="flex justify-between items-center py-3">
              <span className="text-foreground/80 font-medium text-lg">
                Sua função
              </span>
              <span className="font-bold text-xl text-success">
                Proprietário
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Próximos Passos */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-2 border-primary/30 bg-gradient-to-br from-primary/5 to-primary/10 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="bg-primary/20 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <UsersIcon className="h-8 w-8 text-primary" />
            </div>
            <h4 className="font-semibold text-lg mb-2 text-foreground">Convidar Membros</h4>
            <p className="text-foreground/70 text-sm">
              Adicione pessoas ao seu workspace para colaborar
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 border-highlight/30 bg-gradient-to-br from-highlight/5 to-highlight/10 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="bg-highlight/20 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <BookOpenIcon className="h-8 w-8 text-highlight" />
            </div>
            <h4 className="font-semibold text-lg mb-2 text-foreground">Criar Conteúdo</h4>
            <p className="text-foreground/70 text-sm">
              Desenvolva cursos e materiais para sua audiência
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 border-accent-foreground/30 bg-gradient-to-br from-accent-foreground/5 to-accent-foreground/10 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="bg-accent-foreground/20 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <PartyPopperIcon className="h-8 w-8 text-accent-foreground" />
            </div>
            <h4 className="font-semibold text-lg mb-2 text-foreground">Personalizar</h4>
            <p className="text-foreground/70 text-sm">
              Configure cores, logo e identidade visual
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
