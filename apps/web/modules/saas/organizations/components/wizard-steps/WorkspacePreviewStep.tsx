"use client";

import { Form, FormControl, FormField, FormItem, FormLabel, FormDescription } from "@ui/components/form";
import { Card, CardContent } from "@ui/components/card";
import { useTranslations } from "next-intl";
import type { UseFormReturn } from "react-hook-form";
import { UserAvatarUpload } from "@saas/settings/components/UserAvatarUpload";
import { Building2Icon, UsersIcon, BookOpenIcon, SettingsIcon, CheckCircleIcon, UploadIcon } from "lucide-react";
import { useState } from "react";

interface WorkspacePreviewStepProps {
  form: UseFormReturn<{
    name: string;
    description?: string;
    logo?: string;
  }>;
}

export function WorkspacePreviewStep({ form }: WorkspacePreviewStepProps) {
  const t = useTranslations();
  const [logoUrl, setLogoUrl] = useState<string | undefined>(form.getValues().logo);
  const values = form.getValues();

  const handleAvatarSuccess = () => {
    const url = "placeholder-url";
    setLogoUrl(url);
    form.setValue("logo", url);
  };

  const handleAvatarError = () => {
    // Handle error
  };

  return (
    <Form {...form}>
      <div className="space-y-12">
        {/* Logo Upload */}
        <div>
          <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <UploadIcon className="w-5 h-5 text-primary" />
            Logo do workspace
          </h3>
          <FormField
            control={form.control}
            name="logo"
            render={({ field }) => (
              <FormItem className="flex items-center justify-between gap-6">
                <div className="flex-1">
                  <FormLabel className="text-lg font-medium">
                    Logo personalizado
                  </FormLabel>
                  <FormDescription className="text-base mt-1">
                    Adicione um logo personalizado para seu workspace (opcional)
                  </FormDescription>
                </div>
                <FormControl>
                  <UserAvatarUpload
                    onSuccess={handleAvatarSuccess}
                    onError={handleAvatarError}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* Preview do Workspace */}
        <div className="pt-8 border-t border-border/30">
          <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <Building2Icon className="w-5 h-5 text-primary" />
            Como ficará seu workspace
          </h3>

          <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10 shadow-xl">
            <CardContent className="p-8">
              <div className="flex items-center gap-6 mb-8">
                <div className="w-16 h-16 bg-primary/20 rounded-2xl flex items-center justify-center">
                  {logoUrl ? (
                    <img src={logoUrl} alt="Logo" className="w-10 h-10 rounded-lg" />
                  ) : (
                    <Building2Icon className="w-8 h-8 text-primary" />
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-bold text-2xl">
                    {values.name || "Seu workspace"}
                  </h4>
                  {values.description && (
                    <p className="text-foreground/70 mt-2 text-lg">
                      {values.description}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2 bg-green-100 text-green-700 px-3 py-1 rounded-full">
                  <CheckCircleIcon className="w-4 h-4" />
                  <span className="text-sm font-medium">Pronto para criar</span>
                </div>
              </div>

              {/* Features Preview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center gap-4 p-4 bg-background/50 rounded-xl border border-border/30">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <UsersIcon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold text-base">Membros</p>
                    <p className="text-sm text-foreground/60">Gerencie sua equipe</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 bg-background/50 rounded-xl border border-border/30">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <BookOpenIcon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold text-base">Cursos</p>
                    <p className="text-sm text-foreground/60">Crie conteúdo</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 bg-background/50 rounded-xl border border-border/30">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <SettingsIcon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-semibold text-base">Configurações</p>
                    <p className="text-sm text-foreground/60">Personalize tudo</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


      </div>
    </Form>
  );
}
