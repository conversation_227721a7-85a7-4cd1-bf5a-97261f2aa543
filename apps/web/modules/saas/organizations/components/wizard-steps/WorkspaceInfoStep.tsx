"use client";

import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent } from "@ui/components/card";
import { useTranslations } from "next-intl";
import type { UseFormReturn } from "react-hook-form";
import { Building2Icon, SparklesIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface WorkspaceInfoStepProps {
  form: UseFormReturn<{
    name: string;
    description?: string;
    logo?: string;
  }>;
}

export function WorkspaceInfoStep({ form }: WorkspaceInfoStepProps) {
  const t = useTranslations();
  const [nameLength, setNameLength] = useState(0);
  const [descriptionLength, setDescriptionLength] = useState(0);
  const values = form.watch();

  useEffect(() => {
    setNameLength(values.name?.length || 0);
    setDescriptionLength(values.description?.length || 0);
  }, [values.name, values.description]);

  const getSlugPreview = (name: string) => {
    if (!name) return "";
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");
  };

  return (
    <Form {...form}>
      <div className="space-y-12">
        {/* Formulário Principal */}
        <div className="space-y-8">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xl font-semibold flex items-center gap-2">
                  <Building2Icon className="w-5 h-5 text-primary" />
                  Nome do workspace
                </FormLabel>
                <FormDescription className="text-base mt-2">
                  Este será o nome exibido para você e seus membros
                </FormDescription>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Plataforma de Membros"
                    className="text-lg h-14 text-base mt-4"
                  />
                </FormControl>
                <div className="flex items-center justify-between text-sm text-foreground/60 mt-2">
                  <span>{nameLength}/32 caracteres</span>
                  {field.value && (
                    <span className="font-mono text-primary bg-primary/10 px-2 py-1 rounded">
                      cakto.com/{getSlugPreview(field.value)}
                    </span>
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xl font-semibold flex items-center gap-2">
                  <SparklesIcon className="w-5 h-5 text-primary" />
                  Descrição
                </FormLabel>
                <FormDescription className="text-base mt-2">
                  Uma breve descrição do propósito do seu workspace (opcional)
                </FormDescription>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Descreva o propósito deste workspace"
                    rows={4}
                    className="resize-none text-base mt-4"
                  />
                </FormControl>
                <div className="flex justify-end text-sm text-foreground/60 mt-2">
                  {descriptionLength}/500 caracteres
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Preview Rápido */}
        {values.name && (
          <div className="pt-8 border-t border-border/30">
            <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
              <Building2Icon className="w-5 h-5 text-primary" />
              Preview do workspace
            </h3>

            <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-14 h-14 bg-primary/20 rounded-xl flex items-center justify-center">
                    <Building2Icon className="w-7 h-7 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-xl">
                      {values.name}
                    </h4>
                    {values.description && (
                      <p className="text-foreground/70 mt-2 line-clamp-2">
                        {values.description}
                      </p>
                    )}
                    <p className="text-sm text-primary/70 mt-3 font-mono bg-primary/10 px-3 py-1 rounded inline-block">
                      cakto.com/{getSlugPreview(values.name)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Form>
  );
}
