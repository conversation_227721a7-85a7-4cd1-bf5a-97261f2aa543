'use client'

import { useSession } from "@saas/auth/hooks/use-session"
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization"
import { isOrganizationAdmin } from "@repo/auth/lib/helper"
import { redirect } from "next/navigation"
import { useEffect } from "react"

interface AdminRouteProps {
  children: React.ReactNode
  requireGlobalAdmin?: boolean
  requireOrganizationAdmin?: boolean
  organizationSlug?: string
}

export function AdminRoute({
  children,
  requireGlobalAdmin = false,
  requireOrganizationAdmin = false,
  organizationSlug
}: AdminRouteProps) {
  const { user, loaded } = useSession()
  const { activeOrganization } = useActiveOrganization()

  useEffect(() => {
    if (!loaded) return

    if (!user) {
      redirect("/auth/login")
      return
    }

    // Check global admin access
    if (requireGlobalAdmin && user.role !== "admin") {
      redirect("/app")
      return
    }

    // Check organization admin access
    if (requireOrganizationAdmin) {
      const organization = activeOrganization || {
        members: [],
        invitations: [],
        id: '',
        name: '',
        slug: organizationSlug || '',
        createdAt: new Date()
      }

      if (!isOrganizationAdmin(organization, user)) {
        redirect("/app")
        return
      }
    }
  }, [user, loaded, requireGlobalAdmin, requireOrganizationAdmin, activeOrganization, organizationSlug])

  if (!loaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // Global admin check
  if (requireGlobalAdmin && user.role !== "admin") {
    return null
  }

  // Organization admin check
  if (requireOrganizationAdmin) {
    const organization = activeOrganization || {
      members: [],
      invitations: [],
      id: '',
      name: '',
      slug: organizationSlug || '',
      createdAt: new Date()
    }

    if (!isOrganizationAdmin(organization, user)) {
      return null
    }
  }

  return <>{children}</>
}
