"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  role: string;
}

interface OrganizationContextType {
  organizations: Organization[];
  activeOrganization: Organization | null;
  setActiveOrganization: (organization: Organization) => void;
}

const OrganizationContext = createContext<OrganizationContextType | null>(null);

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error("useOrganization must be used within an OrganizationProvider");
  }
  return context;
}

interface OrganizationProviderProps {
  children: ReactNode;
  organizations: Organization[];
  activeOrganizationId?: string;
}

export function OrganizationProvider({
  children,
  organizations,
  activeOrganizationId,
}: OrganizationProviderProps) {
  const [activeOrganization, setActiveOrganization] = useState<Organization | null>(null);

  useEffect(() => {
    if (organizations.length > 0) {
      if (activeOrganizationId) {
        const org = organizations.find(org => org.id === activeOrganizationId);
        if (org) {
          setActiveOrganization(org);
          return;
        }
      }
      // Fallback to first organization if no active organization is set
      setActiveOrganization(organizations[0]);
    } else {
      setActiveOrganization(null);
    }
  }, [organizations, activeOrganizationId]);

  return (
    <OrganizationContext.Provider
      value={{
        organizations,
        activeOrganization,
        setActiveOrganization,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
}
