"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { OrganizationRoleSelect } from "@saas/organizations/components/OrganizationRoleSelect";
import { fullOrganizationQueryKey } from "@saas/organizations/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { UserPlusIcon, MailIcon, ShieldIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
	email: z.string().email(),
	role: z.enum(["member", "owner", "admin"]),
});

type FormValues = z.infer<typeof formSchema>;

export function InviteMemberForm({
	organizationId,
}: {
	organizationId: string;
}) {
	const t = useTranslations();
	const queryClient = useQueryClient();

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
			role: "member",
		},
	});

	const onSubmit: SubmitHandler<FormValues> = async (values) => {
		try {
			const { error } = await authClient.organization.inviteMember({
				...values,
				organizationId,
			});

			if (error) {
				throw error;
			}

			form.reset();

			queryClient.invalidateQueries({
				queryKey: fullOrganizationQueryKey(organizationId),
			});

			toast.success(
				t(
					"organizations.settings.members.inviteMember.notifications.success.title",
				),
			);
		} catch {
			toast.error(
				t(
					"organizations.settings.members.inviteMember.notifications.error.title",
				),
			);
		}
	};

	return (
		<Card className="bg-gradient-to-br from-green/5 to-green/10 border-green/20">
			<CardHeader className="pb-4">
				<div className="flex items-center gap-3">
					<div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
						<UserPlusIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
					</div>
					<div>
						<CardTitle className="text-lg font-semibold">
							{t("organizations.settings.members.inviteMember.title")}
						</CardTitle>
						<p className="text-sm text-muted-foreground">
							{t("organizations.settings.members.inviteMember.description")}
						</p>
					</div>
				</div>
			</CardHeader>
			<CardContent>
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4"
					>
						<div className="grid gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-2">
											<MailIcon className="h-4 w-4" />
											{t("organizations.settings.members.inviteMember.email")}
										</FormLabel>
										<FormControl>
											<Input
												type="email"
												{...field}
												placeholder="<EMAIL>"
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="role"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-2">
											<ShieldIcon className="h-4 w-4" />
											{t("organizations.settings.members.inviteMember.role")}
										</FormLabel>
										<FormControl>
											<OrganizationRoleSelect
												value={field.value}
												onSelect={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
						</div>

						<div className="flex justify-end">
							<Button
								type="submit"
								loading={form.formState.isSubmitting}
								className="px-6"
							>
								<UserPlusIcon className="mr-2 h-4 w-4" />
								{t("organizations.settings.members.inviteMember.submit")}
							</Button>
						</div>
					</form>
				</Form>
			</CardContent>
		</Card>
	);
}
