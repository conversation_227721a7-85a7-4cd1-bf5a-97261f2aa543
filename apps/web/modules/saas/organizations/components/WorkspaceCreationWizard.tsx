"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	organizationListQuery<PERSON>ey,
	useCreateOrganizationMutation,
} from "@saas/organizations/lib/api";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { WorkspaceInfoStep } from "./wizard-steps/WorkspaceInfoStep";
import { WorkspacePreviewStep } from "./wizard-steps/WorkspacePreviewStep";
import { WorkspaceCompletionStep } from "./wizard-steps/WorkspaceCompletionStep";

const formSchema = z.object({
	name: z.string().min(3).max(32),
	description: z.string().optional(),
	logo: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function WorkspaceCreationWizard() {
	const t = useTranslations();
	const router = useRouter();
	const queryClient = useQueryClient();
	const { setActiveOrganization } = useActiveOrganization();
	const createOrganizationMutation = useCreateOrganizationMutation();
	const [currentStep, setCurrentStep] = useState(1);
	const totalSteps = 3;

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
			description: "",
			logo: "",
		},
	});

	const nextStep = () => {
		if (currentStep < totalSteps) {
			setCurrentStep(currentStep + 1);
		}
	};

	const prevStep = () => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
		}
	};

	const onSubmit = form.handleSubmit(async (values) => {
		if (currentStep < totalSteps) {
			nextStep();
			return;
		}

		try {
			const newOrganization = await createOrganizationMutation.mutateAsync({
				name: values.name,
				metadata: {
					description: values.description || undefined,
				},
			});

			if (!newOrganization) {
				throw new Error("Failed to create workspace");
			}

			await setActiveOrganization(newOrganization.id);

			await queryClient.invalidateQueries({
				queryKey: organizationListQueryKey,
			});

			toast.success(t("organizations.createForm.notifications.success"));
			router.replace(`/app/${newOrganization.slug}`);
		} catch (e) {
			toast.error(t("organizations.createForm.notifications.error"));
		}
	});

	const renderStep = () => {
		switch (currentStep) {
			case 1:
				return <WorkspaceInfoStep form={form} />;
			case 2:
				return <WorkspacePreviewStep form={form} />;
			case 3:
				return <WorkspaceCompletionStep form={form} />;
			default:
				return <WorkspaceInfoStep form={form} />;
		}
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case 1:
				return "Informações básicas";
			case 2:
				return "Personalizar workspace";
			case 3:
				return "Workspace criado com sucesso!";
			default:
				return "Informações básicas";
		}
	};

	const getStepDescription = () => {
		switch (currentStep) {
			case 1:
				return "Forneça informações básicas sobre o seu workspace.";
			case 2:
				return "Adicione um logo e veja como ficará seu workspace.";
			case 3:
				return "Seu workspace está pronto para uso.";
			default:
				return "Forneça informações básicas sobre o seu workspace.";
		}
	};

	return (
		<div className="max-w-4xl mx-auto">
			{/* Header com título dinâmico */}
			<div className="text-center mb-12">
				<h1 className="font-bold text-4xl md:text-5xl mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
					{getStepTitle()}
				</h1>
				<p className="text-foreground/60 text-xl max-w-2xl mx-auto">
					{getStepDescription()}
				</p>
			</div>

			{/* Barra de progresso melhorada */}
			<div className="mb-12">
				<div className="flex items-center justify-center gap-8 mb-6">
					<div className="flex items-center gap-4">
						<Progress
							value={(currentStep / totalSteps) * 100}
							className="h-3 w-96"
						/>
						<span className="text-sm font-medium text-foreground/80 whitespace-nowrap">
							{Math.round((currentStep / totalSteps) * 100)}% concluído
						</span>
					</div>
				</div>
				<div className="flex items-center justify-center gap-6">
					{Array.from({ length: totalSteps }, (_, i) => (
						<div key={i} className="flex items-center gap-3">
							<div
								className={`w-8 h-8 rounded-full transition-all duration-300 flex items-center justify-center text-sm font-medium ${
									i + 1 <= currentStep
										? "bg-primary text-primary-foreground scale-110"
										: "bg-muted text-muted-foreground"
								}`}
							>
								{i + 1}
							</div>
							{i < totalSteps - 1 && (
								<div
									className={`w-12 h-0.5 transition-colors duration-300 ${
										i + 1 < currentStep ? "bg-primary" : "bg-muted"
									}`}
								/>
							)}
						</div>
					))}
				</div>
			</div>

			{/* Conteúdo principal */}
			<Card className="p-8 lg:p-12 shadow-xl border-0 bg-card/50 backdrop-blur-sm">
				<form onSubmit={onSubmit}>
					{renderStep()}

					<div className="flex justify-between mt-12 pt-8 border-t border-border/50">
						{currentStep > 1 ? (
							<Button
								type="button"
								variant="outline"
								onClick={prevStep}
								className="min-w-[160px] h-14 text-base"
							>
								Voltar
							</Button>
						) : (
							<div></div>
						)}
						<Button
							type="submit"
							loading={form.formState.isSubmitting}
							className="min-w-[160px] h-14 text-base"
						>
							{currentStep < totalSteps
								? "Continuar"
								: "Criar workspace"}
						</Button>
					</div>
				</form>
			</Card>
		</div>
	);
}
