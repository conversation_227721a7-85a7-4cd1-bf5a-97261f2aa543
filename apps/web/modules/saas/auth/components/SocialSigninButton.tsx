"use client";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { Button } from "@ui/components/button";
import { useQueryState } from "nuqs";
import { parseAsString } from "nuqs";
import { oAuthProviders } from "../constants/oauth-providers";

export function SocialSigninButton({
	provider,
	className,
}: {
	provider: keyof typeof oAuthProviders;
	className?: string;
}) {
	const [invitationId] = useQueryState("invitationId", parseAsString);
	const providerData = oAuthProviders[provider];

	const redirectPath = invitationId
		? `/app/organization-invitation/${invitationId}`
		: config.auth.redirectAfterSignIn;

	const onSignin = async () => {
		if (provider === "cakto") {
			try {
				const callbackURL = new URL(
					redirectPath,
					window.location.origin,
				);
				await authClient.signIn.oauth2({
					providerId: "django-sso",
					callbackURL: callbackURL.toString(),
				});
			} catch (error) {
				console.error("Erro ao iniciar SSO Cakto:", error);
			}
			return;
		}

		const callbackURL = new URL(redirectPath, window.location.origin);
		authClient.signIn.social({
			provider: provider as any,
			callbackURL: callbackURL.toString(),
		});
	};

	return (
		<Button
			onClick={() => onSignin()}
			variant="light"
			type="button"
			className={className}
		>
			{providerData.icon && (
				<i className="mr-2 text-primary">
					<providerData.icon className="size-4" />
				</i>
			)}
			{providerData.name}
		</Button>
	);
}
