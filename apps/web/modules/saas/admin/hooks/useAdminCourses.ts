import { useState, useEffect, useCallback } from "react";
import { apiClient } from "@shared/lib/api-client";

interface Organization {
	id: string;
	name: string;
	slug: string | null;
	coursesCount: number;
}

interface Course {
	id: string;
	name: string;
	logo?: string | null;
	community?: string | null;
	description?: string | null;
	studentsCount: number;
	modulesCount: number;
	status: string;
	type: string;
	createdAt: string;
	updatedAt: string;
	organizationId: string;
	organization: {
		id: string;
		name: string;
		slug: string | null;
	};
	creator?: {
		id: string;
		name: string;
		email: string;
	} | null;
}

interface AdminCoursesResponse {
	courses: Course[];
	totalCount: number;
	organizations: Organization[];
	pagination: {
		limit: number;
		offset: number;
		hasMore: boolean;
	};
}

interface UseAdminCoursesOptions {
	organizationSlug?: string;
	status?: "DRAFT" | "PUBLISHED" | "ARCHIVED";
	limit?: number;
}

export function useAdminCourses(options: UseAdminCoursesOptions = {}) {
	const [data, setData] = useState<AdminCoursesResponse | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isDeleting, setIsDeleting] = useState<string | null>(null);

	const fetchCourses = useCallback(async () => {
		try {
			setIsLoading(true);
			setError(null);

			const queryParams = new URLSearchParams();
			if (options.organizationSlug) {
				queryParams.append("organizationSlug", options.organizationSlug);
			}
			if (options.status) {
				queryParams.append("status", options.status);
			}
			if (options.limit) {
				queryParams.append("limit", options.limit.toString());
			}

			const response = await apiClient.courses.admin.$get({
				query: Object.fromEntries(queryParams),
			});

			if (!response.ok) {
				const errorData = await response.json() as { error?: string };
				throw new Error(errorData.error || "Failed to fetch courses");
			}

			const coursesData = await response.json();
			setData(coursesData);
		} catch (err) {
			console.error("Error fetching admin courses:", err);
			setError(err instanceof Error ? err.message : "Failed to fetch courses");
		} finally {
			setIsLoading(false);
		}
	}, [options.organizationSlug, options.status, options.limit]);

	const deleteCourse = useCallback(async (courseId: string): Promise<boolean> => {
		try {
			setIsDeleting(courseId);
			setError(null);

			const response = await apiClient.courses[":courseId"].$delete({
				param: { courseId },
			});

			if (!response.ok) {
				const errorData = await response.json() as { error?: string };
				throw new Error(errorData.error || "Failed to delete course");
			}

			// Refresh the courses list after successful deletion
			await fetchCourses();
			return true;
		} catch (err) {
			console.error("Error deleting course:", err);
			setError(err instanceof Error ? err.message : "Failed to delete course");
			return false;
		} finally {
			setIsDeleting(null);
		}
	}, [fetchCourses]);

	useEffect(() => {
		fetchCourses();
	}, [fetchCourses]);

	return {
		courses: data?.courses || [],
		organizations: data?.organizations || [],
		totalCount: data?.totalCount || 0,
		pagination: data?.pagination,
		isLoading,
		error,
		isDeleting,
		deleteCourse,
		refetch: fetchCourses,
	};
}
