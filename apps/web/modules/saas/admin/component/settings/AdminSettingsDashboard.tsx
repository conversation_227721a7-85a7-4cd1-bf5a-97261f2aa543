'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { But<PERSON> } from '@/modules/ui/components/button'
import { SettingsIcon, SaveIcon } from 'lucide-react'
import { AdminPageLayout } from '../shared/AdminPageLayout'

export function AdminSettingsDashboard() {
  const [isSaving, setIsSaving] = useState(false)

  const handleSaveSettings = async () => {
    setIsSaving(true)
    // Implementar salvamento das configurações
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsSaving(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Configurações</h2>
          <p className="text-muted-foreground">Configure as opções da plataforma</p>
        </div>
        <Button onClick={handleSaveSettings}>
          <SaveIcon className="mr-2 h-4 w-4" />
          <PERSON><PERSON>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardHeader>
            <CardTitle>Configurações Gerais</CardTitle>
            <CardDescription>
              Configurações básicas da plataforma
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Nome da Plataforma</label>
                <input
                  type="text"
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                  placeholder="Nome da sua plataforma"
                />
              </div>
              <div>
                <label className="text-sm font-medium">URL da Plataforma</label>
                <input
                  type="url"
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                  placeholder="https://sua-plataforma.com"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardHeader>
            <CardTitle>Configurações de Email</CardTitle>
            <CardDescription>
              Configurações para envio de emails
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Email de Suporte</label>
                <input
                  type="email"
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Email de Notificações</label>
                <input
                  type="email"
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardHeader>
            <CardTitle>Configurações de Pagamento</CardTitle>
            <CardDescription>
              Configurações relacionadas a pagamentos
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Moeda Padrão</label>
                <select className="w-full mt-1 px-3 py-2 border rounded-md">
                  <option value="BRL">Real Brasileiro (BRL)</option>
                  <option value="USD">Dólar Americano (USD)</option>
                  <option value="EUR">Euro (EUR)</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Gateway de Pagamento</label>
                <select className="w-full mt-1 px-3 py-2 border rounded-md">
                  <option value="stripe">Stripe</option>
                  <option value="paypal">PayPal</option>
                  <option value="mercadopago">Mercado Pago</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardHeader>
            <CardTitle>Configurações de Segurança</CardTitle>
            <CardDescription>
              Configurações de segurança da plataforma
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Autenticação de 2 Fatores</label>
                  <p className="text-xs text-muted-foreground">Exigir 2FA para todos os usuários</p>
                </div>
                <input type="checkbox" className="ml-2" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Login com Google</label>
                  <p className="text-xs text-muted-foreground">Permitir login com Google</p>
                </div>
                <input type="checkbox" className="ml-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
