"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { toast } from "sonner";
import { authClient } from "@repo/auth/client";

interface CreateUserModalProps {
	isOpen: boolean;
	onClose: () => void;
	onUserCreated?: () => void;
}

export function CreateUserModal({
	isOpen,
	onClose,
	onUserCreated,
}: CreateUserModalProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [formData, setFormData] = useState({
		name: "",
		email: "",
		role: "user",
		sendEmail: true,
	});

	const generateRandomPassword = () => {
		const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		let password = "";
		for (let i = 0; i < 12; i++) {
			password += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		return password;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);

		try {
			const password = generateRandomPassword();

			// Create user using Better Auth
			const { error } = await authClient.admin.createUser({
				email: formData.email,
				name: formData.name,
				password,
				role: formData.role as "user" | "admin",
			});

			if (error) {
				throw new Error(error.message);
			}

			// If sendEmail is true, send welcome email with credentials
			if (formData.sendEmail) {
				// This would typically be handled by the backend
				// For now, we'll show the credentials in a toast
				toast.success(
					`Usuário criado com sucesso! Credenciais: ${formData.email} / ${password}`,
					{
						duration: 10000,
					}
				);
			} else {
				toast.success("Usuário criado com sucesso!");
			}

			onUserCreated?.();
			onClose();

			// Reset form
			setFormData({
				name: "",
				email: "",
				role: "user",
				sendEmail: true,
			});
		} catch (error: any) {
			toast.error(error.message || "Erro ao criar usuário");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Criar Novo Usuário</DialogTitle>
					<DialogDescription>
						Adicione um novo usuário à plataforma. O usuário receberá um
						email com suas credenciais de acesso.
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="name">Nome</Label>
						<Input
							id="name"
							value={formData.name}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									name: e.target.value,
								}))
							}
							placeholder="Nome completo"
							required
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="email">Email</Label>
						<Input
							id="email"
							type="email"
							value={formData.email}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									email: e.target.value,
								}))
							}
							placeholder="<EMAIL>"
							required
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="role">Função</Label>
						<Select
							value={formData.role}
							onValueChange={(value) =>
								setFormData((prev) => ({ ...prev, role: value }))
							}
						>
							<SelectTrigger>
								<SelectValue placeholder="Selecione uma função" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="user">Usuário</SelectItem>
								<SelectItem value="admin">Administrador</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div className="flex items-center space-x-2">
						<input
							id="sendEmail"
							type="checkbox"
							checked={formData.sendEmail}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									sendEmail: e.target.checked,
								}))
							}
							className="rounded border-gray-300"
						/>
						<Label htmlFor="sendEmail" className="text-sm">
							Enviar email com credenciais de acesso
						</Label>
					</div>

					<div className="flex justify-end gap-2 pt-4">
						<Button type="button" variant="outline" onClick={onClose}>
							Cancelar
						</Button>
						<Button type="submit" disabled={isLoading}>
							{isLoading ? "Criando..." : "Criar Usuário"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
