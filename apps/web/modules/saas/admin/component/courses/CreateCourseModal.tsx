'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/modules/ui/components/dialog'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/modules/ui/components/select'
import { toast } from 'sonner'
import { PlusIcon, Loader2Icon } from 'lucide-react'

interface Organization {
  id: string
  name: string
  slug: string
}

interface CreateCourseModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  organizations: Organization[]
}

export function CreateCourseModal({ open, onOpenChange, organizations }: CreateCourseModalProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    organizationId: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('Nome do curso é obrigatório')
      return
    }
    
    if (!formData.organizationId) {
      toast.error('Selecione uma organização')
      return
    }

    setIsLoading(true)
    
    try {
      const organization = organizations.find(org => org.id === formData.organizationId)
      if (organization) {
        // Redirect to course creation page
        router.push(`/app/${organization.slug}/courses/create?name=${encodeURIComponent(formData.name)}`)
        onOpenChange(false)
        setFormData({ name: '', organizationId: '' })
      }
    } catch (error) {
      toast.error('Erro ao criar curso')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Criar Novo Curso</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="courseName">Nome do Curso</Label>
            <Input
              id="courseName"
              placeholder="Ex: React Avançado"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="organization">Organização</Label>
            <Select
              value={formData.organizationId}
              onValueChange={(value) => setFormData(prev => ({ ...prev, organizationId: value }))}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma organização" />
              </SelectTrigger>
              <SelectContent>
                {organizations.map((org) => (
                  <SelectItem key={org.id} value={org.id}>
                    {org.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2Icon className="h-4 w-4 mr-2 animate-spin" />
                  Criando...
                </>
              ) : (
                <>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Criar Curso
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}