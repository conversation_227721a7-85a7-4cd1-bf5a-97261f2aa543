import {
	Too<PERSON><PERSON>,
	Too<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { CheckIcon, ClockIcon, ShieldIcon, UserIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export function UserRoleIcon({
	role,
	className,
}: {
	role: string;
	className?: string;
}) {
	const t = useTranslations();
	return (
		<TooltipProvider delayDuration={0}>
			<Tooltip>
				<TooltipContent>
					{role === "admin" ? "Administrador" : "Membro"}
					</TooltipContent>
				<TooltipTrigger className={cn(className)}>
					{role === "admin" ? <ShieldIcon className="size-3 text-primary" /> : <UserIcon className="size-4" />}
				</TooltipTrigger>
			</Tooltip>
		</TooltipProvider>
	);
}
