import { auth } from "@repo/auth";
import { createUser, createUserAccount, getUserByEmail } from "@repo/database";
import { logger } from "@repo/logs";

async function main() {
  console.log("🔐 Criando usuários em lote com Better Auth...");

  const users = [
    {
      email: "<EMAIL>",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON><PERSON><PERSON>",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "admin",
      password: "admin123"
    },
    {
      email: "ismaelm<PERSON><EMAIL>",
      name: "<PERSON><PERSON><PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Beatriz Almeida",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Rafael Mendes",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Camila Souza",
      role: "user",
      password: "user123"
    }
  ];

  const authContext = await auth.$context;

  for (const userData of users) {
    try {
      const existingUser = await getUserByEmail(userData.email);
      if (existingUser) {
        console.log(`⚠️ Usuário já existe: ${userData.email}`);
        continue;
      }

      const hashedPassword = await authContext.password.hash(userData.password);

      const user = await createUser({
        email: userData.email,
        name: userData.name,
        role: userData.role as "user" | "admin",
        emailVerified: true,
        onboardingComplete: true,
      });

      if (!user) {
        console.log(`❌ Falha ao criar usuário: ${userData.email}`);
        continue;
      }

      await createUserAccount({
        userId: user.id,
        providerId: "credential",
        accountId: userData.email,
        hashedPassword,
      });

      console.log(`✅ Usuário criado: ${userData.email} (${userData.role}) - Senha: ${userData.password}`);

    } catch (error) {
      console.log(`❌ Erro ao criar usuário ${userData.email}:`, error);
    }
  }

  console.log("\n🎉 Processo concluído!");
  console.log("\n🔐 Contas criadas:");
  console.log("Super Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Ismael Costa: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");
}

main()
  .catch(e => {
    console.error('❌ Erro:', e);
    process.exit(1);
  });
