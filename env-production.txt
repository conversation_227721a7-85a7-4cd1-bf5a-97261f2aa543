# =============================================================================
# MEMBERS-BASE - CONFIGURAÇÃO DE PRODUÇÃO
# =============================================================================

# =============================================================================
# DATABASE (Neon)
# =============================================================================
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# =============================================================================
# BETTER AUTH
# =============================================================================
BETTER_AUTH_SECRET=112c209614f7bb08cf834f53d5e2b1721ba940a106b01a3769a4e9bd29cd723c697ce245a874c1ab3140142c3a4fb780c19bf1c5a302b1ee3e85a4950cc3e6c8

# =============================================================================
# NEXT.JS (URLs de produção)
# =============================================================================
NEXT_PUBLIC_SITE_URL=https://members-base.vercel.app
NEXT_PUBLIC_APP_URL=https://members-base.vercel.app

# =============================================================================
# MAIL SMTP
# =============================================================================
MAIL__ADDRESS_FROM=<EMAIL>
MAIL__SMTP_ENABLE=true
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAILER_SMTP_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASS=**************************************************
MAIL__TLS_REJECT_UNAUTHORIZED=false
MAIL_USE_CUSTOM_CONFIGS=true

# =============================================================================
# STORAGE (DigitalOcean Spaces)
# =============================================================================
S3_ENDPOINT=https://cakto-members-files.nyc3.digitaloceanspaces.com/
S3_REGION=nyc3
S3_ACCESS_KEY_ID=DO00CZT23AZED2ZHJTL7
S3_SECRET_ACCESS_KEY=cqLzBLc1bmP5HjTiStDz4RGTolwn/DKWn5xW//QKh+c
NEXT_PUBLIC_AVATARS_BUCKET_NAME=cakto-members-files
S3_BUCKET_NAME=cakto-members-files

# =============================================================================
# SSO Configuration
# =============================================================================
CAKTO_SSO_CLIENT_ID=Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN
CAKTO_SSO_CLIENT_SECRET=KqLDBTCThIh82h2kOPRK7VBbpVyAOoQSTXSJDmdOeSvO5HeLrMX6PeuesRK6qcZGZJ8aoYcLOxy9htHzmBo57O1yPdcCXCCNlolJMt7P1NyiSs8ePOgT0NJXsjIT6O9f
SSO_REDIRECT_URI=https://members-base.vercel.app/auth/callback
CAKTO_API_URL=https://sso.cakto.com.br
SSO_DOMAIN=sso.cakto.com.br
CAKTO_DOMAIN=.cakto.com.br

# =============================================================================
# Bunny.net Stream Configuration
# =============================================================================
NEXT_PUBLIC_BUNNY_LIBRARY_ID=467287
NEXT_PUBLIC_BUNNY_SIGNATURE=812c6dae-88a0-4b5a-bf32b440cae9-5072-4772
NEXT_PUBLIC_BUNNY_EXPIRE=1735689600

# =============================================================================
# AMBIENTE
# =============================================================================
NODE_ENV=production
NEXT_PUBLIC_CAKTO_BACKEND_URL=https://api.cakto.com.br

# =============================================================================
# INSTRUÇÕES
# =============================================================================
# 1. Acesse: https://vercel.com/dashboard
# 2. Vá em seu projeto "members-base"
# 3. Clique em "Settings" > "Environment Variables"
# 4. Adicione cada variável acima (Production)
# 5. Após adicionar todas, faça o deploy novamente
# 6. URLs podem precisar ser atualizadas após o primeiro deploy

# =============================================================================
# CONTAS DE TESTE
# =============================================================================
# Super Admin: <EMAIL> / admin123
# Admin: <EMAIL> / admin123
# Ismael Costa: <EMAIL> / user123
