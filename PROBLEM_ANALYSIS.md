# Análise do Problema: Error: <Html> should not be imported outside of pages/_document

## Resumo do Problema

O projeto está enfrentando um erro persistente durante o build do Next.js:

```
Error: <Html> should not be imported outside of pages/_document.
Read more: https://nextjs.org/docs/messages/no-document-import-in-page
```

Este erro ocorre especificamente durante a fase de "Collecting page data" do build, afetando as páginas `/404` e `/500`.

## Causa Raiz Identificada

O problema é causado pelo pacote `@repo/mail` que utiliza componentes do React Email (`@react-email/components`), especificamente o componente `<Html>`. O Next.js não permite que componentes do React Email sejam incluídos no bundle do lado do cliente, pois eles são destinados apenas para renderização server-side de emails.

### Estrutura do Problema

1. **Dependência**: `@repo/auth` importa `sendEmail` de `@repo/mail`
2. **Pacote de Email**: `@repo/mail` usa `@react-email/components` (incluindo `<Html>`)
3. **Build Process**: Next.js tenta incluir esses componentes no bundle do cliente
4. **Erro**: Next.js rejeita `<Html>` fora de `pages/_document`

## Tentativas de Solução Implementadas

### 1. Configuração Webpack (❌ Falhou)

**Abordagem**: Adicionar aliases webpack para ignorar `@repo/mail` no lado do cliente

```typescript
// apps/web/next.config.ts
webpack: (config, { webpack, isServer }) => {
  if (!isServer) {
    config.resolve.alias = {
      ...config.resolve.alias,
      "@repo/mail": false,
      "@react-email/components": false,
      "@react-email/render": false,
      "react-email": false,
    };
  }
  return config;
}
```

**Resultado**: Erro persistiu, indicando que o webpack ainda estava processando os componentes

### 2. Imports Dinâmicos no Pacote de Email (❌ Falhou)

**Abordagem**: Modificar `packages/mail` para usar imports dinâmicos

```typescript
// packages/mail/src/util/email-components.ts
export async function getEmailComponents() {
  const { Container, Font, Head, Html, Section, Tailwind } = await import("@react-email/components");
  return { Container, Font, Head, Html, Section, Tailwind };
}

// packages/mail/src/util/templates.ts
const { render } = await import("@react-email/render");
```

**Resultado**: Erro persistiu, indicando que os componentes ainda estavam sendo incluídos no bundle

### 3. Imports Dinâmicos no Auth (❌ Falhou)

**Abordagem**: Modificar `packages/auth/auth.ts` para usar imports dinâmicos

```typescript
const sendEmailSafely = async (options: any) => {
  try {
    const { sendEmail } = await import("@repo/mail");
    return await sendEmail(options);
  } catch (error) {
    logger.error("Failed to send email", { error, options });
  }
};
```

**Resultado**: Erro persistiu, indicando que o problema é mais profundo

### 4. Remoção Temporária do Pacote (✅ Confirmou a Causa)

**Abordagem**: Remover completamente `@repo/mail` das dependências

```json
// apps/web/package.json
// Removido temporariamente: "@repo/mail": "workspace:*"
```

**Resultado**: ✅ **SUCESSO** - O erro do `<Html>` foi resolvido, confirmando que `@repo/mail` era a causa raiz

## Análise Técnica Detalhada

### Por que as Soluções Falharam

1. **Webpack Aliases**: O Next.js ainda processa os arquivos durante a análise de dependências, mesmo com aliases
2. **Imports Dinâmicos**: O bundler ainda precisa resolver as dependências durante o build
3. **TranspilePackages**: O `@repo/mail` estava sendo transpilado junto com outros pacotes

### Estrutura de Dependências Problemática

```
apps/web/
├── package.json (depende de @repo/mail)
├── next.config.ts (transpilePackages inclui @repo/auth)
└── ...

packages/auth/
├── auth.ts (importa sendEmail de @repo/mail)
└── ...

packages/mail/
├── emails/*.tsx (usam @react-email/components)
├── src/components/Wrapper.tsx (importa Html)
└── src/util/templates.ts (usa @react-email/render)
```

## Soluções Recomendadas

### Solução 1: API Route para Emails (Recomendada)

Criar uma API route dedicada para envio de emails, isolando completamente a lógica de email do bundle do cliente:

```typescript
// apps/web/app/api/send-email/route.ts
export async function POST(req: Request) {
  const { sendEmail } = await import('@repo/mail');
  const body = await req.json();
  return await sendEmail(body);
}

// packages/auth/auth.ts
const sendEmailSafely = async (options: any) => {
  const response = await fetch('/api/send-email', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(options)
  });
  return response.json();
};
```

### Solução 2: Wrapper Type-Safe

Criar um wrapper que usa `require` condicional:

```typescript
// packages/mail-wrapper/index.ts
export const sendEmail = process.browser
  ? () => { throw new Error('Not available in browser') }
  : require('@repo/mail').sendEmail;
```

### Solução 3: Configuração Avançada do Webpack

Adicionar regras mais específicas:

```typescript
// apps/web/next.config.ts
webpack: (config) => {
  config.externals = config.externals || {};
  config.externals['@repo/mail'] = 'commonjs @repo/mail';
  return config;
}
```

## Estado Atual

- ✅ **Causa identificada**: `@repo/mail` e seus componentes React Email
- ✅ **Confirmação**: Remoção do pacote resolve o problema
- ❌ **Solução em produção**: Ainda não implementada
- 🔄 **Próximos passos**: Implementar uma das soluções recomendadas

## Arquivos Modificados Durante as Tentativas

1. `apps/web/next.config.ts` - Configurações webpack
2. `packages/auth/auth.ts` - Imports dinâmicos
3. `packages/mail/src/util/email-components.ts` - Utilitário de imports dinâmicos
4. `packages/mail/src/util/templates.ts` - Imports dinâmicos
5. `packages/mail/src/components/Wrapper.tsx` - Imports dinâmicos
6. `packages/mail/emails/*.tsx` - Templates de email
7. `apps/web/package.json` - Dependências

## Comandos Úteis para Debugging

```bash
# Limpar cache
rm -rf apps/web/.next apps/web/tsconfig.tsbuildinfo node_modules/.cache

# Build
pnpm build

# Verificar dependências
grep -r "@react-email" apps/web/
grep -r "@repo/mail" apps/web/
```

## Conclusão

O problema é bem definido e tem solução. A abordagem mais robusta seria implementar a **Solução 1 (API Route)** que isola completamente a lógica de email do bundle do cliente, mantendo a funcionalidade existente sem comprometer a arquitetura atual.
