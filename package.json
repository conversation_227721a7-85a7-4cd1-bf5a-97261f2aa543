{"name": "cakto-members", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write", "db:check": "dotenv -c -e .env -- tsx scripts/check-schema-compatibility.ts", "db:migrate": "dotenv -c -e .env -- tsx scripts/migrate-schema.ts", "db:push": "pnpm --filter database push", "create:user": "dotenv -c -e .env -- tsx scripts/create-user.ts", "create:user:auth": "dotenv -c -e .env -- tsx scripts/create-user-better-auth.ts", "check:passkey": "dotenv -c -e .env -- tsx scripts/fix-passkey-config.ts", "list:users": "dotenv -c -e .env -- tsx scripts/list-users.ts", "associate:product": "dotenv -c -e .env -- tsx scripts/associate-cakto-product-to-course.ts", "test:webhook": "dotenv -c -e .env -- tsx scripts/test-cakto-webhook.ts", "test:integration": "dotenv -c -e .env -- tsx scripts/test-integration.ts", "test:real-webhook": "dotenv -c -e .env -- tsx scripts/test-real-webhook.ts", "test:email": "dotenv -c -e .env.local -- tsx scripts/test-email.ts", "test:webhook:complete": "dotenv -c -e .env.local -- tsx scripts/test-cakto-webhook-complete.ts", "test:webhook:http": "dotenv -c -e .env.local -- tsx scripts/test-webhook-http.ts", "test:sso": "dotenv -c -e .env.local -- tsx scripts/test-sso-login.ts", "test:magic-link": "dotenv -c -e .env.local -- tsx scripts/test-magic-link-welcome.ts", "remove:user-access": "dotenv -c -e .env.local -- tsx scripts/remove-user-access.ts", "create:user:account": "dotenv -c -e .env.local -- tsx scripts/create-user-account.ts", "remove:access": "dotenv -c -e .env.local -- tsx scripts/remove-course-access.ts"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.15.30", "dotenv-cli": "^8.0.0", "turbo": "^2.5.4", "typescript": "5.8.3"}, "dependencies": {"decimal.js": "^10.6.0"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}}