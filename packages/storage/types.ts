export type CreateBucketHandler = (
	name: string,
	options?: {
		public?: boolean;
	},
) => Promise<void>;

export type GetSignedUploadUrlHandler = (
	path: string,
	options: {
		bucket: string;
		contentType?: string;
	},
) => Promise<string>;

export type GetSignedUrlHander = (
	path: string,
	options: {
		bucket: string;
		expiresIn?: number;
	},
) => Promise<string>;

export type UploadFileHandler = (
	path: string,
	file: Buffer,
	options: {
		bucket: string;
		contentType?: string;
	},
) => Promise<void>;
