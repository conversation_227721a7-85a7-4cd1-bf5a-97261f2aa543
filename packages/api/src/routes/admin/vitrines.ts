import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const querySchema = z.object({
	limit: z.string().optional().default("10"),
	offset: z.string().optional().default("0"),
	query: z.string().optional().default(""),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional(),
	visibility: z.enum(["PUBLIC", "PRIVATE"]).optional(),
	organizationSlug: z.string().optional(),
});

export const vitrineRouter = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.get("/vitrines", validator("query", querySchema), async (c) => {
		try {
			const { limit, offset, query, status, visibility, organizationSlug } = c.req.valid("query");
			const limitNum = parseInt(limit);
			const offsetNum = parseInt(offset);

			// Build where clause
			const whereClause: any = {};

			// Add search query
			if (query) {
				whereClause.OR = [
					{ title: { contains: query, mode: "insensitive" as const } },
					{ description: { contains: query, mode: "insensitive" as const } },
					{
						organization: {
							name: { contains: query, mode: "insensitive" as const },
						},
					},
				];
			}

			// Add status filter
			if (status) {
				whereClause.status = status;
			}

			// Add visibility filter
			if (visibility) {
				whereClause.visibility = visibility;
			}

			// Add organization filter
			if (organizationSlug) {
				whereClause.organization = {
					slug: organizationSlug,
				};
			}

			const [vitrines, total, organizations, stats] = await Promise.all([
				db.vitrine.findMany({
					where: whereClause,
					include: {
						organization: {
							select: {
								id: true,
								name: true,
								slug: true,
							},
						},
						creator: {
							select: {
								id: true,
								name: true,
								email: true,
							},
						},
						_count: {
							select: {
								sections: true,
								views: true,
							},
						},
					},
					orderBy: { createdAt: "desc" },
					skip: offsetNum,
					take: limitNum,
				}),
				db.vitrine.count({ where: whereClause }),
				// Get organizations with vitrine counts
				db.organization.findMany({
					select: {
						id: true,
						name: true,
						slug: true,
						_count: {
							select: {
								vitrines: true,
							},
						},
					},
					orderBy: { name: "asc" },
				}),
				// Get overall stats
				Promise.all([
					db.vitrine.count(),
					db.vitrine.count({ where: { status: "PUBLISHED" } }),
					db.vitrineView.count(),
					// Calculate revenue (placeholder - would need actual purchase data)
					Promise.resolve(0),
				]),
			]);

			const formattedVitrines = vitrines.map((vitrine) => ({
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organization: vitrine.organization,
				creator: vitrine.creator,
				sectionsCount: vitrine._count.sections,
				viewsCount: vitrine._count.views,
			}));

			const formattedOrganizations = organizations.map((org) => ({
				id: org.id,
				name: org.name,
				slug: org.slug,
				vitrinesCount: org._count.vitrines,
			}));

			const [totalVitrines, publishedVitrines, totalViews, totalRevenue] = stats;

			return c.json({
				vitrines: formattedVitrines,
				organizations: formattedOrganizations,
				stats: {
					total: totalVitrines,
					published: publishedVitrines,
					views: totalViews,
					revenue: totalRevenue,
				},
				total,
			});
		} catch (error) {
			console.error("Error fetching admin vitrines:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
