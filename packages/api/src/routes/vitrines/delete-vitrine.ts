import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	id: z.string(),
});

const querySchema = z.object({
	organizationId: z.string(),
});

export const deleteVitrine = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.delete("/:id", validator("param", paramsSchema), validator("query", querySchema), async (c) => {
		try {
			const user = c.get("user");
			const { id } = c.req.valid("param");
			const { organizationId } = c.req.valid("query");

			// Verificar se o usuário tem permissão na organização
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can delete vitrines" }, 403);
			}

			// Verificar se a vitrine existe e pertence à organização
			const existingVitrine = await db.vitrine.findFirst({
				where: {
					id,
					organizationId,
				},
				include: {
					sections: {
						include: {
							purchases: true,
						},
					},
				},
			});

			if (!existingVitrine) {
				return c.json({ error: "Vitrine not found" }, 404);
			}

			// Verificar se há compras ativas
			const activePurchases = existingVitrine.sections.some((section) =>
				section.purchases.some((purchase) => purchase.status === "COMPLETED"),
			);

			if (activePurchases) {
				return c.json(
					{
						error: "Cannot delete vitrine with active purchases. Archive it instead.",
						suggestion: "Use PUT /vitrines/:id with status: 'ARCHIVED' to archive this vitrine",
					},
					400,
				);
			}

			// Deletar vitrine (cascade irá deletar seções, cursos relacionados e views)
			await db.vitrine.delete({
				where: { id },
			});

			return c.json({ message: "Vitrine deleted successfully" });
		} catch (error) {
			console.error("Error deleting vitrine:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});