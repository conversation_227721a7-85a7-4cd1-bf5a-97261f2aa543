import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	id: z.string(),
});

const updateVitrineSchema = z.object({
	title: z.string().min(1, "Title is required"),
	description: z.string().optional().nullable(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional(),
	visibility: z.enum(["PUBLIC", "PRIVATE"]).optional(),
	bannerImage: z.string().optional().or(z.literal("")).nullable(),
	isDefault: z.boolean().optional(),
	organizationId: z.string().min(1, "Organization ID is required"),
	sections: z
		.array(
			z.object({
				title: z.string().min(1, "Section title is required"),
				subtitle: z.string().optional().nullable(),
				description: z.string().optional().nullable(),
				position: z.number().int().min(0),
				isLocked: z.boolean().default(false),
				requiresPurchase: z.boolean().default(false),
				checkoutUrl: z.string().optional().or(z.literal("")).nullable(),
				webhookUrl: z.string().optional().or(z.literal("")).nullable(),
				price: z.number().positive().optional().nullable(),
				originalPrice: z.number().positive().optional().nullable(),
				accessType: z.enum(["FREE", "PAID", "MEMBER_ONLY"]).default("FREE"),
				visibility: z.enum(["PUBLIC", "PRIVATE"]).default("PUBLIC"),
				courses: z
					.array(
						z.object({
							courseId: z.string(),
							position: z.number().int().min(0),
						}),
					)
					.default([]),
			}),
		)
		.optional(),
});

export const updateVitrine = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.put("/:id", validator("param", paramsSchema), validator("json", updateVitrineSchema), async (c) => {
		try {
			const user = c.get("user");
			const { id } = c.req.valid("param");
			const data = c.req.valid("json");
			const { organizationId, sections, ...updateData } = data;

			// Verificar se o usuário tem permissão na organização
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can update vitrines" }, 403);
			}

			// Verificar se a vitrine existe e pertence à organização
			const existingVitrine = await db.vitrine.findFirst({
				where: {
					id,
					organizationId,
				},
			});

			if (!existingVitrine) {
				return c.json({ error: "Vitrine not found" }, 404);
			}

			// If this vitrine is being set as default, unset any existing default vitrines
			if (data.isDefault) {
				await db.vitrine.updateMany({
					where: {
						organizationId,
						isDefault: true,
						id: { not: id }, // Don't unset the current vitrine being updated
					},
					data: {
						isDefault: false,
					},
				});
			}

			// Handle sections update if provided
			if (sections) {
				// Validate courses exist in the organization
				const courseIds = sections.flatMap((section) => section.courses.map((c) => c.courseId));
				if (courseIds.length > 0) {
					const existingCourses = await db.courses.findMany({
						where: {
							id: { in: courseIds },
							organizationId,
						},
						select: { id: true },
					});

					const existingCourseIds = existingCourses.map((c) => c.id);
					const invalidCourseIds = courseIds.filter((id) => !existingCourseIds.includes(id));

					if (invalidCourseIds.length > 0) {
						return c.json(
							{
								error: "Some courses do not exist or do not belong to this organization",
								invalidCourseIds,
							},
							400,
						);
					}
				}

				// Delete existing sections and their courses
				await db.vitrineSectionCourse.deleteMany({
					where: {
						section: {
							vitrineId: id,
						},
					},
				});

				await db.vitrineSection.deleteMany({
					where: {
						vitrineId: id,
					},
				});

				// Create new sections
				await db.vitrineSection.createMany({
					data: sections.map((section) => ({
						vitrineId: id,
						title: section.title,
						subtitle: section.subtitle,
						description: section.description,
						position: section.position,
						isLocked: section.isLocked,
						requiresPurchase: section.requiresPurchase,
						checkoutUrl: section.checkoutUrl,
						webhookUrl: section.webhookUrl,
						price: section.price,
						originalPrice: section.originalPrice,
						accessType: section.accessType,
						visibility: section.visibility,
					})),
				});

				// Get the created sections to add courses
				const createdSections = await db.vitrineSection.findMany({
					where: { vitrineId: id },
					orderBy: { position: "asc" },
				});

				// Create section courses
				for (let i = 0; i < sections.length; i++) {
					const section = sections[i];
					const createdSection = createdSections[i];

					if (section.courses.length > 0) {
						await db.vitrineSectionCourse.createMany({
							data: section.courses.map((course) => ({
								sectionId: createdSection.id,
								courseId: course.courseId,
								position: course.position,
							})),
						});
					}
				}
			}

			// Atualizar vitrine
			const vitrine = await db.vitrine.update({
				where: { id },
				data: updateData,
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
										},
									},
								},
							},
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});

			const response = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organizationId: vitrine.organizationId,
				createdBy: vitrine.createdBy,
				creator: vitrine.creator,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					webhookUrl: section.webhookUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses,
				})),
			};

			return c.json(response);
		} catch (error) {
			console.error("Error updating vitrine:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
