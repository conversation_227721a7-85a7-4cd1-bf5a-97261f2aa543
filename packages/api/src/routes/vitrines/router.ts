import { Hono } from "hono";
import { getVitrines } from "./get-vitrines";
import { getVitrine } from "./get-vitrine";
import { getAdminVitrine } from "./get-admin-vitrine";
import { createVitrine } from "./create-vitrine";
import { updateVitrine } from "./update-vitrine";
import { deleteVitrine } from "./delete-vitrine";
import { getPublicVitrines } from "./get-public-vitrines";
import { getPublicVitrine } from "./get-public-vitrine";
import { getOrganizationVitrines } from "./get-organization-vitrines";
import { getOrganizationVitrine } from "./get-organization-vitrine";
import { uploadVitrineBanner } from "./upload-banner";

export const vitrinesRouter = new Hono()
	.basePath("/vitrines")
	.route("/", getVitrines)
	.route("/", getVitrine)
	.route("/", getAdminVitrine)
	.route("/", createVitrine)
	.route("/", updateVitrine)
	.route("/", deleteVitrine)
	.route("/public", getPublicVitrines)
	.route("/public", getPublicVitrine)
	.route("/", getOrganizationVitrines)
	.route("/", getOrganizationVitrine)
	.route("/", uploadVitrineBanner);
