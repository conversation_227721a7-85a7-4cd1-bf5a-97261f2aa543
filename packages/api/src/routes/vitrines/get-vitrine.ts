import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
	id: z.string(),
});

const querySchema = z.object({
	organizationId: z.string(),
});

export const getVitrine = new Hono()
	.use(authMiddleware)
	.get("/:id", validator("param", paramsSchema), validator("query", querySchema), async (c) => {
		try {
			const user = c.get("user");
			const { id } = c.req.valid("param");
			const { organizationId } = c.req.valid("query");

			// Verificar se o usuário tem permissão na organização
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			const vitrine = await db.vitrine.findFirst({
				where: {
					id,
					organizationId,
				},
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
											community: true,
											link: true,
											createdAt: true,
											updatedAt: true,
										},
									},
								},
							},
							purchases: {
								select: {
									id: true,
									status: true,
									amount: true,
									createdAt: true,
								},
							},
						},
					},
					views: {
						select: {
							id: true,
							createdAt: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
							logo: true,
						},
					},
				},
			});

			if (!vitrine) {
				return c.json({ error: "Vitrine not found" }, 404);
			}

			const totalPurchases = vitrine.sections.reduce(
				(acc, section) => acc + section.purchases.length,
				0,
			);
			const totalRevenue = vitrine.sections.reduce(
				(acc, section) =>
					acc +
					section.purchases
						.filter((p) => p.status === "COMPLETED")
						.reduce((sum, p) => sum + Number(p.amount), 0),
				0,
			);

			const formattedVitrine = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organizationId: vitrine.organizationId,
				createdBy: vitrine.createdBy,
				creator: vitrine.creator,
				organization: vitrine.organization,
				stats: {
					views: vitrine.views.length,
					enrollments: totalPurchases,
					revenue: totalRevenue,
					sections: vitrine.sections.length,
					courses: vitrine.sections.reduce((acc, s) => acc + s.courses.length, 0),
				},
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					webhookUrl: section.webhookUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					stats: {
						courses: section.courses.length,
						purchases: section.purchases.length,
						revenue: section.purchases
							.filter((p) => p.status === "COMPLETED")
							.reduce((sum, p) => sum + Number(p.amount), 0),
					},
					courses: section.courses.map((courseRelation) => ({
						id: courseRelation.course.id,
						name: courseRelation.course.name,
						logo: courseRelation.course.logo,
						community: courseRelation.course.community,
						link: courseRelation.course.link,
						createdAt: courseRelation.course.createdAt.toISOString(),
						updatedAt: courseRelation.course.updatedAt.toISOString(),
						position: courseRelation.position,
					})),
				})),
			};

			return c.json(formattedVitrine);
		} catch (error) {
			console.error("Error fetching vitrine:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});