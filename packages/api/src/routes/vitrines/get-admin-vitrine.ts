import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	vitrineId: z.string(),
});

export const getAdminVitrine = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.get("/admin/:vitrineId", validator("param", paramsSchema), async (c) => {
		try {
			const user = c.get("user");
			const { vitrineId } = c.req.valid("param");

			console.log('🔍 Admin Vitrine API - Starting vitrine fetch:', { vitrineId, userId: user.id, userRole: user.role });

			// Fetch vitrine with all related data for admin editing
			const vitrine = await db.vitrine.findUnique({
				where: { id: vitrineId },
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
											community: true,
										},
									},
								},
							},
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});

			if (!vitrine) {
				console.log('❌ Admin Vitrine API - Vitrine not found:', vitrineId);
				return c.json({ error: 'Vitrine not found' }, 404);
			}

			// Format the vitrine data for admin editing
			const formattedVitrine = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				bannerImage: vitrine.bannerImage,
				status: vitrine.status,
				visibility: vitrine.visibility,
				isDefault: vitrine.isDefault,
				organizationId: vitrine.organizationId,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organization: vitrine.organization,
				creator: vitrine.creator,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					webhookUrl: section.webhookUrl,
					price: section.price ? Number(section.price) : undefined,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : undefined,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses.map((sectionCourse) => ({
						courseId: sectionCourse.courseId,
						position: sectionCourse.position,
						course: sectionCourse.course,
					})),
				})),
			};

			console.log('✅ Admin Vitrine API - Vitrine data formatted successfully');
			return c.json(formattedVitrine);
		} catch (error) {
			console.error('❌ Admin Vitrine API - Error fetching vitrine:', error);
			return c.json({ error: 'Failed to fetch vitrine' }, 500);
		}
	});
