import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { authMiddleware } from '../../middleware/auth'
import { db } from "@repo/database";
import { getLessonComments } from '../courses/get-lesson-comments';
import { getLessonFiles } from '../courses/get-lesson-files';
import { watchLesson } from '../courses/watch-lesson';
import { addLessonComment } from '../courses/add-lesson-comment';
import { replyLessonComment } from '../courses/reply-lesson-comment';
import { downloadLessonFile } from '../courses/download-lesson-file';
import { lessonFiles } from './lesson-files';
import { updateLesson } from './update-lesson';

const lessonParamsSchema = z.object({
  lessonId: z.string(),
})

const commentParamsSchema = z.object({
  lessonId: z.string(),
  commentId: z.string().transform(Number),
})

const fileParamsSchema = z.object({
  lessonId: z.string(),
  fileId: z.string().transform(Number),
})

export const lessonsRouter = new Hono()
  .basePath('/lessons')
  .use(authMiddleware)

  // Get lesson comments
  .route("/:lessonId/comments", getLessonComments)

  // Get lesson files
  .route("/:lessonId/files", getLessonFiles)

  // Watch lesson (mark as completed)
  .route("/:lessonId/watch", watchLesson)

  // Toggle lesson completion
  .post('/:lessonId/completion', validator('param', lessonParamsSchema), async (c) => {
    try {
      const user = c.get('user')
      const { lessonId } = c.req.valid('param')
      const { isCompleted } = await c.req.json()

      console.log('🔍 Lessons API - Toggle completion:', { lessonId, isCompleted, userId: user.id })

      // Check if lesson exists
      const lesson = await db.lessons.findUnique({
        where: { id: lessonId },
        include: {
          module: {
            include: {
              courseModules: {
                include: {
                  course: {
                    include: {
                      organization: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!lesson) {
        console.log('❌ Lessons API - Lesson not found:', lessonId)
        return c.json({ error: 'Lesson not found' }, 404)
      }

      // Check if user has access to the course
      const course = lesson.module.courseModules[0]?.course
      if (!course) {
        console.log('❌ Lessons API - Course not found for lesson')
        return c.json({ error: 'Course not found' }, 404)
      }

      // Check if user is admin or has access to the course
      if (user.role !== 'admin') {
        const userCourseAccess = await db.userCourses.findFirst({
          where: {
            userId: user.id,
            courseId: course.id,
          },
        })

        if (!userCourseAccess) {
          console.log('❌ Lessons API - User does not have access to this course')
          return c.json({ error: 'Access denied' }, 403)
        }
      }

      // Update or create user watched lesson
      const userWatchedLesson = await db.userWatchedLessons.upsert({
        where: {
          userId_lessonId: {
            userId: user.id,
            lessonId: lessonId,
          },
        },
        update: {
          isCompleted: isCompleted,
        },
        create: {
          userId: user.id,
          lessonId: lessonId,
          isCompleted: isCompleted,
        },
      })

      console.log('✅ Lessons API - Lesson completion toggled successfully')
      return c.json({ success: true, userWatchedLesson })
    } catch (error) {
      console.error('❌ Lessons API - Error toggling lesson completion:', error)
      return c.json({ error: 'Failed to toggle lesson completion' }, 500)
    }
  })

  // Rate lesson
  .post('/:lessonId/rating', validator('param', lessonParamsSchema), async (c) => {
    try {
      const user = c.get('user')
      const { lessonId } = c.req.valid('param')
      const { rating } = await c.req.json()

      console.log('🔍 Lessons API - Rate lesson:', { lessonId, rating, userId: user.id })

      // Validate rating
      if (rating < 1 || rating > 5) {
        return c.json({ error: 'Rating must be between 1 and 5' }, 400)
      }

      // Check if lesson exists
      const lesson = await db.lessons.findUnique({
        where: { id: lessonId },
        include: {
          module: {
            include: {
              courseModules: {
                include: {
                  course: {
                    include: {
                      organization: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!lesson) {
        console.log('❌ Lessons API - Lesson not found:', lessonId)
        return c.json({ error: 'Lesson not found' }, 404)
      }

      // Check if user has access to the course
      const course = lesson.module.courseModules[0]?.course
      if (!course) {
        console.log('❌ Lessons API - Course not found for lesson')
        return c.json({ error: 'Course not found' }, 404)
      }

      // Check if user is admin or has access to the course
      if (user.role !== 'admin') {
        const userCourseAccess = await db.userCourses.findFirst({
          where: {
            userId: user.id,
            courseId: course.id,
          },
        })

        if (!userCourseAccess) {
          console.log('❌ Lessons API - User does not have access to this course')
          return c.json({ error: 'Access denied' }, 403)
        }
      }

      // Update or create user watched lesson with rating
      const userWatchedLesson = await db.userWatchedLessons.upsert({
        where: {
          userId_lessonId: {
            userId: user.id,
            lessonId: lessonId,
          },
        },
        update: {
          rating: rating,
        },
        create: {
          userId: user.id,
          lessonId: lessonId,
          rating: rating,
        },
      })

      console.log('✅ Lessons API - Lesson rated successfully')
      return c.json({ success: true, userWatchedLesson })
    } catch (error) {
      console.error('❌ Lessons API - Error rating lesson:', error)
      return c.json({ error: 'Failed to rate lesson' }, 500)
    }
  })

  // Add lesson comment
  .route("/:lessonId/comments", addLessonComment)

  // Reply to lesson comment
  .route("/:lessonId/comments/:commentId/replies", replyLessonComment)

  // Download lesson file
  .route("/:lessonId/files/:fileId/download", downloadLessonFile)

  // Lesson files management (admin only)
  .route("/", lessonFiles)

  // Update lesson (admin only)
  .route("/", updateLesson)

  .get('*', async (c) => {
    console.log('🔍 Lessons API - Catch-all route hit for path:', c.req.path)
    return c.json({ error: 'Route not found', path: c.req.path }, 404)
  })
