import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
	organizationId: z.string(),
});

export const getOrganizationCourses = new Hono()
	.use(authMiddleware)
	.get("/organization", validator("query", querySchema), async (c) => {
		try {
			const user = c.get("user");
			const { organizationId } = c.req.valid("query");

			// Verify user has permission in the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			// Get all courses for the organization
			const courses = await db.courses.findMany({
				where: {
					organizationId,
				},
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					courseModules: {
						include: {
							module: {
								select: {
									id: true,
									name: true,
									position: true,
									cover: true,
								},
							},
						},
						orderBy: { module: { position: "asc" } },
					},
					userCourses: {
						select: {
							userId: true,
						},
					},
				},
				orderBy: { createdAt: "desc" },
			});

			// Transform the data to match the expected format
			const transformedCourses = courses.map((course) => ({
				id: course.id,
				name: course.name,
				logo: course.logo,
				community: course.community,
				description: course.community || `Curso de ${course.name}`, // Use community as description fallback
				studentsCount: course.userCourses.length,
				duration: `${course.courseModules.length} módulos`, // Calculate duration based on modules
				createdAt: course.createdAt.toISOString(),
				updatedAt: course.updatedAt.toISOString(),
				organizationId: course.organizationId,
				createdBy: course.createdBy,
				organization: course.organization,
				creator: course.creator,
				modules: course.courseModules.map((cm) => ({
					id: cm.module.id,
					name: cm.module.name,
					position: cm.module.position,
					cover: cm.module.cover,
				})),
			}));

			return c.json(transformedCourses);
		} catch (error) {
			console.error("Error fetching organization courses:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
