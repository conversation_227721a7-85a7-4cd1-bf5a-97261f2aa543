import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	courseId: z.string(),
});

export const deleteCourse = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.delete("/:courseId", validator("param", paramsSchema), async (c) => {
		try {
			const user = c.get("user");
			const { courseId } = c.req.valid("param");

			// Check if course exists
			const course = await db.courses.findUnique({
				where: { id: courseId },
				include: {
					organization: {
						select: {
							id: true,
							name: true,
						},
					},
				},
			});

			if (!course) {
				return c.json({ error: "Course not found" }, 404);
			}

			// Check if user has permission (admin or member of organization)
			if (user.role !== "admin") {
				const userMembership = await db.member.findFirst({
					where: {
						userId: user.id,
						organizationId: course.organizationId,
					},
				});

				if (!userMembership || userMembership.role === "member") {
					return c.json({ error: "Insufficient permissions" }, 403);
				}
			}

			// Delete in proper order to handle foreign key constraints
			// 1. Delete user courses
			await db.userCourses.deleteMany({
				where: { courseId },
			});

			// 2. Delete course modules (this will handle the relationship)
			await db.courseModules.deleteMany({
				where: { courseId },
			});

			// 3. Delete vitrine section courses
			await db.vitrineSectionCourse.deleteMany({
				where: { courseId },
			});

			// 4. Delete course banner buttons
			const courseBanners = await db.courseBanner.findMany({
				where: { courseId },
				select: { id: true },
			});

			for (const banner of courseBanners) {
				await db.courseBannerButton.deleteMany({
					where: { bannerId: banner.id },
				});
			}

			// 5. Delete course banners
			await db.courseBanner.deleteMany({
				where: { courseId },
			});

			// 6. Delete course products
			await db.courseProduct.deleteMany({
				where: { courseId },
			});

			// 7. Finally delete the course
			await db.courses.delete({
				where: { id: courseId },
			});

			return c.json({
				success: true,
				message: `Course "${course.name}" deleted successfully`,
			});
		} catch (error) {
			console.error("Error deleting course:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
