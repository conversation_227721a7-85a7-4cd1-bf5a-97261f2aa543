import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createModuleSchema = z.object({
	name: z.string().min(1, "Module name is required"),
	description: z.string().optional(),
	position: z.number().int().min(0),
	cover: z.string().optional(),
});

const courseParamsSchema = z.object({
	courseId: z.string(),
});

export const createModule = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.post("/courses/:courseId/modules", validator("param", courseParamsSchema), validator("json", createModuleSchema), async (c) => {
		try {
			const user = c.get("user");
			const { courseId } = c.req.valid("param");
			const moduleData = c.req.valid("json");

			// Verify course exists and user has access
			const course = await db.courses.findUnique({
				where: { id: courseId },
				select: {
					id: true,
					organizationId: true,
					createdBy: true,
				},
			});

			if (!course) {
				return c.json({ error: "Course not found" }, 404);
			}

			// Check user permission in organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: course.organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can create modules" }, 403);
			}

			// Create module
			const module = await db.modules.create({
				data: {
					...moduleData,
				},
			});

			// Create course-module relationship
			await db.courseModules.create({
				data: {
					courseId: course.id,
					moduleId: module.id,
				},
			});

			return c.json({ module }, 201);
		} catch (error) {
			console.error("Error creating module:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
