import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { getSignedUploadUrl } from "@repo/storage";
import { db } from "@repo/database";
import type { Session } from "@repo/auth";

const uploadFileRequestSchema = z.object({
	fileName: z.string().min(1, "File name is required"),
	fileType: z.enum(["course-materials", "lesson-files", "module-files"]),
	organizationId: z.string(),
	lessonId: z.string().optional(),
	moduleId: z.string().optional(),
});

export const uploadFiles = new Hono<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>()
	.post("/files", validator("json", uploadFileRequestSchema), async (c) => {
		try {
			const user = c.get("user");
			const { fileName, fileType, organizationId, lessonId, moduleId } = c.req.valid("json");

			// Check if user is admin
			if (user.role !== "admin") {
				return c.json({ error: "Only admins can upload files" }, 403);
			}

			// Verify user has permission in the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can upload files" }, 403);
			}

			// Generate a unique file path
			const fileExtension = fileName.split('.').pop();
			const timestamp = Date.now();
			const uniqueFileName = `${timestamp}_${Math.random().toString(36).substring(7)}.${fileExtension}`;

			// Create file path based on type
			let filePath: string;
			if (lessonId) {
				filePath = `courses/${organizationId}/lessons/${lessonId}/${uniqueFileName}`;
			} else if (moduleId) {
				filePath = `courses/${organizationId}/modules/${moduleId}/${uniqueFileName}`;
			} else {
				filePath = `courses/${organizationId}/${fileType}/${uniqueFileName}`;
			}

			// Get signed upload URL from storage provider
			const bucket = process.env.S3_BUCKET_NAME;
			if (!bucket) {
				return c.json({ error: "Storage configuration missing" }, 500);
			}

			// Determine content type based on file extension
			const getContentType = (fileName: string) => {
				const ext = fileName.split('.').pop()?.toLowerCase();
				switch (ext) {
					case 'pdf':
						return 'application/pdf';
					case 'doc':
						return 'application/msword';
					case 'docx':
						return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
					case 'xls':
						return 'application/vnd.ms-excel';
					case 'xlsx':
						return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
					case 'ppt':
						return 'application/vnd.ms-powerpoint';
					case 'pptx':
						return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
					case 'txt':
						return 'text/plain';
					case 'zip':
						return 'application/zip';
					case 'rar':
						return 'application/vnd.rar';
					case 'mp4':
						return 'video/mp4';
					case 'mp3':
						return 'audio/mpeg';
					case 'jpg':
					case 'jpeg':
						return 'image/jpeg';
					case 'png':
						return 'image/png';
					case 'gif':
						return 'image/gif';
					default:
						return 'application/octet-stream';
				}
			};

			const uploadUrl = await getSignedUploadUrl(filePath, {
				bucket,
				contentType: getContentType(fileName)
			});

			// Return the upload URL and the final file path
			return c.json({
				uploadUrl,
				filePath,
				publicUrl: `${process.env.CDN_BASE_URL || process.env.S3_ENDPOINT}/${bucket}/${filePath}`,
			});
		} catch (error) {
			console.error("Error generating upload URL:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
