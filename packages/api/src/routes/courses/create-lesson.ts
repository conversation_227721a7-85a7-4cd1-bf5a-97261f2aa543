import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createLessonSchema = z.object({
	name: z.string().min(1, "Lesson name is required"),
	description: z.string().optional(),
	videoUrl: z.string().optional(),
	position: z.number().int().min(0),
	thumbnail: z.string().optional(),
	duration: z.string().optional(),
	externalLink: z.string().optional(),
});

const moduleParamsSchema = z.object({
	moduleId: z.string(),
});

export const createLesson = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.post("/modules/:moduleId/lessons", validator("param", moduleParamsSchema), validator("json", createLessonSchema), async (c) => {
		try {
			const user = c.get("user");
			const { moduleId } = c.req.valid("param");
			const lessonData = c.req.valid("json");

			// Verify module exists and user has access
			const module = await db.modules.findUnique({
				where: { id: moduleId },
				include: {
					courseModules: {
						include: {
							course: {
								select: {
									organizationId: true,
								},
							},
						},
					},
				},
			});

			if (!module) {
				return c.json({ error: "Module not found" }, 404);
			}

			// Check user permission in organization
			const organizationId = module.courseModules[0]?.course.organizationId;
			if (!organizationId) {
				return c.json({ error: "Course organization not found" }, 404);
			}

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can create lessons" }, 403);
			}

			// Create lesson
			const lesson = await db.lessons.create({
				data: {
					...lessonData,
					moduleId,
				},
			});

			return c.json({ lesson }, 201);
		} catch (error) {
			console.error("Error creating lesson:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
