import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

const paramsSchema = z.object({
  lessonId: z.string(),
})

export const getLessonComments = new Hono()
  .get('/lesson/:lessonId/comments', validator('param', paramsSchema), async (c) => {
    try {
      const { lessonId } = c.req.valid('param')

      // Replace with Prisma query
      const comments = await db.lessonComments.findMany({
        where: { lessonId },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          lessonCommentReplies: {
            include: {
              user: { select: { id: true, name: true, email: true } }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const formattedComments = comments.map(comment => ({
        id: comment.id.toString(),
        content: comment.comment,
        createdAt: comment.createdAt.toISOString(),
        lessonId: comment.lessonId,
        user: comment.user,
        replies: comment.lessonCommentReplies.map(reply => ({
          id: reply.id.toString(),
          content: reply.replyComment,
          createdAt: reply.createdAt.toISOString(),
          commentId: comment.id.toString(),
          user: reply.user
        }))
      }));

      return c.json({ comments })
    } catch (error) {
      return c.json({ error: 'Failed to fetch lesson comments' }, 500)
    }
  })