import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { getSignedUploadUrl, uploadFile, getSignedUrl } from "@repo/storage";
import { db } from "@repo/database";
import type { Session } from "@repo/auth";

const uploadRequestSchema = z.object({
	fileName: z.string().min(1, "File name is required"),
	fileType: z.enum(["course-logo", "module-cover", "lesson-cover"]),
	organizationId: z.string(),
	fileData: z.string().optional(), // Base64 encoded file data
});

const accessRequestSchema = z.object({
	filePath: z.string().min(1, "File path is required"),
	organizationId: z.string(),
});

export const uploadImage = new Hono<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>()
	.post("/upload-url", validator("json", uploadRequestSchema), async (c) => {
		try {
			const user = c.get("user");
			const { fileName, fileType, organizationId, fileData } = c.req.valid("json");

			// Check if user is admin
			if (user.role !== "admin") {
				return c.json({ error: "Only admins can upload images" }, 403);
			}

			// Verify user has permission in the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can upload images" }, 403);
			}

			// Generate a unique file path
			const fileExtension = fileName.split('.').pop();
			const timestamp = Date.now();
			const uniqueFileName = `${timestamp}_${Math.random().toString(36).substring(7)}.${fileExtension}`;
			const filePath = `courses/${organizationId}/${fileType}/${uniqueFileName}`;

			// Get signed upload URL from storage provider
			const bucket = process.env.S3_BUCKET_NAME;
			if (!bucket) {
				return c.json({ error: "Storage configuration missing" }, 500);
			}

			// Determine content type based on file extension
			const getContentType = (fileName: string) => {
				const ext = fileName.split('.').pop()?.toLowerCase();
				switch (ext) {
					case 'jpg':
					case 'jpeg':
						return 'image/jpeg';
					case 'png':
						return 'image/png';
					case 'gif':
						return 'image/gif';
					case 'webp':
						return 'image/webp';
					case 'svg':
						return 'image/svg+xml';
					default:
						return 'image/jpeg';
				}
			};

			const contentType = getContentType(fileName);

			// If fileData is provided, upload directly through the API
			if (fileData) {
				try {
					// Convert base64 to buffer
					const buffer = Buffer.from(fileData.split(',')[1], 'base64');

					await uploadFile(filePath, buffer, {
						bucket,
						contentType,
					});

					// Generate a signed URL for accessing the uploaded file
					const accessUrl = await getSignedUrl(filePath, {
						bucket,
						expiresIn: 3600 * 24 * 7, // 7 days
					});

					return c.json({
						filePath,
						publicUrl: accessUrl,
					});
				} catch (uploadError) {
					console.error("Error uploading file:", uploadError);
					return c.json({ error: "Error uploading file" }, 500);
				}
			}

			// Otherwise, return signed URL for direct upload
			const uploadUrl = await getSignedUploadUrl(filePath, {
				bucket,
				contentType
			});

			// Return the upload URL and the final file path
			return c.json({
				uploadUrl,
				filePath,
				publicUrl: `${process.env.CDN_BASE_URL || process.env.S3_ENDPOINT}/${bucket}/${filePath}`,
			});
		} catch (error) {
			console.error("Error generating upload URL:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	})
	.post("/access-url", validator("json", accessRequestSchema), async (c) => {
		try {
			const user = c.get("user");
			const { filePath, organizationId } = c.req.valid("json");

			// Verify user has permission in the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			// Verify the file path belongs to this organization
			if (!filePath.includes(`courses/${organizationId}/`)) {
				return c.json({ error: "Access denied to this file" }, 403);
			}

			const bucket = process.env.S3_BUCKET_NAME;
			if (!bucket) {
				return c.json({ error: "Storage configuration missing" }, 500);
			}

			// Generate a signed URL for accessing the file
			const accessUrl = await getSignedUrl(filePath, {
				bucket,
				expiresIn: 3600 * 24 * 7, // 7 days
			});

			return c.json({
				accessUrl,
			});
		} catch (error) {
			console.error("Error generating access URL:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
