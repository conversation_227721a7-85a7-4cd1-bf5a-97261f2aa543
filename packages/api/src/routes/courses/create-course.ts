import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createCourseSchema = z.object({
	name: z.string().min(1, "Course name is required"),
	description: z.string().optional(),
	organizationId: z.string(),
	community: z.string().optional(),
	link: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
		message: "Link must be a valid URL if provided",
	}),
	logo: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
		message: "Logo must be a valid URL if provided",
	}),
	modules: z
		.array(
			z.object({
				name: z.string().min(1, "Module name is required"),
				position: z.number().int().min(0),
				cover: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
					message: "Cover must be a valid URL if provided",
				}),
			}),
		)
		.default([]),
});

export const createCourse = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.post("/", async (c) => {
		try {
			const user = c.get("user");

			// Parse and validate the request body
			let data;
			try {
				const rawData = await c.req.json();
				data = createCourseSchema.parse(rawData);
			} catch (error) {
				console.error("Validation error:", error);
				if (error instanceof z.ZodError) {
					return c.json({
						error: "Validation failed",
						details: error.errors.map(e => ({
							field: e.path.join('.'),
							message: e.message
						}))
					}, 400);
				}
				return c.json({ error: "Invalid request data" }, 400);
			}
			const { modules, organizationId, ...courseData } = data;

			// Verify user has permission in the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can create courses" }, 403);
			}

			// Create course with modules
			const course = await db.courses.create({
				data: {
					...courseData,
					organizationId,
					createdBy: user.id,
				},
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});

			// Create modules if provided
			if (modules.length > 0) {
				// First create the modules
				const createdModules = await Promise.all(
					modules.map((moduleData) =>
						db.modules.create({
							data: {
								name: moduleData.name,
								position: moduleData.position,
								cover: moduleData.cover,
							},
						})
					)
				);

				// Then create course-module relationships
				await Promise.all(
					createdModules.map((module) =>
						db.courseModules.create({
							data: {
								courseId: course.id,
								moduleId: module.id,
							},
						})
					)
				);
			}

			// Fetch the complete course with modules
			const completeCourse = await db.courses.findUnique({
				where: { id: course.id },
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					courseModules: {
						include: {
							module: {
								select: {
									id: true,
									name: true,
									position: true,
									cover: true,
								},
							},
						},
						orderBy: { module: { position: "asc" } },
					},
				},
			});

			const response = {
				id: completeCourse!.id,
				name: completeCourse!.name,
				description: completeCourse!.description,
				community: completeCourse!.community,
				link: completeCourse!.link,
				logo: completeCourse!.logo,
				createdAt: completeCourse!.createdAt.toISOString(),
				updatedAt: completeCourse!.updatedAt.toISOString(),
				organizationId: completeCourse!.organizationId,
				createdBy: completeCourse!.createdBy,
				organization: completeCourse!.organization,
				creator: completeCourse!.creator,
				modules: completeCourse!.courseModules.map((cm) => ({
					id: cm.module.id,
					name: cm.module.name,
					position: cm.module.position,
					cover: cm.module.cover,
				})),
			};

			return c.json(response, 201);
		} catch (error) {
			console.error("Error creating course:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
