import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { authMiddleware } from '../../middleware/auth'
import { db } from "@repo/database";
import { createCourse } from './create-course';
import { getOrganizationCourses } from './get-organization-courses';
import { uploadImage } from './upload-image';
import { uploadFiles } from './upload-files';
import { getAdminCourses } from './get-admin-courses';
import { getAdminCourse } from './get-admin-course';
import { updateCourse } from './update-course';
import { deleteCourse } from './delete-course';
import { createLesson } from './create-lesson';
import { createModule } from './create-module';

const courseParamsSchema = z.object({
  courseId: z.string(),
})

const courseQuerySchema = z.object({
  organizationSlug: z.string(),
})

export const coursesRouter = new Hono()
  .basePath('/courses')
  .get('/test', async (c) => {
    console.log('🔍 Course API - Test route hit!')
    return c.json({ message: 'Test route working' })
  })
  .get('/simple-test', async (c) => {
    console.log('🔍 Course API - Simple test route hit!')
    return c.json({ message: 'Simple test route working' })
  })
  .use(authMiddleware)
  .get('/auth-test', async (c) => {
    console.log('🔍 Course API - Auth test route hit!')
    const user = c.get('user')
    return c.json({ message: 'Auth test route working', user: { id: user.id, email: user.email } })
  })
  .route("/", createCourse)
  .route("/", getOrganizationCourses)
  .route("/", uploadImage)
  .route("/", uploadFiles)
  .route("/", getAdminCourses)
  .route("/", getAdminCourse)
  .route("/", updateCourse)
  .route("/", deleteCourse)
  .route("/", createLesson)
  .route("/", createModule)
  .get('/course/:courseId', validator('param', courseParamsSchema), validator('query', courseQuerySchema), async (c) => {
    try {
      const user = c.get('user')
      const { courseId } = c.req.valid('param')
      const { organizationSlug } = c.req.valid('query')

      console.log('🔍 Course API - Starting course fetch:', { courseId, organizationSlug, userId: user.id, userRole: user.role })

      // Find organization by slug
      const organization = await db.organization.findFirst({
        where: { slug: organizationSlug },
      })

      console.log('🔍 Course API - Organization found:', organization ? { id: organization.id, name: organization.name } : 'NOT FOUND')

      if (!organization) {
        console.log('❌ Course API - Organization not found for slug:', organizationSlug)
        return c.json({ error: 'Organization not found' }, 404)
      }

      // Check if user is admin - admins should have access to all courses
      if (user.role === 'admin') {
        console.log('✅ Course API - User is admin, bypassing membership check')
      } else {
        // Verify user is member of organization
        const userMembership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId: organization.id,
          },
        })

        console.log('🔍 Course API - User membership:', userMembership ? { role: userMembership.role, organizationId: userMembership.organizationId } : 'NOT FOUND')

        if (!userMembership) {
          console.log('❌ Course API - User is not a member of organization')
          return c.json({ error: 'Access denied' }, 403)
        }
      }

      // Buscar o curso com seus módulos e lições
      const course = await db.courses.findFirst({
        where: {
          id: courseId,
          organizationId: organization.id
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          courseModules: {
            include: {
              module: {
                include: {
                  lessons: {
                    orderBy: { position: 'asc' },
                    include: {
                      userWatchedLessons: {
                        where: { userId: user.id },
                        select: {
                          isCompleted: true,
                          currentTime: true,
                          duration: true,
                        }
                      }
                    }
                  }
                }
              }
            },
            orderBy: { module: { position: 'asc' } }
          }
        }
      })

      console.log('🔍 Course API - Course found:', course ? { id: course.id, name: course.name, organizationId: course.organizationId } : 'NOT FOUND')

      if (!course) {
        console.log('❌ Course API - Course not found:', { courseId, organizationId: organization.id })

        // Let's also check if the course exists at all
        const anyCourse = await db.courses.findFirst({
          where: { id: courseId },
          select: { id: true, name: true, organizationId: true }
        })

        if (anyCourse) {
          console.log('🔍 Course API - Course exists but in different organization:', {
            courseId,
            courseOrganizationId: anyCourse.organizationId,
            requestedOrganizationId: organization.id
          })
        } else {
          console.log('🔍 Course API - Course does not exist at all')
        }

        return c.json({ error: 'Course not found' }, 404)
      }

      // Check if user has access to the course
      if (user.role !== 'admin') {
        // Check if user is the creator of the course
        const isCreator = course.createdBy === user.id;

        if (!isCreator) {
          // If not creator, check if user is enrolled in the course
          const userCourseAccess = await db.userCourses.findFirst({
            where: {
              userId: user.id,
              courseId: courseId,
            },
          })

          console.log('🔍 Course API - User course access:', userCourseAccess ? 'FOUND' : 'NOT FOUND')

          if (!userCourseAccess) {
            console.log('❌ Course API - User does not have access to this course')
            return c.json({ error: 'Access denied - You are not enrolled in this course' }, 403)
          }
        } else {
          console.log('✅ Course API - User is the creator of this course')
        }
      }

      // Format course data
      const formattedCourse = {
        id: course.id,
        name: course.name,
        logo: course.logo,
        community: course.community,
        link: course.link,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        organization: course.organization,
        modules: course.courseModules.map(cm => ({
          id: cm.module.id,
          name: cm.module.name,
          position: cm.module.position,
          cover: cm.module.cover,
          courseId: course.id,
          lessons: cm.module.lessons.map(lesson => ({
            id: lesson.id,
            name: lesson.name,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            position: lesson.position,
            moduleId: cm.module.id, // Use the module ID from the course module relationship
            thumbnail: lesson.thumbnail,
            duration: lesson.duration,
            externalLink: lesson.externalLink,
            userWatchedLessons: lesson.userWatchedLessons[0] ? {
              isCompleted: lesson.userWatchedLessons[0].isCompleted,
              currentTime: lesson.userWatchedLessons[0].currentTime,
              duration: lesson.userWatchedLessons[0].duration,
            } : undefined,
          }))
        }))
      }

      console.log('✅ Course API - Course data formatted successfully')
      return c.json({ course: formattedCourse })
    } catch (error) {
      console.error('❌ Course API - Error fetching course:', error)
      return c.json({ error: 'Failed to fetch course' }, 500)
    }
  })
  .get('*', async (c) => {
    console.log('🔍 Course API - Catch-all route hit for path:', c.req.path)
    return c.json({ error: 'Route not found', path: c.req.path }, 404)
  })
