import { type Session, auth } from "@repo/auth";
import { createMiddleware } from "hono/factory";

export const authMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>(async (c, next) => {
	console.log("🔍 Auth Middleware - Starting authentication check");
	console.log("🔍 Auth Middleware - URL:", c.req.url);
	console.log("🔍 Auth Middleware - Method:", c.req.method);

	const headers = Object.fromEntries(c.req.raw.headers.entries());
	console.log("🔍 Auth Middleware - Headers:", {
		cookie: headers.cookie,
		authorization: headers.authorization,
		'user-agent': headers['user-agent'],
		origin: headers.origin,
		referer: headers.referer
	});

	try {
		const session = await auth.api.getSession({
			headers: c.req.raw.headers,
		});

		console.log("🔍 Auth Middleware - Session extraction result:", {
			hasSession: !!session,
			userId: session?.user?.id,
			userEmail: session?.user?.email,
			sessionId: session?.session?.id,
			sessionToken: session?.session?.token ? "***" + session.session.token.slice(-4) : null
		});

		if (!session) {
			console.log("❌ Auth Middleware - No session found, returning 401");
			return c.json({ error: "Unauthorized" }, 401);
		}

		if (!session.user || !session.session) {
			console.log("❌ Auth Middleware - Invalid session data structure");
			return c.json({ error: "Invalid session" }, 401);
		}

		c.set("session", session.session);
		c.set("user", session.user);

		console.log("✅ Auth Middleware - User authenticated successfully:", {
			email: session.user.email,
			id: session.user.id,
			name: session.user.name
		});

		await next();
	} catch (error) {
		console.error("❌ Auth Middleware - Error during session extraction:", error);
		return c.json({ error: "Authentication error" }, 500);
	}
});
