import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface NewUserProps {
  name?: string;
  url: string;
}

export const NewUser = ({ name, url }: NewUserProps) => (
  <Wrapper>
    <Preview>Bem-vindo ao Cakto Members!</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Bem-vindo ao Cakto Members!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        Obrigado por se cadastrar no Cakto Members!<br />
        Para começar a usar nossa plataforma, confirme seu email clicando no botão abaixo.
      </Text>
      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#556cd6",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Confirmar Email
        </Link>
      </Section>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Estamos ansiosos para ver você aproveitando nossos cursos e conteúdos!
      </Text>
    </Section>
  </Wrapper>
);

export default NewUser;
