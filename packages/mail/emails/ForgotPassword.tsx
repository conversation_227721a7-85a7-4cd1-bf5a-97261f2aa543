import React from "react";
import { getEmailComponents } from "../src/util/email-components";
import { Wrapper } from "./Wrapper";

interface ForgotPasswordProps {
  name?: string;
  url: string;
}

export const ForgotPassword = async ({ name, url }: ForgotPasswordProps) => {
  const { Preview, Heading, Text, Link, Section } = await getEmailComponents();

  return (
    <Wrapper>
      <Preview>Redefinir sua senha | Cakto Members</Preview>
      <Section style={{ textAlign: "center" }}>
        <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
          Redefinir sua senha
        </Heading>
        <Text style={{ fontSize: 16, margin: "16px 0" }}>
          Olá{ name ? ` ${name}` : "" },<br />
          Recebemos uma solicitação para redefinir sua senha.<br />
          Clique no botão abaixo para criar uma nova senha segura.
        </Text>
        <Section style={{ margin: "32px 0" }}>
          <Link
            href={url}
            style={{
              display: "inline-block",
              background: "#556cd6",
              color: "#fff",
              padding: "12px 32px",
              borderRadius: 6,
              fontWeight: 600,
              textDecoration: "none",
              fontSize: 16,
            }}
          >
            Redefinir Senha
          </Link>
        </Section>
        <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
          Se você não solicitou esta alteração, ignore este email.<br />
          Sua senha permanecerá inalterada.
        </Text>
      </Section>
    </Wrapper>
  );
};

export default ForgotPassword;
