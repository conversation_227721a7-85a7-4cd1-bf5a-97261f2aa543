import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";
import { Logo } from "../src/components/Logo";

interface WelcomeMagicLinkProps {
  name: string;
  email: string;
  url: string;
  productName?: string;
  organizationName?: string;
}

export const WelcomeMagicLink = ({
  name,
  email,
  url,
  productName,
  organizationName
}: WelcomeMagicLinkProps) => (
  <Wrapper>
    <Preview>Acesso liberado | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Logo />

      <Heading style={{ fontSize: 24, margin: "24px 0 8px", color: "#1f2937" }}>
         Olá {name},<br />
      </Heading>

      <Text style={{ fontSize: 16, margin: "16px 0", color: "#374151" }}>
        Sua conta foi criada com sucesso e você já tem acesso à nossa plataforma!
      </Text>

      {productName && (
        <Text style={{ fontSize: 16, margin: "16px 0", color: "#374151" }}>
          <strong>Produto:</strong> {productName}
        </Text>
      )}



      <Text style={{ fontSize: 16, margin: "24px 0", color: "#374151" }}>
        Clique no botão abaixo para acessar sua conta e começar a usar a plataforma:
      </Text>

      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#10b981",
            color: "#ffffff",
            padding: "16px 40px",
            borderRadius: "8px",
            fontWeight: "600",
            textDecoration: "none",
            fontSize: "16px",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
          }}
        >
          Acessar Minha Conta
        </Link>
      </Section>

      <Text style={{ color: "#6b7280", fontSize: 14, margin: "24px 0 0" }}>
         🔒 Este link é válido por 24 horas e pode ser usado apenas uma vez.<br />
        Se você não solicitou este acesso, ignore este email.
      </Text>

      <Text style={{ color: "#6b7280", fontSize: 14, margin: "16px 0 0" }}>
        Se tiver dúvidas, conte com nosso suporte em <strong><EMAIL></strong>
      </Text>
    </Section>
  </Wrapper>
);

export default WelcomeMagicLink;
