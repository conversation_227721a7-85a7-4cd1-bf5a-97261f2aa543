import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface UserCreatedProps {
  name?: string;
  email: string;
  password?: string;
  url: string;
}

export const UserCreated = ({
  name,
  email,
  password,
  url
}: UserCreatedProps) => (
  <Wrapper>
    <Preview>Sua conta foi criada | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Sua conta foi criada com sucesso!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        Sua conta no Cakto Members foi criada com sucesso!<br />
        Use os dados abaixo para fazer seu primeiro login.
      </Text>

      <Section style={{
        background: "#f8f9fa",
        padding: "16px",
        borderRadius: "6px",
        margin: "24px 0",
        textAlign: "left"
      }}>
        <Text style={{ margin: "8px 0", fontSize: 14 }}>
          <strong>Email:</strong> {email}
        </Text>
        {password && (
          <Text style={{ margin: "8px 0", fontSize: 14 }}>
            <strong>Senha:</strong> {password}
          </Text>
        )}
      </Section>

      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#556cd6",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Fazer Login
        </Link>
      </Section>

      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        <strong>⚠️ Importante:</strong> Por segurança, recomendamos que você altere sua senha no primeiro acesso.
      </Text>
    </Section>
  </Wrapper>
);

export default UserCreated;
