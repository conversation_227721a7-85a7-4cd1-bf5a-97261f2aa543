import React from "react";
import { Preview, Heading, Text, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface NewsletterSignupProps {
  name?: string;
}

export const NewsletterSignup = ({ name }: NewsletterSignupProps) => (
  <Wrapper>
    <Preview>Bem-vindo à nossa newsletter | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Bem-vindo à nossa newsletter!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        O<PERSON><PERSON>{ name ? ` ${name}` : "" },<br />
        Obrigado por se inscrever na newsletter do Cakto Members!<br />
        Você receberá as últimas novidades, dicas e atualizações sobre nossos cursos.
      </Text>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Fique atento aos nossos próximos emails com conteúdo exclusivo!
      </Text>
    </Section>
  </Wrapper>
);

export default NewsletterSignup;
