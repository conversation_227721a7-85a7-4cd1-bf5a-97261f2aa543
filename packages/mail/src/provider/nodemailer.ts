import { config } from "@repo/config";
import nodemailer from "nodemailer";
import type { Send<PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../types";

const { from } = config.mails;

export const send: SendEmailHandler = async ({ to, subject, text, html }) => {
	const transporter = nodemailer.createTransport({
		host: process.env.MAIL_HOST as string,
		port: Number.parseInt(process.env.MAIL_PORT as string, 10),
		secure: false, // Mailgun usa STARTTLS na porta 587
		auth: {
			user: process.env.MAIL_USER as string,
			pass: process.env.MAIL_PASS as string,
		},
		// Configurações adicionais para Mailgun
		tls: {
			rejectUnauthorized: false, // Para desenvolvimento
		},
	});

	await transporter.sendMail({
		to,
		from,
		subject,
		text,
		html,
	});
};
