// Utilitário para importar componentes do React Email dinamicamente
// Isso evita que os componentes sejam incluídos no bundle do cliente

export async function getEmailComponents() {
	const {
		Container,
		Font,
		Head,
		Html,
		Section,
		Tailwind,
		Preview,
		Heading,
		Text,
		Link,
		Body,
		Img,
		Button,
	} = await import("@react-email/components");

	return {
		Container,
		Font,
		Head,
		Html,
		Section,
		Tailwind,
		Preview,
		Heading,
		Text,
		Link,
		Body,
		Img,
		Button,
	};
}

export async function getEmailRender() {
	const { render } = await import("@react-email/render");
	return render;
}
